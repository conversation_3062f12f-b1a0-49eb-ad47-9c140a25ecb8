import api from './api';

/**
 * Service for handling issue-related API calls
 */
const issueService = {
  /**
   * Upload issue data to the backend server
   * @param {Object} issueData - The issue data to upload
   * @param {string} issueData.title - The issue title
   * @param {string} issueData.category - The issue category
   * @param {string} issueData.description - The issue description
   * @param {string} issueData.location - The issue location
   * @param {Object} [issueData.coordinates] - The issue coordinates (optional)
   * @param {number} issueData.coordinates.lat - The latitude
   * @param {number} issueData.coordinates.lng - The longitude
   * @param {File} issueData.photoFile - The photo file
   * @returns {Promise<Object>} The response from the server
   */
  async uploadIssue(issueData) {
    try {
      console.log('Starting issue upload with data:', {
        title: issueData.title,
        category: issueData.category,
        description: issueData.description.substring(0, 30) + '...',
        location: issueData.location,
        hasCoordinates: !!issueData.coordinates,
        hasPhoto: !!issueData.photoFile
      });

      // Create form data for file upload
      const formData = new FormData();
      formData.append('title', issueData.title);
      formData.append('category', issueData.category);
      formData.append('description', issueData.description);
      formData.append('location', issueData.location);

      // Add coordinates if available
      if (issueData.coordinates) {
        formData.append('latitude', issueData.coordinates.lat);
        formData.append('longitude', issueData.coordinates.lng);
      }

      // Add photo if available
      if (issueData.photoFile) {
        formData.append('photo', issueData.photoFile);
        console.log('Added photo to form data:', issueData.photoFile.name, issueData.photoFile.size);
      } else {
        console.log('No photo provided');
      }

      // Check if token exists
      const token = localStorage.getItem('token');
      console.log('Token exists:', !!token);

      if (!token) {
        console.error('No authentication token found');
        throw new Error('Authentication token not found. Please log in again.');
      }

      // Send API request
      console.log('Sending API request to /issues');
      const response = await api.post('/issues', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Issue upload successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error uploading issue:', error);

      // Log more detailed error information
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        console.error('Response headers:', error.response.headers);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      throw error;
    }
  },

  /**
   * Get all issues
   * @param {Object} [filters] - Optional filters
   * @param {string} [filters.category] - Filter by category
   * @param {string} [filters.status] - Filter by status
   * @param {string} [filters.user] - Filter by user ID
   * @returns {Promise<Object>} The response from the server
   */
  async getIssues(filters = {}) {
    try {
      // Build query string from filters
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      const queryString = queryParams.toString();
      const url = queryString ? `/issues?${queryString}` : '/issues';

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching issues:', error);
      throw error;
    }
  },

  /**
   * Get a specific issue by ID
   * @param {string} issueId - The ID of the issue to fetch
   * @returns {Promise<Object>} The response from the server
   */
  async getIssueById(issueId) {
    try {
      const response = await api.get(`/issues/${issueId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching issue ${issueId}:`, error);
      throw error;
    }
  },

  /**
   * Update an issue's status
   * @param {string} issueId - The ID of the issue to update
   * @param {string} status - The new status
   * @param {string} [comment] - Optional comment about the status update
   * @returns {Promise<Object>} The response from the server
   */
  async updateIssueStatus(issueId, status, comment = '') {
    try {
      const response = await api.put(`/issues/${issueId}/status`, {
        status,
        comment
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating issue ${issueId} status:`, error);
      throw error;
    }
  },

  /**
   * Delete an issue
   * @param {string} issueId - The ID of the issue to delete
   * @returns {Promise<Object>} The response from the server
   */
  async deleteIssue(issueId) {
    try {
      const response = await api.delete(`/issues/${issueId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting issue ${issueId}:`, error);
      throw error;
    }
  }
};

export default issueService;
