const mongoose = require('mongoose');

const CourseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: ['sustainability', 'waste', 'energy', 'water', 'food', 'transport', 'construction']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  instructor: {
    type: String,
    required: [true, 'Please add an instructor']
  },
  duration: {
    type: String,
    required: [true, 'Please add a duration']
  },
  image: {
    type: String
  },
  lessons: [
    {
      title: {
        type: String,
        required: true
      },
      description: {
        type: String
      },
      content: {
        type: String
      },
      duration: {
        type: String
      },
      resources: [
        {
          title: {
            type: String
          },
          url: {
            type: String
          }
        }
      ]
    }
  ],
  enrollments: [
    {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      progress: {
        type: Number,
        default: 0
      },
      completedLessons: [
        {
          lessonId: {
            type: mongoose.Schema.Types.ObjectId
          },
          completedAt: {
            type: Date,
            default: Date.now
          }
        }
      ],
      enrolledAt: {
        type: Date,
        default: Date.now
      },
      lastAccessed: {
        type: Date,
        default: Date.now
      }
    }
  ],
  ratings: [
    {
      rating: {
        type: Number,
        min: 1,
        max: 5,
        required: true
      },
      review: {
        type: String
      },
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Virtual for lesson count
CourseSchema.virtual('lessonCount').get(function() {
  return this.lessons.length;
});

// Virtual for enrollment count
CourseSchema.virtual('enrollmentCount').get(function() {
  return this.enrollments.length;
});

// Virtual for average rating
CourseSchema.virtual('averageRating').get(function() {
  if (this.ratings.length === 0) {
    return 0;
  }
  
  const sum = this.ratings.reduce((total, rating) => total + rating.rating, 0);
  return (sum / this.ratings.length).toFixed(1);
});

// Enable virtuals
CourseSchema.set('toJSON', { virtuals: true });
CourseSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Course', CourseSchema);
