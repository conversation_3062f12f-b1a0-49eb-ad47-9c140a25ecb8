import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, Form, Button, Alert, <PERSON>, Badge, Mo<PERSON>, Spin<PERSON>, To<PERSON>, ToastContainer } from 'react-bootstrap';
import { FaExclamationTriangle, FaCamera, FaMapMarkerAlt, FaCheck, FaExclamation, FaBell } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import MapComponent from '../components/map/MapComponent';
import api from '../utils/api';
import issueService from '../utils/issueService';
import { useAuth } from '../contexts/AuthContext';
import socketService from '../services/socketService';
import notificationService from '../services/notificationService';

const IssueReportingPage = () => {
  const { currentUser } = useAuth();
  const [issueTitle, setIssueTitle] = useState('');
  const [issueCategory, setIssueCategory] = useState('');
  const [issueDescription, setIssueDescription] = useState('');
  const [issueLocation, setIssueLocation] = useState('');
  const [issuePhoto, setIssuePhoto] = useState(null);
  const [photoFile, setPhotoFile] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [mapCenter, setMapCenter] = useState([51.505, -0.09]); // Default to London
  const [issueCoordinates, setIssueCoordinates] = useState(null);
  const [userIssues, setUserIssues] = useState([]);
  const [loadingIssues, setLoadingIssues] = useState(true);
  const [validationErrors, setValidationErrors] = useState({});

  // State for status notifications
  const [showStatusToast, setShowStatusToast] = useState(false);
  const [statusNotification, setStatusNotification] = useState(null);

  // Set up socket connection and event listeners
  useEffect(() => {
    if (!currentUser) return;

    // Connect to socket if not already connected
    if (!socketService.isConnected) {
      socketService.connect();
    }

    // Join user's room to receive updates about their issues
    socketService.joinUserRoom(currentUser.id);

    // Listen for issue updates
    const unsubscribe = socketService.on('issue-update', handleIssueStatusUpdate);

    // Clean up when component unmounts
    return () => {
      unsubscribe();
    };
  }, [currentUser]);

  // Handle issue status updates from socket
  const handleIssueStatusUpdate = (data) => {
    console.log('Received issue update:', data);

    // Update the issue in the local state
    setUserIssues(prevIssues =>
      prevIssues.map(issue =>
        issue.id === data.issueId || issue._id === data.issueId
          ? { ...issue, status: data.status }
          : issue
      )
    );

    // Find the issue that was updated
    const updatedIssue = userIssues.find(issue =>
      issue.id === data.issueId || issue._id === data.issueId
    );

    if (updatedIssue) {
      // Set status notification
      setStatusNotification({
        title: updatedIssue.title,
        oldStatus: updatedIssue.status,
        newStatus: data.status,
        comment: data.comment,
        timestamp: data.timestamp
      });

      // Show toast notification
      setShowStatusToast(true);

      // Show browser notification
      notificationService.showIssueUpdate({
        id: data.issueId,
        title: updatedIssue.title,
        status: data.status
      });
    }
  };

  // Fetch user's reported issues
  useEffect(() => {
    const fetchUserIssues = async () => {
      try {
        setLoadingIssues(true);
        // Use the issueService to fetch user issues
        const result = await issueService.getIssues({ user: currentUser?.id || '' });
        setUserIssues(result.data || []);

        // Join issue rooms for each issue to receive updates
        if (result.data && result.data.length > 0) {
          result.data.forEach(issue => {
            socketService.joinIssueRoom(issue.id || issue._id);
          });
        }
      } catch (err) {
        console.error('Error fetching issues:', err);
        // Use mock data if API fails
        const mockIssues = [
          { id: 1, title: 'Pothole on Main Street', category: 'Roads', status: 'Open', date: '2023-04-15', location: '123 Main St' },
          { id: 2, title: 'Broken Streetlight', category: 'Lighting', status: 'In Progress', date: '2023-04-10', location: 'Corner of Oak and Pine' },
          { id: 3, title: 'Overflowing Trash Bin', category: 'Waste', status: 'Resolved', date: '2023-04-05', location: 'City Park' }
        ];

        setUserIssues(mockIssues);

        // Join issue rooms for mock issues
        mockIssues.forEach(issue => {
          socketService.joinIssueRoom(issue.id);
        });
      } finally {
        setLoadingIssues(false);
      }
    };

    if (currentUser) {
      fetchUserIssues();
    }
  }, [currentUser]);

  // Validate form fields
  const validateForm = () => {
    const errors = {};

    if (!issueTitle.trim()) {
      errors.title = 'Title is required';
    } else if (issueTitle.length > 100) {
      errors.title = 'Title must be less than 100 characters';
    }

    if (!issueCategory) {
      errors.category = 'Category is required';
    }

    if (!issueDescription.trim()) {
      errors.description = 'Description is required';
    } else if (issueDescription.length > 500) {
      errors.description = 'Description must be less than 500 characters';
    }

    if (!issueLocation.trim()) {
      errors.location = 'Location is required';
    }

    // Photo is optional, but validate size if provided
    if (photoFile && photoFile.size > 5 * 1024 * 1024) {
      errors.photo = 'Photo must be less than 5MB';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  /**
   * Upload issue data to the backend server
   * @param {Object} issueData - The issue data to upload
   * @param {string} issueData.title - The issue title
   * @param {string} issueData.category - The issue category
   * @param {string} issueData.description - The issue description
   * @param {string} issueData.location - The issue location
   * @param {Object} [issueData.coordinates] - The issue coordinates (optional)
   * @param {number} issueData.coordinates.lat - The latitude
   * @param {number} issueData.coordinates.lng - The longitude
   * @param {File} issueData.photoFile - The photo file
   * @returns {Promise<Object>} The response from the server
   */
  const uploadIssueData = async (issueData) => {
    try {
      // Create form data for file upload
      const formData = new FormData();
      formData.append('title', issueData.title);
      formData.append('category', issueData.category);
      formData.append('description', issueData.description);
      formData.append('location', issueData.location);

      // Add coordinates if available
      if (issueData.coordinates) {
        formData.append('latitude', issueData.coordinates.lat);
        formData.append('longitude', issueData.coordinates.lng);
      }

      // Add photo if available
      if (issueData.photoFile) {
        formData.append('photo', issueData.photoFile);
      }

      // Log the form data for debugging
      console.log('Uploading issue data:', {
        title: issueData.title,
        category: issueData.category,
        description: issueData.description.substring(0, 50) + '...',
        location: issueData.location,
        hasCoordinates: !!issueData.coordinates,
        hasPhoto: !!issueData.photoFile
      });

      // Send API request
      const response = await api.post('/issues', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      console.log('Upload successful:', response.data);
      return response;
    } catch (error) {
      console.error('Error uploading issue data:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      setError('Please correct the errors in the form');
      return;
    }

    setError('');
    setSuccess('');
    setLoading(true);

    try {
      console.log('Form validation passed, preparing to submit issue');

      // Prepare issue data
      const issueData = {
        title: issueTitle,
        category: issueCategory,
        description: issueDescription,
        location: issueLocation,
        coordinates: issueCoordinates,
        photoFile: photoFile
      };

      console.log('Issue data prepared:', {
        title: issueData.title,
        category: issueData.category,
        hasPhoto: !!issueData.photoFile,
        hasCoordinates: !!issueData.coordinates
      });

      // Check if user is authenticated
      const token = localStorage.getItem('token');
      const storedUser = localStorage.getItem('user');

      if (!currentUser && (!token || !storedUser)) {
        console.error('No current user found and no token/user in localStorage');
        setError('You must be logged in to submit an issue. Please log in and try again.');
        setLoading(false);
        return;
      }

      // Use stored user if currentUser is not available
      if (!currentUser && storedUser) {
        console.log('Using stored user from localStorage');
        try {
          const userData = JSON.parse(storedUser);
          console.log('Parsed user data:', userData);
        } catch (e) {
          console.error('Error parsing stored user data:', e);
        }
      }

      // Upload the issue data using the issueService
      console.log('Calling issueService.uploadIssue');
      const result = await issueService.uploadIssue(issueData);
      console.log('Issue upload successful:', result);

      // Update user issues list with the new issue
      setUserIssues(prevIssues => [result.data, ...prevIssues]);

      // Show success message
      setSuccess('Issue reported successfully! City officials have been notified.');

      // Reset form
      setIssueTitle('');
      setIssueCategory('');
      setIssueDescription('');
      setIssueLocation('');
      setIssuePhoto(null);
      setPhotoFile(null);
      setIssueCoordinates(null);

    } catch (err) {
      console.error('Error submitting issue:', err);

      // Provide more detailed error message
      let errorMessage = 'Failed to submit issue. Please try again.';

      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);

        if (err.response.status === 401) {
          errorMessage = 'Authentication error. Please log in again and try submitting.';
        } else if (err.response.data && err.response.data.error) {
          errorMessage = err.response.data.error;

          // If there are validation details, add them
          if (err.response.data.details && Array.isArray(err.response.data.details)) {
            errorMessage += ': ' + err.response.data.details.join(', ');
          }
        }
      } else if (err.request) {
        errorMessage = 'No response from server. Please check your internet connection.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handlePhotoChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file size
      if (file.size > 5 * 1024 * 1024) {
        setValidationErrors({...validationErrors, photo: 'Photo must be less than 5MB'});
        return;
      }

      // Validate file type
      if (!file.type.match(/image\/(jpeg|jpg|png|gif)/)) {
        setValidationErrors({...validationErrors, photo: 'Only image files are allowed'});
        return;
      }

      // Clear any previous errors
      const newErrors = {...validationErrors};
      delete newErrors.photo;
      setValidationErrors(newErrors);

      // Set the file and preview
      setPhotoFile(file);
      setIssuePhoto(URL.createObjectURL(file));
    }
  };

  const handleMapClick = (e) => {
    const { lat, lng } = e.latlng;
    setIssueCoordinates({ lat, lng });
    setIssueLocation(`Latitude: ${lat.toFixed(6)}, Longitude: ${lng.toFixed(6)}`);

    // Clear location validation error if it exists
    if (validationErrors.location) {
      const newErrors = {...validationErrors};
      delete newErrors.location;
      setValidationErrors(newErrors);
    }
  };

  const handleViewIssue = (issue) => {
    setSelectedIssue(issue);
    setShowModal(true);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Open':
        return <Badge bg="danger">Open</Badge>;
      case 'In Progress':
        return <Badge bg="warning">In Progress</Badge>;
      case 'Resolved':
        return <Badge bg="success">Resolved</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  return (
    <Container>
      {/* Status Update Toast Notification */}
      <ToastContainer
        position="top-end"
        className="p-3"
        style={{ zIndex: 1060 }}
      >
        <Toast
          show={showStatusToast}
          onClose={() => setShowStatusToast(false)}
          delay={6000}
          autohide
          bg={statusNotification?.newStatus === 'Resolved' ? 'success' :
              statusNotification?.newStatus === 'In Progress' ? 'warning' : 'info'}
          className="text-white"
        >
          <Toast.Header closeButton>
            <FaBell className="me-2" />
            <strong className="me-auto">Issue Status Update</strong>
            <small>
              {statusNotification?.timestamp ?
                new Date(statusNotification.timestamp).toLocaleTimeString() :
                'Just now'}
            </small>
          </Toast.Header>
          <Toast.Body>
            <div>
              <strong>{statusNotification?.title}</strong> status changed from{' '}
              <Badge bg={
                statusNotification?.oldStatus === 'Resolved' ? 'success' :
                statusNotification?.oldStatus === 'In Progress' ? 'warning' : 'danger'
              }>
                {statusNotification?.oldStatus}
              </Badge>{' '}
              to{' '}
              <Badge bg={
                statusNotification?.newStatus === 'Resolved' ? 'success' :
                statusNotification?.newStatus === 'In Progress' ? 'warning' : 'danger'
              }>
                {statusNotification?.newStatus}
              </Badge>
            </div>
            {statusNotification?.comment && (
              <div className="mt-2">
                <small>Comment: {statusNotification.comment}</small>
              </div>
            )}
          </Toast.Body>
        </Toast>
      </ToastContainer>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Urban Issue Reporting</h1>
          <p className="lead">
            Report issues like potholes, streetlights, trash problems, and more to help improve our city.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-danger bg-opacity-10 p-3 rounded">
            <FaExclamationTriangle className="text-danger fs-1" />
          </div>
        </Col>
      </Row>

      <Row>
        <Col lg={7} className="mb-4 mb-lg-0">
          <Card className="border-0 shadow-sm">
            <Card.Body className="p-4">
              <h4 className="mb-4">Report an Issue</h4>

              {error && (
                <Alert variant="danger" className="d-flex align-items-start">
                  <FaExclamation className="me-2 mt-1" />
                  <div>{error}</div>
                </Alert>
              )}
              {success && (
                <Alert variant="success" className="d-flex align-items-start">
                  <FaCheck className="me-2 mt-1" />
                  <div>
                    <strong>Success!</strong> {success}
                    <div className="mt-2">
                      <small>Your issue has been added to the map and will be visible to city officials.</small>
                    </div>
                  </div>
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3" controlId="issueTitle">
                  <Form.Label>Issue Title*</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="E.g., Pothole on Main Street"
                    value={issueTitle}
                    onChange={(e) => {
                      setIssueTitle(e.target.value);
                      if (validationErrors.title) {
                        const newErrors = {...validationErrors};
                        delete newErrors.title;
                        setValidationErrors(newErrors);
                      }
                    }}
                    isInvalid={!!validationErrors.title}
                    required
                  />
                  <Form.Control.Feedback type="invalid">
                    {validationErrors.title}
                  </Form.Control.Feedback>
                </Form.Group>

                <Form.Group className="mb-3" controlId="issueCategory">
                  <Form.Label>Category*</Form.Label>
                  <Form.Select
                    value={issueCategory}
                    onChange={(e) => {
                      setIssueCategory(e.target.value);
                      if (validationErrors.category) {
                        const newErrors = {...validationErrors};
                        delete newErrors.category;
                        setValidationErrors(newErrors);
                      }
                    }}
                    isInvalid={!!validationErrors.category}
                    required
                  >
                    <option value="">Select a category</option>
                    <option value="Roads">Roads</option>
                    <option value="Lighting">Lighting</option>
                    <option value="Waste">Waste</option>
                    <option value="Parks">Parks</option>
                    <option value="Graffiti">Graffiti</option>
                    <option value="Public Transport">Public Transport</option>
                    <option value="Other">Other</option>
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {validationErrors.category}
                  </Form.Control.Feedback>
                </Form.Group>

                <Form.Group className="mb-3" controlId="issueDescription">
                  <Form.Label>Description*</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    placeholder="Please provide details about the issue..."
                    value={issueDescription}
                    onChange={(e) => {
                      setIssueDescription(e.target.value);
                      if (validationErrors.description) {
                        const newErrors = {...validationErrors};
                        delete newErrors.description;
                        setValidationErrors(newErrors);
                      }
                    }}
                    isInvalid={!!validationErrors.description}
                    required
                  />
                  <Form.Control.Feedback type="invalid">
                    {validationErrors.description}
                  </Form.Control.Feedback>
                </Form.Group>

                <Form.Group className="mb-3" controlId="issueLocation">
                  <Form.Label>Location*</Form.Label>
                  <div className="input-group mb-3">
                    <span className="input-group-text">
                      <FaMapMarkerAlt />
                    </span>
                    <Form.Control
                      type="text"
                      placeholder="E.g., 123 Main Street or intersection of Oak and Pine"
                      value={issueLocation}
                      onChange={(e) => {
                        setIssueLocation(e.target.value);
                        if (validationErrors.location) {
                          const newErrors = {...validationErrors};
                          delete newErrors.location;
                          setValidationErrors(newErrors);
                        }
                      }}
                      isInvalid={!!validationErrors.location}
                      required
                    />
                    <Form.Control.Feedback type="invalid">
                      {validationErrors.location}
                    </Form.Control.Feedback>
                  </div>
                  <div className="mb-2">
                    <small className="text-muted">Click on the map to set the exact location of the issue</small>
                  </div>
                  <div style={{ height: '300px', width: '100%', marginBottom: '10px' }}>
                    <MapComponent
                      height="300px"
                      onMapClick={handleMapClick}
                      markers={issueCoordinates ? [{
                        lat: issueCoordinates.lat,
                        lng: issueCoordinates.lng,
                        popup: "Issue location"
                      }] : []}
                    />
                  </div>
                </Form.Group>

                <Form.Group className="mb-4" controlId="issuePhoto">
                  <Form.Label>Photo <span className="text-muted">(Optional)</span></Form.Label>
                  <div className="d-flex align-items-center">
                    <Button
                      variant={validationErrors.photo ? 'danger' : 'outline-secondary'}
                      className="position-relative"
                      onClick={() => document.getElementById('photoInput').click()}
                    >
                      <FaCamera className="me-2" />
                      {issuePhoto ? 'Change Photo' : 'Upload Photo'}
                      <Form.Control
                        id="photoInput"
                        type="file"
                        accept="image/*"
                        className="position-absolute top-0 start-0 opacity-0 w-100 h-100"
                        onChange={handlePhotoChange}
                      />
                    </Button>
                    {issuePhoto && (
                      <div className="ms-3">
                        <img
                          src={issuePhoto}
                          alt="Issue preview"
                          style={{ height: '60px', width: '60px', objectFit: 'cover' }}
                          className="rounded shadow-sm"
                        />
                      </div>
                    )}
                  </div>
                  {validationErrors.photo && (
                    <div className="text-danger small mt-1">
                      {validationErrors.photo}
                    </div>
                  )}
                  <Form.Text className={validationErrors.photo ? 'text-danger' : 'text-muted'}>
                    Adding a photo helps city officials better understand and address the issue.
                  </Form.Text>
                </Form.Group>

                <div className="d-grid">
                  <Button
                    variant="primary"
                    type="submit"
                    disabled={loading}
                    size="lg"
                    className="d-flex justify-content-center align-items-center"
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        <span>Submitting...</span>
                      </>
                    ) : (
                      <>
                        <FaExclamationTriangle className="me-2" />
                        <span>Submit Report</span>
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={5}>
          <Card className="border-0 shadow-sm mb-4">
            <Card.Body className="p-4">
              <h4 className="mb-3">How It Works</h4>
              <ol className="ps-3">
                <li className="mb-2">Fill out the issue reporting form with details about the problem.</li>
                <li className="mb-2">Add a photo to help officials understand the issue.</li>
                <li className="mb-2">Submit your report to notify city officials.</li>
                <li className="mb-2">Track the status of your reported issues in your dashboard.</li>
                <li className="mb-2">Receive updates as the city addresses the problem.</li>
                <li>
                  <Link to="/issues-map" className="text-decoration-none">
                    View all reported issues on the city map
                    <FaMapMarkerAlt className="ms-1" />
                  </Link>
                </li>
              </ol>
            </Card.Body>
          </Card>

          <Card className="border-0 shadow-sm">
            <Card.Body className="p-4">
              <h4 className="mb-3">Your Reported Issues</h4>

              {loadingIssues ? (
                <div className="text-center py-4">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-2 text-muted">Loading your issues...</p>
                </div>
              ) : userIssues.length > 0 ? (
                <div className="table-responsive">
                  <Table hover className="mb-0">
                    <thead>
                      <tr>
                        <th>Issue</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {userIssues.map((issue) => (
                        <tr key={issue._id || issue.id}>
                          <td>{issue.title}</td>
                          <td>{getStatusBadge(issue.status)}</td>
                          <td>{new Date(issue.createdAt || issue.date).toLocaleDateString()}</td>
                          <td>
                            <Button
                              variant="link"
                              className="p-0 text-decoration-none"
                              onClick={() => handleViewIssue(issue)}
                            >
                              View
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-4">
                  <FaExclamation className="text-muted mb-2" size={24} />
                  <p className="text-muted mb-0">You haven't reported any issues yet.</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Issue Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Issue Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedIssue && (
            <>
              <h5>{selectedIssue.title}</h5>
              <p className="mb-2">
                <strong>Category:</strong> {selectedIssue.category}
              </p>
              <p className="mb-2">
                <strong>Status:</strong> {getStatusBadge(selectedIssue.status)}
              </p>
              <p className="mb-2">
                <strong>Date Reported:</strong> {selectedIssue.date}
              </p>
              <p className="mb-2">
                <strong>Location:</strong> {selectedIssue.location}
              </p>
              <p className="mb-4">
                <strong>Description:</strong> Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris.
              </p>

              <h6>Status Updates</h6>
              <ul className="list-unstyled">
                <li className="mb-2">
                  <small className="text-muted">
                    {new Date(selectedIssue.createdAt || selectedIssue.date).toLocaleString()}
                  </small>
                  <div className="d-flex align-items-center">
                    <Badge bg="danger" className="me-2">Open</Badge>
                    <p className="mb-0">Issue reported</p>
                  </div>
                </li>

                {/* Show real status updates if available */}
                {selectedIssue.statusUpdates && selectedIssue.statusUpdates.length > 0 ? (
                  selectedIssue.statusUpdates.map((update, index) => (
                    <li key={index} className="mb-2">
                      <small className="text-muted">
                        {new Date(update.updatedAt).toLocaleString()}
                      </small>
                      <div className="d-flex align-items-center">
                        <Badge
                          bg={
                            update.status === 'Resolved' ? 'success' :
                            update.status === 'In Progress' ? 'warning' : 'danger'
                          }
                          className="me-2"
                        >
                          {update.status}
                        </Badge>
                        <p className="mb-0">
                          {update.comment || `Status changed to ${update.status}`}
                        </p>
                      </div>
                    </li>
                  ))
                ) : (
                  // Show mock status updates if no real updates are available
                  <>
                    {selectedIssue.status !== 'Open' && (
                      <li className="mb-2">
                        <small className="text-muted">
                          {new Date(
                            new Date(selectedIssue.createdAt || selectedIssue.date).getTime() + 86400000
                          ).toLocaleString()}
                        </small>
                        <div className="d-flex align-items-center">
                          <Badge bg="warning" className="me-2">In Progress</Badge>
                          <p className="mb-0">Issue assigned to maintenance team</p>
                        </div>
                      </li>
                    )}

                    {selectedIssue.status === 'Resolved' && (
                      <li>
                        <small className="text-muted">
                          {new Date(
                            new Date(selectedIssue.createdAt || selectedIssue.date).getTime() + 172800000
                          ).toLocaleString()}
                        </small>
                        <div className="d-flex align-items-center">
                          <Badge bg="success" className="me-2">Resolved</Badge>
                          <p className="mb-0">Issue resolved by maintenance team</p>
                        </div>
                      </li>
                    )}
                  </>
                )}
              </ul>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default IssueReportingPage;
