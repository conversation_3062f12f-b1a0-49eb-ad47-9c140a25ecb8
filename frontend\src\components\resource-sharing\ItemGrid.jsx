import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import ItemCard from './ItemCard';

/**
 * ItemGrid component displays a grid of items
 * @param {Object} props
 * @param {Array<import('../../types/item').Item>} props.items - The items to display
 */
const ItemGrid = ({ items }) => {
  if (items.length === 0) {
    return (
      <Card className="border-0 shadow-sm">
        <Card.Body className="p-5 text-center">
          <p className="mb-3 text-muted">No resources found matching your criteria.</p>
          <Button
            variant="outline-primary"
            onClick={() => {
              // This would be handled by the parent component
            }}
          >
            Clear Filters
          </Button>
        </Card.Body>
      </Card>
    );
  }

  return (
    <Row xs={1} md={2} lg={3} className="g-4">
      {items.map(item => (
        <Col key={item.id}>
          <ItemCard item={item} />
        </Col>
      ))}
    </Row>
  );
};

export default ItemGrid;
