// This script helps ensure the authentication state is properly recognized

// Function to check if user is logged in
function checkAuthState() {
  const token = localStorage.getItem('token');
  const userDataString = localStorage.getItem('user');
  
  if (token && userDataString) {
    try {
      // Parse user data
      const userData = JSON.parse(userDataString);
      console.log('Auth refresh: User is logged in as', userData.name || 'User');
      return true;
    } catch (error) {
      console.error('Auth refresh: Error parsing user data', error);
      return false;
    }
  }
  
  console.log('Auth refresh: User is not logged in');
  return false;
}

// Function to refresh the page if needed
function refreshIfNeeded() {
  const isLoggedIn = checkAuthState();
  const currentPath = window.location.pathname;
  
  // If we're on the home page and logged in, we might need to refresh to show the correct navbar
  if (currentPath === '/' && isLoggedIn) {
    // Check if the navbar shows login/signup buttons when it should show profile
    const loginButton = document.querySelector('a[href="/public/direct-login.html"]');
    const profileDropdown = document.getElementById('profile-dropdown');
    
    if (loginButton && !profileDropdown) {
      console.log('Auth refresh: Navbar needs updating, refreshing page...');
      // Force a refresh of the page to update the navbar
      window.location.reload();
    } else {
      console.log('Auth refresh: Navbar appears to be correct');
    }
  }
}

// Run the check after the page has loaded
window.addEventListener('load', function() {
  // Wait a moment for React to render the page
  setTimeout(refreshIfNeeded, 1000);
});
