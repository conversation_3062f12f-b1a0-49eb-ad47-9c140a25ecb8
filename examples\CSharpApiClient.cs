using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace UrbanPulse.ApiClient
{
    /// <summary>
    /// C# client for the Urban Pulse API
    /// </summary>
    public class UrbanPulseApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private string _authToken;

        public UrbanPulseApiClient(string baseUrl = "http://localhost:5002/api")
        {
            _baseUrl = baseUrl;
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// Set the authentication token for API requests
        /// </summary>
        public void SetAuthToken(string token)
        {
            _authToken = token;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        /// <summary>
        /// Login to the API and get an authentication token
        /// </summary>
        public async Task<LoginResponse> LoginAsync(string email, string password)
        {
            var loginData = new
            {
                email,
                password
            };

            var content = new StringContent(
                JsonSerializer.Serialize(loginData),
                Encoding.UTF8,
                "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/auth/login", content);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var loginResponse = JsonSerializer.Deserialize<LoginResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (loginResponse?.Success == true && !string.IsNullOrEmpty(loginResponse.Token))
            {
                SetAuthToken(loginResponse.Token);
            }

            return loginResponse;
        }

        /// <summary>
        /// Get a list of all issues
        /// </summary>
        public async Task<IssueListResponse> GetIssuesAsync()
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/issues");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<IssueListResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        /// <summary>
        /// Get a specific issue by ID
        /// </summary>
        public async Task<IssueResponse> GetIssueAsync(string issueId)
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/issues/{issueId}");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<IssueResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        /// <summary>
        /// Create a new issue
        /// </summary>
        public async Task<IssueResponse> CreateIssueAsync(IssueCreateRequest issue)
        {
            // For multipart form data with image upload
            using var formData = new MultipartFormDataContent();
            
            // Add text fields
            formData.Add(new StringContent(issue.Title), "title");
            formData.Add(new StringContent(issue.Category), "category");
            formData.Add(new StringContent(issue.Description), "description");
            formData.Add(new StringContent(issue.Location), "location");
            
            // Add coordinates if available
            if (issue.Latitude.HasValue && issue.Longitude.HasValue)
            {
                formData.Add(new StringContent(issue.Latitude.Value.ToString()), "latitude");
                formData.Add(new StringContent(issue.Longitude.Value.ToString()), "longitude");
            }
            
            // Add photo if available
            if (issue.PhotoBytes != null && issue.PhotoBytes.Length > 0)
            {
                var photoContent = new ByteArrayContent(issue.PhotoBytes);
                photoContent.Headers.ContentType = new MediaTypeHeaderValue("image/jpeg");
                formData.Add(photoContent, "photo", issue.PhotoFilename ?? "issue.jpg");
            }
            
            var response = await _httpClient.PostAsync($"{_baseUrl}/issues", formData);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<IssueResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
    }

    #region Models

    public class LoginResponse
    {
        public bool Success { get; set; }
        public string Token { get; set; }
        public UserData Data { get; set; }
    }

    public class UserData
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public bool IsAdmin { get; set; }
    }

    public class IssueListResponse
    {
        public bool Success { get; set; }
        public int Count { get; set; }
        public Pagination Pagination { get; set; }
        public List<Issue> Data { get; set; }
    }

    public class IssueResponse
    {
        public bool Success { get; set; }
        public Issue Data { get; set; }
    }

    public class Pagination
    {
        public PaginationInfo Next { get; set; }
        public PaginationInfo Prev { get; set; }
    }

    public class PaginationInfo
    {
        public int Page { get; set; }
        public int Limit { get; set; }
    }

    public class Issue
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }
        public string Location { get; set; }
        public string Photo { get; set; }
        public string Status { get; set; }
        public string User { get; set; }
        public DateTime CreatedAt { get; set; }
        public Coordinates Coordinates { get; set; }
        public List<StatusUpdate> StatusUpdates { get; set; }
    }

    public class Coordinates
    {
        public double Lat { get; set; }
        public double Lng { get; set; }
    }

    public class StatusUpdate
    {
        public string Status { get; set; }
        public string Comment { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class IssueCreateRequest
    {
        public string Title { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }
        public string Location { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public byte[] PhotoBytes { get; set; }
        public string PhotoFilename { get; set; }
    }

    #endregion

    /// <summary>
    /// Example usage of the Urban Pulse API client
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Create API client
            var client = new UrbanPulseApiClient("http://localhost:5002/api");
            
            try
            {
                // Login to get authentication token
                var loginResponse = await client.LoginAsync("<EMAIL>", "password123");
                Console.WriteLine($"Logged in as: {loginResponse.Data.Name}");
                
                // Get all issues
                var issuesResponse = await client.GetIssuesAsync();
                Console.WriteLine($"Found {issuesResponse.Count} issues:");
                
                foreach (var issue in issuesResponse.Data)
                {
                    Console.WriteLine($"- {issue.Title} ({issue.Status})");
                }
                
                // Create a new issue with photo
                var photoBytes = System.IO.File.ReadAllBytes("pothole.jpg");
                var newIssue = new IssueCreateRequest
                {
                    Title = "Large pothole on Main Street",
                    Category = "Roads",
                    Description = "There's a large pothole causing traffic hazards",
                    Location = "123 Main Street",
                    Latitude = 51.505,
                    Longitude = -0.09,
                    PhotoBytes = photoBytes,
                    PhotoFilename = "pothole.jpg"
                };
                
                var createResponse = await client.CreateIssueAsync(newIssue);
                Console.WriteLine($"Created new issue: {createResponse.Data.Id}");
                
                // Get details of the new issue
                var issueResponse = await client.GetIssueAsync(createResponse.Data.Id);
                Console.WriteLine($"Issue details: {issueResponse.Data.Title} - {issueResponse.Data.Description}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
}
