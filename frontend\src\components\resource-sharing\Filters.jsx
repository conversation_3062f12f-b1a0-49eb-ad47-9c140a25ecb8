import React from 'react';
import { Card, Form, Row, Col } from 'react-bootstrap';
import { FaFilter } from 'react-icons/fa';

/**
 * Filters component for filtering items in the resource sharing platform
 * @param {Object} props
 * @param {Array<string>} props.categories - Available categories
 * @param {Array<string>} props.locations - Available locations
 * @param {string|null} props.selectedCategory - Currently selected category
 * @param {string|null} props.selectedLocation - Currently selected location
 * @param {Function} props.onSelectCategory - Function to call when category changes
 * @param {Function} props.onSelectLocation - Function to call when location changes
 * @param {Function} props.onShowAvailableOnly - Function to call when available only filter changes
 * @param {boolean} props.showAvailableOnly - Whether to show only available items
 * @param {Function} props.onShowNearMe - Function to call when near me filter changes
 * @param {boolean} props.showNearMe - Whether to show only items near the user
 * @param {boolean} props.canUseLocation - Whether location services are available
 */
const Filters = ({
  categories,
  locations,
  selectedCategory,
  selectedLocation,
  onSelectCategory,
  onSelectLocation,
  onShowAvailableOnly,
  showAvailableOnly,
  onShowNearMe,
  showNearMe,
  canUseLocation
}) => {
  return (
    <Card className="border-0 shadow-sm mb-4">
      <Card.Body className="p-4">
        <div className="d-flex align-items-center mb-3">
          <FaFilter className="text-primary me-2" />
          <h5 className="mb-0">Filters</h5>
        </div>

        <Row>
          <Col md={4} className="mb-3">
            <Form.Group>
              <Form.Label>Category</Form.Label>
              <Form.Select
                value={selectedCategory || ''}
                onChange={(e) => onSelectCategory(e.target.value || null)}
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>

          <Col md={4} className="mb-3">
            <Form.Group>
              <Form.Label>Location</Form.Label>
              <Form.Select
                value={selectedLocation || ''}
                onChange={(e) => onSelectLocation(e.target.value || null)}
              >
                <option value="">All Locations</option>
                {locations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>

          <Col md={4} className="mb-3">
            <Form.Label>Availability</Form.Label>
            <div>
              <Form.Check
                type="checkbox"
                id="availableOnly"
                label="Show available items only"
                checked={showAvailableOnly}
                onChange={(e) => onShowAvailableOnly(e.target.checked)}
                className="mb-2"
              />
              <Form.Check
                type="checkbox"
                id="nearMe"
                label="Show items near me"
                checked={showNearMe}
                onChange={(e) => onShowNearMe(e.target.checked)}
                disabled={!canUseLocation}
              />
            </div>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );
};

export default Filters;
