const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const { getUserActivityReport, getAdminUserActivityReport } = require('../controllers/reportController');

// User routes
router.get('/activity', protect, getUserActivityReport);

// Admin routes
router.get('/admin/user/:userId/activity', protect, authorize('admin'), getAdminUserActivityReport);

module.exports = router;
