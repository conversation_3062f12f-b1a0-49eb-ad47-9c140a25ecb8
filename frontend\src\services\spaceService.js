import api from '../utils/api';

// Get all green spaces
export const getGreenSpaces = async (params) => {
  try {
    const res = await api.get('/spaces', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch green spaces');
  }
};

// Get green space by ID
export const getGreenSpaceById = async (id) => {
  try {
    const res = await api.get(`/spaces/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch green space');
  }
};

// Create new green space (admin only)
export const createGreenSpace = async (spaceData) => {
  try {
    const res = await api.post('/spaces', spaceData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create green space');
  }
};

// Update green space (admin only)
export const updateGreenSpace = async (id, spaceData) => {
  try {
    const res = await api.put(`/spaces/${id}`, spaceData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update green space');
  }
};

// Delete green space (admin only)
export const deleteGreenSpace = async (id) => {
  try {
    const res = await api.delete(`/spaces/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete green space');
  }
};

// Get events for a green space
export const getSpaceEvents = async (spaceId, params) => {
  try {
    const res = await api.get(`/spaces/${spaceId}/events`, { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch space events');
  }
};

// Get green spaces by location
export const getGreenSpacesByLocation = async (lat, lng, radius) => {
  try {
    const res = await api.get('/spaces/nearby', { params: { lat, lng, radius } });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch nearby green spaces');
  }
};

// Get green spaces by type
export const getGreenSpacesByType = async (type) => {
  try {
    const res = await api.get('/spaces', { params: { type } });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch green spaces by type');
  }
};

// Get green spaces by neighborhood
export const getGreenSpacesByNeighborhood = async (neighborhood) => {
  try {
    const res = await api.get('/spaces', { params: { neighborhood } });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch green spaces by neighborhood');
  }
};
