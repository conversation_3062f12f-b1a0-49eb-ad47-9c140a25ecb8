import React, { useState, useEffect } from 'react';
import { Dropdown, Badge, Button, Toast, ToastContainer } from 'react-bootstrap';
import { FaBell, FaCheck, FaTrash, FaExclamationCircle, FaInfoCircle, FaCheckCircle } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import notificationService from '../services/notificationService';
import { formatDistanceToNow } from 'date-fns';

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([]);
  const [showToast, setShowToast] = useState(false);
  const [currentToast, setCurrentToast] = useState(null);
  
  // Get unread count
  const unreadCount = notifications.filter(n => !n.read).length;
  
  // Listen for notifications
  useEffect(() => {
    // Get initial notifications
    setNotifications(notificationService.getNotifications());
    
    // Add listener for new notifications
    const unsubscribe = notificationService.addListener((notification) => {
      setNotifications(notificationService.getNotifications());
      
      // Show toast for new notifications
      if (notification && !notification.read) {
        setCurrentToast(notification);
        setShowToast(true);
      }
    });
    
    // Request notification permission on component mount
    notificationService.requestPermission();
    
    return () => {
      unsubscribe();
    };
  }, []);
  
  // Handle marking a notification as read
  const handleMarkAsRead = (id) => {
    notificationService.markAsRead(id);
  };
  
  // Handle marking all notifications as read
  const handleMarkAllAsRead = () => {
    notificationService.markAllAsRead();
  };
  
  // Get icon based on notification type
  const getIcon = (type) => {
    switch (type) {
      case 'error':
        return <FaExclamationCircle className="text-danger" />;
      case 'success':
        return <FaCheckCircle className="text-success" />;
      case 'warning':
        return <FaExclamationCircle className="text-warning" />;
      case 'info':
      default:
        return <FaInfoCircle className="text-primary" />;
    }
  };
  
  return (
    <>
      <Dropdown align="end">
        <Dropdown.Toggle variant="link" id="notification-dropdown" className="nav-link position-relative p-0">
          <FaBell size={20} />
          {unreadCount > 0 && (
            <Badge 
              bg="danger" 
              pill 
              className="position-absolute top-0 start-100 translate-middle"
              style={{ fontSize: '0.6rem' }}
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Dropdown.Toggle>

        <Dropdown.Menu className="shadow-sm border-0 p-0" style={{ width: '320px', maxHeight: '400px', overflow: 'auto' }}>
          <div className="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h6 className="mb-0">Notifications</h6>
            {unreadCount > 0 && (
              <Button 
                variant="link" 
                size="sm" 
                className="text-decoration-none p-0"
                onClick={handleMarkAllAsRead}
              >
                <FaCheck className="me-1" /> Mark all as read
              </Button>
            )}
          </div>
          
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-muted">
              <p className="mb-0">No notifications</p>
            </div>
          ) : (
            notifications.map((notification) => (
              <Dropdown.Item 
                key={notification.id} 
                as="div" 
                className={`p-3 border-bottom ${!notification.read ? 'bg-light' : ''}`}
              >
                <div className="d-flex">
                  <div className="me-3">
                    {getIcon(notification.type)}
                  </div>
                  <div className="flex-grow-1">
                    <div className="d-flex justify-content-between align-items-center mb-1">
                      <strong>{notification.title}</strong>
                      <small className="text-muted">
                        {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                      </small>
                    </div>
                    <p className="mb-1 small">{notification.message}</p>
                    <div className="d-flex justify-content-between align-items-center">
                      {notification.url ? (
                        <Link 
                          to={notification.url} 
                          className="small text-decoration-none"
                          onClick={() => handleMarkAsRead(notification.id)}
                        >
                          View details
                        </Link>
                      ) : (
                        <span></span>
                      )}
                      {!notification.read && (
                        <Button 
                          variant="link" 
                          size="sm" 
                          className="p-0 text-muted"
                          onClick={() => handleMarkAsRead(notification.id)}
                        >
                          Mark as read
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </Dropdown.Item>
            ))
          )}
        </Dropdown.Menu>
      </Dropdown>
      
      {/* Toast notifications */}
      <ToastContainer position="top-end" className="p-3" style={{ zIndex: 1060 }}>
        {currentToast && (
          <Toast 
            show={showToast} 
            onClose={() => setShowToast(false)} 
            delay={5000} 
            autohide
          >
            <Toast.Header>
              <span className="me-2">{getIcon(currentToast.type)}</span>
              <strong className="me-auto">{currentToast.title}</strong>
              <small>{formatDistanceToNow(new Date(currentToast.timestamp), { addSuffix: true })}</small>
            </Toast.Header>
            <Toast.Body>
              {currentToast.message}
              {currentToast.url && (
                <div className="mt-2">
                  <Link 
                    to={currentToast.url} 
                    className="btn btn-sm btn-primary"
                    onClick={() => {
                      handleMarkAsRead(currentToast.id);
                      setShowToast(false);
                    }}
                  >
                    View details
                  </Link>
                </div>
              )}
            </Toast.Body>
          </Toast>
        )}
      </ToastContainer>
    </>
  );
};

export default NotificationCenter;
