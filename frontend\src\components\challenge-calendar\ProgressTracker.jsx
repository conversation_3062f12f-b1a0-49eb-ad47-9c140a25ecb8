import React from 'react';
import { Card } from 'react-bootstrap';
import { FaCheckCircle, FaCircle } from 'react-icons/fa';

const ProgressTracker = ({ challenges }) => {
  // Calculate progress statistics
  const totalChallenges = challenges.length;
  const completedChallenges = challenges.filter(c => c.completed).length;
  const completionRate = totalChallenges > 0 
    ? Math.round((completedChallenges / totalChallenges) * 100) 
    : 0;
  const totalPoints = challenges
    .filter(c => c.completed)
    .reduce((sum, challenge) => sum + (challenge.points || 0), 0);

  return (
    <Card className="border-0 shadow-sm">
      <Card.Body className="p-4">
        <h5 className="mb-3">Your Progress</h5>
        
        <div className="d-flex justify-content-between align-items-center mb-3">
          <span className="h4 mb-0">{completedChallenges}</span>
          <span className="text-muted">Challenges Completed</span>
        </div>
        
        <div className="d-flex justify-content-between align-items-center mb-3">
          <span className="h4 mb-0">{totalPoints}</span>
          <span className="text-muted">Points Earned</span>
        </div>
        
        <div className="d-flex justify-content-between align-items-center mb-3">
          <span className="h4 mb-0">{completionRate}%</span>
          <span className="text-muted">Completion Rate</span>
        </div>
        
        <div className="mt-4">
          <p className="mb-2 fw-bold">Progress</p>
          <div className="d-flex align-items-center gap-1">
            {challenges.map((challenge) => (
              <div key={challenge.id} className="flex-grow-1">
                {challenge.completed ? (
                  <FaCheckCircle className="text-success" />
                ) : (
                  <FaCircle className="text-muted opacity-25" />
                )}
              </div>
            ))}
          </div>
          <div className="d-flex align-items-center mt-2">
            <small className="text-muted">
              {completedChallenges}/{totalChallenges} challenges
            </small>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export default ProgressTracker;
