const axios = require('axios');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');
  
  // Get a token for authentication
  let token;
  try {
    console.log('Attempting to log in...');
    const loginResponse = await axios.post('http://localhost:5002/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    token = loginResponse.data.token;
    console.log('Login successful, token obtained');
  } catch (error) {
    console.error('Login failed:', error.response ? error.response.data : error.message);
    process.exit(1);
  }
  
  // Test issue submission
  try {
    console.log('\nTesting issue submission...');
    
    // Create form data
    const formData = new FormData();
    formData.append('title', 'Test Issue from Script');
    formData.append('category', 'Roads');
    formData.append('description', 'This is a test issue created from a script');
    formData.append('location', 'Test Location');
    
    // Add a test image if available
    const testImagePath = path.join(__dirname, 'test-image.jpg');
    if (fs.existsSync(testImagePath)) {
      const fileStream = fs.createReadStream(testImagePath);
      formData.append('photo', fileStream);
      console.log('Added test image to form data');
    }
    
    console.log('Submitting issue with data:', {
      title: 'Test Issue from Script',
      category: 'Roads',
      description: 'This is a test issue created from a script',
      location: 'Test Location'
    });
    
    // Make the API request
    const response = await axios.post('http://localhost:5002/api/issues', formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('\nIssue submission successful!');
    console.log('Response:', response.data);
  } catch (error) {
    console.error('\nIssue submission failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
  
  // Close the MongoDB connection
  await mongoose.connection.close();
  console.log('\nConnection closed');
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});
