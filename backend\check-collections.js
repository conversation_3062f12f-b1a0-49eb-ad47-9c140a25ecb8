const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('Checking MongoDB collections...');
console.log(`Connecting to: ${process.env.MONGO_URI}`);

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');
  
  // Check collections
  const collections = await mongoose.connection.db.listCollections().toArray();
  
  if (collections.length === 0) {
    console.log('No collections found in the database.');
  } else {
    console.log('\nExisting collections:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
  }
  
  // Expected collections based on models
  const expectedCollections = [
    'users',
    'issues',
    'events',
    'resources',
    'spaces',
    'transport',
    'challenges',
    'reviews',
    'services',
    'courses'
  ];
  
  console.log('\nMissing collections:');
  const existingCollectionNames = collections.map(c => c.name);
  const missingCollections = expectedCollections.filter(name => !existingCollectionNames.includes(name));
  
  if (missingCollections.length === 0) {
    console.log('All expected collections exist.');
  } else {
    missingCollections.forEach(name => {
      console.log(`- ${name}`);
    });
  }
  
  // Close the connection
  await mongoose.connection.close();
  console.log('\nConnection closed');
  process.exit(0);
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});
