import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>er, <PERSON>, Col, Card, Nav, Tab, <PERSON>ton, Badge, ProgressBar } from 'react-bootstrap';
import { FaLeaf, FaHeartbeat, FaAward, FaCalendarAlt, FaTrophy, FaCheckCircle } from 'react-icons/fa';

// Import our new components
import MonthlyCalendar from '../components/challenge-calendar/MonthlyCalendar';
import ProgressTracker from '../components/challenge-calendar/ProgressTracker';
import BadgeDisplay from '../components/challenge-calendar/BadgeDisplay';

const ChallengeCalendarPage = () => {
  // State for selected month and active tab
  const [selectedMonth, setSelectedMonth] = useState("January");
  const [activeTab, setActiveTab] = useState("sustainability");
  const [earnedBadges, setEarnedBadges] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get completed challenges from localStorage or initialize empty arrays
  const [sustainabilityCompleted, setSustainabilityCompleted] = useState(() => {
    const saved = localStorage.getItem('sustainabilityCompletedChallenges');
    return saved ? JSON.parse(saved) : [];
  });

  const [healthCompleted, setHealthCompleted] = useState(() => {
    const saved = localStorage.getItem('healthCompletedChallenges');
    return saved ? JSON.parse(saved) : [];
  });

  // Save completed challenges to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('sustainabilityCompletedChallenges', JSON.stringify(sustainabilityCompleted));
  }, [sustainabilityCompleted]);

  useEffect(() => {
    localStorage.setItem('healthCompletedChallenges', JSON.stringify(healthCompleted));
  }, [healthCompleted]);

  // Convert existing challenges to the new format with IDs and completion status
  const [challenges, setChallenges] = useState(() => {
    // Convert sustainability challenges
    const sustainabilityChallengesData = [
      {
        id: 's1',
        week: 1,
        month: "January",
        title: "Plastic-Free Week",
        description: "Avoid single-use plastics for the entire week. Use reusable alternatives.",
        points: 50,
        category: "sustainability",
        tips: "Carry a reusable water bottle, coffee cup, shopping bags, and food containers.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's2',
        week: 2,
        month: "January",
        title: "Energy Audit",
        description: "Conduct an energy audit of your home and identify three ways to reduce consumption.",
        points: 40,
        category: "sustainability",
        tips: "Check for drafts, inspect insulation, and evaluate appliance efficiency.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's3',
        week: 3,
        month: "January",
        title: "Meatless Monday",
        description: "Go vegetarian for a full day each week this month to reduce your carbon footprint.",
        points: 30,
        category: "sustainability",
        tips: "Try plant-based proteins like beans, lentils, tofu, or tempeh.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's4',
        week: 4,
        month: "January",
        title: "Zero-Waste Shopping",
        description: "Shop for groceries without generating any packaging waste.",
        points: 60,
        category: "sustainability",
        tips: "Bring your own containers, shop at bulk stores, and choose package-free produce.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's5',
        week: 1,
        month: "February",
        title: "Water Conservation",
        description: "Implement three water-saving techniques in your home.",
        points: 40,
        category: "sustainability",
        tips: "Fix leaks, install low-flow fixtures, and collect shower water for plants.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's6',
        week: 2,
        month: "February",
        title: "Buy Nothing Week",
        description: "Avoid purchasing any new non-essential items for one week.",
        points: 50,
        category: "sustainability",
        tips: "Borrow, repair, or repurpose items instead of buying new ones.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's7',
        week: 3,
        month: "February",
        title: "Upcycling Project",
        description: "Transform waste materials into something useful or decorative instead of discarding them.",
        points: 35,
        category: "sustainability",
        tips: "Consider glass jars, cardboard boxes, or old clothing. Look online for creative upcycling ideas.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's8',
        week: 4,
        month: "February",
        title: "Composting Challenge",
        description: "Start a composting system at home to reduce food waste going to landfills.",
        points: 55,
        category: "sustainability",
        tips: "Choose between a backyard compost bin, worm composting, or a countertop system for apartments.",
        is_accepted: false,
        completed: false
      },
      // March Challenges
      {
        id: 's9',
        week: 1,
        month: "March",
        title: "Water Conservation",
        description: "Implement water-saving practices at home to reduce water consumption.",
        points: 50,
        category: "sustainability",
        tips: "Fix leaky faucets, install low-flow showerheads, and collect rainwater for plants.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's10',
        week: 2,
        month: "March",
        title: "Sustainable Fashion",
        description: "Avoid fast fashion for a month and choose sustainable clothing options.",
        points: 40,
        category: "sustainability",
        tips: "Shop secondhand, repair existing clothes, or support ethical clothing brands.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's11',
        week: 3,
        month: "March",
        title: "Digital Cleanup",
        description: "Reduce your digital carbon footprint by cleaning up digital waste.",
        points: 30,
        category: "sustainability",
        tips: "Delete unused emails, files, and apps. Unsubscribe from unnecessary newsletters.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's12',
        week: 4,
        month: "March",
        title: "Local Food Challenge",
        description: "Source your food from local producers to reduce food miles.",
        points: 60,
        category: "sustainability",
        tips: "Visit farmers markets, join a CSA, or shop at stores that highlight local products.",
        is_accepted: false,
        completed: false
      },
      // April Challenges
      {
        id: 's13',
        week: 1,
        month: "April",
        title: "Eco-Friendly Transportation",
        description: "Use eco-friendly transportation methods for a week (walking, biking, public transit).",
        points: 55,
        category: "sustainability",
        tips: "Plan your routes in advance, try carpooling, or use electric vehicles when possible.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's14',
        week: 2,
        month: "April",
        title: "Paperless Challenge",
        description: "Reduce paper usage by going digital for all communications and note-taking.",
        points: 40,
        category: "sustainability",
        tips: "Use digital apps for notes, opt for e-receipts, and unsubscribe from paper mail.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's15',
        week: 3,
        month: "April",
        title: "Earth Day Advocacy",
        description: "Educate others about environmental issues and inspire action in your community.",
        points: 65,
        category: "sustainability",
        tips: "Share information on social media, organize a workshop, or create educational materials.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's16',
        week: 4,
        month: "April",
        title: "Native Plant Garden",
        description: "Plant native species in your garden to support local biodiversity.",
        points: 50,
        category: "sustainability",
        tips: "Research native plants for your region, visit local nurseries, and create habitats for pollinators.",
        is_accepted: false,
        completed: false
      },
      // May Challenges
      {
        id: 's17',
        week: 1,
        month: "May",
        title: "Renewable Energy Research",
        description: "Research renewable energy options for your home and take steps toward implementation.",
        points: 60,
        category: "sustainability",
        tips: "Look into solar panels, wind energy, or switching to a green energy provider.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's18',
        week: 2,
        month: "May",
        title: "Sustainable Gardening",
        description: "Implement sustainable gardening practices to conserve water and reduce chemical use.",
        points: 45,
        category: "sustainability",
        tips: "Use companion planting, natural pest control, and water-saving techniques like mulching.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's19',
        week: 3,
        month: "May",
        title: "Plastic Audit",
        description: "Identify all plastic items in your home and find sustainable alternatives.",
        points: 50,
        category: "sustainability",
        tips: "Focus on the bathroom and kitchen first, where most plastic items are found.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's20',
        week: 4,
        month: "May",
        title: "Eco-Friendly Cleaning",
        description: "Replace conventional cleaning products with natural, non-toxic alternatives.",
        points: 40,
        category: "sustainability",
        tips: "Make your own cleaners using vinegar, baking soda, and essential oils.",
        is_accepted: false,
        completed: false
      },
      // June Challenges
      {
        id: 's21',
        week: 1,
        month: "June",
        title: "Ocean Conservation",
        description: "Take actions to protect ocean health and marine life.",
        points: 55,
        category: "sustainability",
        tips: "Participate in a beach cleanup, avoid single-use plastics, and choose sustainable seafood.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's22',
        week: 2,
        month: "June",
        title: "Sustainable Travel",
        description: "Plan a trip using sustainable travel practices to minimize environmental impact.",
        points: 60,
        category: "sustainability",
        tips: "Choose eco-friendly accommodations, pack light, and offset your carbon emissions.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's23',
        week: 3,
        month: "June",
        title: "Water Footprint Reduction",
        description: "Track and reduce your direct and indirect water consumption.",
        points: 45,
        category: "sustainability",
        tips: "Take shorter showers, fix leaks, and be mindful of the water used to produce food and goods.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's24',
        week: 4,
        month: "June",
        title: "Sustainable Eating",
        description: "Adopt a more sustainable diet by focusing on local, seasonal, and plant-based foods.",
        points: 50,
        category: "sustainability",
        tips: "Reduce food waste, eat more plants, and choose sustainably produced animal products.",
        is_accepted: false,
        completed: false
      },
      // July Challenges
      {
        id: 's25',
        week: 1,
        month: "July",
        title: "Plastic-Free July",
        description: "Participate in Plastic-Free July by eliminating single-use plastics for the month.",
        points: 70,
        category: "sustainability",
        tips: "Plan ahead with reusable alternatives for common plastic items you use daily.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's26',
        week: 2,
        month: "July",
        title: "Energy Conservation",
        description: "Reduce your energy consumption during peak summer demand.",
        points: 45,
        category: "sustainability",
        tips: "Use fans instead of AC when possible, hang clothes to dry, and unplug electronics.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's27',
        week: 3,
        month: "July",
        title: "Sustainable Recreation",
        description: "Enjoy outdoor activities while practicing Leave No Trace principles.",
        points: 40,
        category: "sustainability",
        tips: "Stay on designated trails, pack out all trash, and respect wildlife and natural features.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's28',
        week: 4,
        month: "July",
        title: "Community Garden",
        description: "Volunteer at a community garden or start a neighborhood gardening project.",
        points: 55,
        category: "sustainability",
        tips: "Connect with local garden organizations, share resources, and grow food for food banks.",
        is_accepted: false,
        completed: false
      },
      // August Challenges
      {
        id: 's29',
        week: 1,
        month: "August",
        title: "Sustainable Home Cooling",
        description: "Implement passive cooling strategies to reduce air conditioning use.",
        points: 50,
        category: "sustainability",
        tips: "Use window coverings, create cross-ventilation, and plant shade trees around your home.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's30',
        week: 2,
        month: "August",
        title: "Zero-Waste Bathroom",
        description: "Transform your bathroom routine to eliminate waste and reduce plastic.",
        points: 45,
        category: "sustainability",
        tips: "Try solid shampoo bars, bamboo toothbrushes, and reusable cotton rounds.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's31',
        week: 3,
        month: "August",
        title: "Sustainable School Supplies",
        description: "Choose eco-friendly school or office supplies for the upcoming season.",
        points: 40,
        category: "sustainability",
        tips: "Look for recycled paper products, refillable pens, and plastic-free alternatives.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's32',
        week: 4,
        month: "August",
        title: "Food Preservation",
        description: "Learn and practice methods to preserve seasonal produce to reduce food waste.",
        points: 55,
        category: "sustainability",
        tips: "Try canning, freezing, drying, or fermenting fruits and vegetables at their peak.",
        is_accepted: false,
        completed: false
      },
      // September Challenges
      {
        id: 's33',
        week: 1,
        month: "September",
        title: "Sustainable Fashion Audit",
        description: "Evaluate your wardrobe and create a plan for more sustainable clothing choices.",
        points: 50,
        category: "sustainability",
        tips: "Organize a clothing swap, repair damaged items, and research ethical brands.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's34',
        week: 2,
        month: "September",
        title: "Reduce Food Waste",
        description: "Implement strategies to minimize food waste in your household.",
        points: 45,
        category: "sustainability",
        tips: "Plan meals, store food properly, use leftovers creatively, and compost scraps.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's35',
        week: 3,
        month: "September",
        title: "Sustainable Transportation Week",
        description: "Use only sustainable transportation methods for an entire week.",
        points: 60,
        category: "sustainability",
        tips: "Walk, bike, use public transit, or carpool for all your transportation needs.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's36',
        week: 4,
        month: "September",
        title: "Environmental Advocacy",
        description: "Contact elected officials about an environmental issue important to you.",
        points: 55,
        category: "sustainability",
        tips: "Research the issue thoroughly, prepare talking points, and be specific about desired actions.",
        is_accepted: false,
        completed: false
      },
      // October Challenges
      {
        id: 's37',
        week: 1,
        month: "October",
        title: "Sustainable Halloween",
        description: "Plan an eco-friendly Halloween with minimal waste and sustainable choices.",
        points: 50,
        category: "sustainability",
        tips: "Create costumes from existing items, use natural decorations, and choose plastic-free treats.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's38',
        week: 2,
        month: "October",
        title: "Home Energy Efficiency",
        description: "Prepare your home for colder weather with energy-efficient improvements.",
        points: 55,
        category: "sustainability",
        tips: "Seal drafts, add insulation, service heating systems, and install a programmable thermostat.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's39',
        week: 3,
        month: "October",
        title: "Sustainable Tech Habits",
        description: "Adopt more sustainable practices with your electronic devices.",
        points: 40,
        category: "sustainability",
        tips: "Extend device lifespan through proper maintenance, repair rather than replace, and recycle e-waste properly.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's40',
        week: 4,
        month: "October",
        title: "Zero-Waste Kitchen",
        description: "Transform your kitchen routines to minimize waste and increase sustainability.",
        points: 60,
        category: "sustainability",
        tips: "Use reusable food storage, cloth towels instead of paper, and buy in bulk to reduce packaging.",
        is_accepted: false,
        completed: false
      },
      // November Challenges
      {
        id: 's41',
        week: 1,
        month: "November",
        title: "Sustainable Thanksgiving",
        description: "Plan a more sustainable Thanksgiving celebration with reduced environmental impact.",
        points: 65,
        category: "sustainability",
        tips: "Source local and organic ingredients, reduce food waste, and use reusable dinnerware.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's42',
        week: 2,
        month: "November",
        title: "Buy Nothing Week",
        description: "Participate in Buy Nothing Week by avoiding all non-essential purchases.",
        points: 55,
        category: "sustainability",
        tips: "Borrow, repair, or make do with what you have instead of buying new items.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's43',
        week: 3,
        month: "November",
        title: "Sustainable Gift Planning",
        description: "Plan sustainable, meaningful gifts for the upcoming holiday season.",
        points: 45,
        category: "sustainability",
        tips: "Consider experiences, homemade gifts, second-hand items, or donations to causes.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's44',
        week: 4,
        month: "November",
        title: "Gratitude for Nature",
        description: "Practice daily gratitude for nature and take actions to protect it.",
        points: 40,
        category: "sustainability",
        tips: "Spend time outdoors, journal about nature appreciation, and take conservation actions.",
        is_accepted: false,
        completed: false
      },
      // December Challenges
      {
        id: 's45',
        week: 1,
        month: "December",
        title: "Eco-Friendly Holiday Decorations",
        description: "Create or choose sustainable holiday decorations that minimize environmental impact.",
        points: 50,
        category: "sustainability",
        tips: "Use natural materials, LED lights, and reusable or biodegradable decorations.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's46',
        week: 2,
        month: "December",
        title: "Sustainable Gift Wrapping",
        description: "Use eco-friendly gift wrapping alternatives to reduce waste.",
        points: 45,
        category: "sustainability",
        tips: "Try fabric wrapping (furoshiki), reused paper, or recyclable materials without tape.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's47',
        week: 3,
        month: "December",
        title: "Low-Impact Celebrations",
        description: "Host or attend holiday gatherings with sustainability in mind.",
        points: 60,
        category: "sustainability",
        tips: "Reduce food waste, use reusable dinnerware, and consider virtual gatherings to reduce travel.",
        is_accepted: false,
        completed: false
      },
      {
        id: 's48',
        week: 4,
        month: "December",
        title: "Year-End Reflection",
        description: "Reflect on your sustainability journey and set green goals for the new year.",
        points: 55,
        category: "sustainability",
        tips: "Review your accomplishments, identify areas for improvement, and create specific action plans.",
        is_accepted: false,
        completed: false
      }
    ];

    // Convert health challenges
    const healthChallengesData = [
      {
        id: 'h1',
        week: 1,
        month: "January",
        title: "Hydration Habit",
        description: "Drink at least 8 glasses of water daily for the entire week.",
        points: 40,
        category: "health",
        tips: "Keep a water bottle with you at all times and set reminders.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h2',
        week: 2,
        month: "January",
        title: "Morning Stretch Routine",
        description: "Start each day with a 10-minute stretching routine.",
        points: 50,
        category: "health",
        tips: "Focus on major muscle groups and hold each stretch for 20-30 seconds.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h3',
        week: 3,
        month: "January",
        title: "Mindful Eating",
        description: "Practice mindful eating at every meal for one week.",
        points: 45,
        category: "health",
        tips: "Eat slowly, without distractions, and pay attention to hunger and fullness cues.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h4',
        week: 4,
        month: "January",
        title: "Sleep Improvement",
        description: "Establish a consistent sleep schedule and bedtime routine for one week.",
        points: 55,
        category: "health",
        tips: "Aim for 7-9 hours of sleep, avoid screens before bed, and create a relaxing environment.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h5',
        week: 1,
        month: "February",
        title: "Heart-Healthy Meals",
        description: "Prepare heart-healthy meals for five days, focusing on whole foods.",
        points: 60,
        category: "health",
        tips: "Include plenty of vegetables, whole grains, lean proteins, and healthy fats.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h6',
        week: 2,
        month: "February",
        title: "Cardio Challenge",
        description: "Complete 30 minutes of cardiovascular exercise three times this week.",
        points: 50,
        category: "health",
        tips: "Choose activities you enjoy like walking, cycling, dancing, or swimming.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h7',
        week: 3,
        month: "February",
        title: "Strength Training",
        description: "Complete two strength training sessions this week.",
        points: 55,
        category: "health",
        tips: "Focus on major muscle groups and use bodyweight exercises if you don't have equipment.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h8',
        week: 4,
        month: "February",
        title: "Digital Detox",
        description: "Reduce screen time by 50% for one week to improve mental health.",
        points: 45,
        category: "health",
        tips: "Set specific times for checking devices, use screen time tracking apps, and find offline activities.",
        is_accepted: false,
        completed: false
      },
      // March Health Challenges
      {
        id: 'h9',
        week: 1,
        month: "March",
        title: "Spring Fitness Restart",
        description: "Establish a new fitness routine for spring with at least 3 workouts this week.",
        points: 50,
        category: "health",
        tips: "Choose activities you enjoy, set realistic goals, and track your progress.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h10',
        week: 2,
        month: "March",
        title: "Meal Prep Challenge",
        description: "Prepare healthy meals in advance for at least 4 days this week.",
        points: 45,
        category: "health",
        tips: "Choose recipes that freeze well, use similar ingredients for multiple meals, and invest in good containers.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h11',
        week: 3,
        month: "March",
        title: "Stress Management",
        description: "Practice a different stress-reduction technique each day for a week.",
        points: 40,
        category: "health",
        tips: "Try meditation, deep breathing, yoga, journaling, nature walks, or progressive muscle relaxation.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h12',
        week: 4,
        month: "March",
        title: "Sugar Reduction",
        description: "Reduce added sugar intake by avoiding processed sweets and sugary drinks for one week.",
        points: 55,
        category: "health",
        tips: "Read labels carefully, try natural sweeteners like fruit, and drink water or herbal tea instead of soda.",
        is_accepted: false,
        completed: false
      },
      // April Health Challenges
      {
        id: 'h13',
        week: 1,
        month: "April",
        title: "Daily Walking",
        description: "Walk for at least 30 minutes every day for one week.",
        points: 40,
        category: "health",
        tips: "Break it into smaller sessions if needed, take walking meetings, or explore new neighborhoods.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h14',
        week: 2,
        month: "April",
        title: "Posture Improvement",
        description: "Practice good posture throughout the day and do posture-strengthening exercises.",
        points: 35,
        category: "health",
        tips: "Set reminders to check your posture, strengthen your core, and set up an ergonomic workspace.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h15',
        week: 3,
        month: "April",
        title: "Healthy Snacking",
        description: "Replace processed snacks with nutritious alternatives for one week.",
        points: 45,
        category: "health",
        tips: "Prepare snacks in advance, keep healthy options visible, and focus on protein and fiber.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h16',
        week: 4,
        month: "April",
        title: "Gratitude Practice",
        description: "Write down three things you're grateful for each day for one week.",
        points: 30,
        category: "health",
        tips: "Be specific, include both big and small things, and reflect on why they matter to you.",
        is_accepted: false,
        completed: false
      },
      // May Health Challenges
      {
        id: 'h17',
        week: 1,
        month: "May",
        title: "Outdoor Workouts",
        description: "Exercise outdoors at least 3 times this week to boost mood and vitamin D.",
        points: 50,
        category: "health",
        tips: "Try hiking, cycling, outdoor yoga, or bodyweight exercises in a park.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h18',
        week: 2,
        month: "May",
        title: "Meditation Challenge",
        description: "Meditate for at least 10 minutes daily for one week.",
        points: 45,
        category: "health",
        tips: "Use a guided meditation app, start with shorter sessions, and try different techniques.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h19',
        week: 3,
        month: "May",
        title: "Healthy Sleep Routine",
        description: "Establish a consistent sleep schedule and bedtime routine for one week.",
        points: 55,
        category: "health",
        tips: "Aim for 7-9 hours, avoid screens before bed, and create a relaxing environment.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h20',
        week: 4,
        month: "May",
        title: "Plant-Based Meals",
        description: "Prepare at least 5 plant-based meals this week.",
        points: 50,
        category: "health",
        tips: "Focus on legumes, whole grains, and seasonal vegetables. Try international cuisines for inspiration.",
        is_accepted: false,
        completed: false
      },
      // June Health Challenges
      {
        id: 'h21',
        week: 1,
        month: "June",
        title: "Strength Training",
        description: "Complete 3 strength training sessions this week.",
        points: 55,
        category: "health",
        tips: "Focus on major muscle groups, use bodyweight exercises if you don't have equipment, and maintain proper form.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h22',
        week: 2,
        month: "June",
        title: "Digital Sunset",
        description: "Turn off all screens at least 1 hour before bedtime for one week.",
        points: 40,
        category: "health",
        tips: "Replace screen time with reading, journaling, or gentle stretching.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h23',
        week: 3,
        month: "June",
        title: "Hydration Challenge",
        description: "Drink at least 8 glasses of water daily for one week.",
        points: 35,
        category: "health",
        tips: "Carry a reusable water bottle, set reminders, and add natural flavors like lemon or cucumber.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h24',
        week: 4,
        month: "June",
        title: "Mindful Eating",
        description: "Practice mindful eating at every meal for one week.",
        points: 45,
        category: "health",
        tips: "Eat slowly, without distractions, and pay attention to hunger and fullness cues.",
        is_accepted: false,
        completed: false
      },
      // July Health Challenges
      {
        id: 'h25',
        week: 1,
        month: "July",
        title: "Summer Fitness Challenge",
        description: "Try a new outdoor fitness activity at least twice this week.",
        points: 50,
        category: "health",
        tips: "Consider swimming, hiking, kayaking, or outdoor yoga classes.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h26',
        week: 2,
        month: "July",
        title: "Healthy Summer Cooking",
        description: "Prepare meals using fresh, seasonal produce for one week.",
        points: 45,
        category: "health",
        tips: "Visit farmers markets, try new recipes, and focus on colorful fruits and vegetables.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h27',
        week: 3,
        month: "July",
        title: "Digital Detox Weekend",
        description: "Spend an entire weekend (48 hours) without screens.",
        points: 60,
        category: "health",
        tips: "Plan outdoor activities, read books, connect with friends in person, and enjoy nature.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h28',
        week: 4,
        month: "July",
        title: "Proper Hydration",
        description: "Track and optimize your water intake during hot summer days.",
        points: 40,
        category: "health",
        tips: "Carry a reusable water bottle, set reminders, and eat water-rich foods like watermelon and cucumber.",
        is_accepted: false,
        completed: false
      },
      // August Health Challenges
      {
        id: 'h29',
        week: 1,
        month: "August",
        title: "Morning Routine Makeover",
        description: "Establish a healthy morning routine and follow it for one week.",
        points: 50,
        category: "health",
        tips: "Include hydration, movement, mindfulness, and a nutritious breakfast.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h30',
        week: 2,
        month: "August",
        title: "Flexibility Focus",
        description: "Incorporate stretching or yoga into your daily routine for one week.",
        points: 45,
        category: "health",
        tips: "Start with 10-15 minutes daily, focus on major muscle groups, and hold each stretch for 20-30 seconds.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h31',
        week: 3,
        month: "August",
        title: "Healthy Lunch Prep",
        description: "Prepare and bring your own nutritious lunch every day for one week.",
        points: 40,
        category: "health",
        tips: "Batch cook on weekends, use a variety of containers, and include protein, whole grains, and vegetables.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h32',
        week: 4,
        month: "August",
        title: "Stress Management Toolkit",
        description: "Create a personalized toolkit of stress management techniques and use them daily.",
        points: 55,
        category: "health",
        tips: "Include breathing exercises, physical activity, creative outlets, and social connection.",
        is_accepted: false,
        completed: false
      },
      // September Health Challenges
      {
        id: 'h33',
        week: 1,
        month: "September",
        title: "Back-to-Routine Fitness",
        description: "Establish a consistent exercise schedule for the fall season.",
        points: 55,
        category: "health",
        tips: "Schedule workouts in your calendar, find an accountability partner, and set specific goals.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h34',
        week: 2,
        month: "September",
        title: "Healthy Breakfast Challenge",
        description: "Start each day with a nutritious breakfast for one week.",
        points: 40,
        category: "health",
        tips: "Prepare overnight oats, egg muffins, or smoothie ingredients in advance for busy mornings.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h35',
        week: 3,
        month: "September",
        title: "Posture Improvement",
        description: "Focus on maintaining good posture throughout the day for one week.",
        points: 45,
        category: "health",
        tips: "Set hourly reminders, strengthen core muscles, and ensure ergonomic workspaces.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h36',
        week: 4,
        month: "September",
        title: "Mindfulness Practice",
        description: "Practice mindfulness for at least 10 minutes daily for one week.",
        points: 50,
        category: "health",
        tips: "Try guided meditations, mindful walking, or simply focusing on your breath.",
        is_accepted: false,
        completed: false
      },
      // October Health Challenges
      {
        id: 'h37',
        week: 1,
        month: "October",
        title: "Immune System Boost",
        description: "Adopt habits that support immune health as cold season approaches.",
        points: 55,
        category: "health",
        tips: "Focus on adequate sleep, stress management, and foods rich in vitamins C and D.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h38',
        week: 2,
        month: "October",
        title: "Healthy Snack Makeover",
        description: "Replace processed snacks with nutritious alternatives for one week.",
        points: 40,
        category: "health",
        tips: "Prepare snacks in advance, keep healthy options visible, and focus on protein and fiber.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h39',
        week: 3,
        month: "October",
        title: "Strength Training Focus",
        description: "Complete three strength training sessions this week.",
        points: 50,
        category: "health",
        tips: "Include exercises for all major muscle groups, focus on form, and gradually increase resistance.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h40',
        week: 4,
        month: "October",
        title: "Healthy Halloween",
        description: "Navigate Halloween treats mindfully and maintain healthy habits.",
        points: 45,
        category: "health",
        tips: "Choose dark chocolate, limit portion sizes, maintain regular exercise, and stay hydrated.",
        is_accepted: false,
        completed: false
      },
      // November Health Challenges
      {
        id: 'h41',
        week: 1,
        month: "November",
        title: "Gratitude Practice",
        description: "Write down three things you're grateful for each day for one week.",
        points: 40,
        category: "health",
        tips: "Be specific, include both big and small things, and reflect on why they matter to you.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h42',
        week: 2,
        month: "November",
        title: "Indoor Workout Routine",
        description: "Establish an effective indoor workout routine for colder weather.",
        points: 55,
        category: "health",
        tips: "Try bodyweight exercises, online fitness classes, or invest in simple home equipment.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h43',
        week: 3,
        month: "November",
        title: "Healthy Holiday Cooking",
        description: "Prepare healthier versions of traditional holiday dishes.",
        points: 50,
        category: "health",
        tips: "Reduce sugar and fat, increase vegetables, and practice portion control.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h44',
        week: 4,
        month: "November",
        title: "Stress Management",
        description: "Implement daily stress management techniques during the busy holiday season.",
        points: 45,
        category: "health",
        tips: "Try deep breathing, progressive muscle relaxation, or short meditation sessions.",
        is_accepted: false,
        completed: false
      },
      // December Health Challenges
      {
        id: 'h45',
        week: 1,
        month: "December",
        title: "Winter Wellness Routine",
        description: "Establish a winter wellness routine to maintain health during the holiday season.",
        points: 55,
        category: "health",
        tips: "Include immune-supporting foods, regular exercise, adequate sleep, and stress management.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h46',
        week: 2,
        month: "December",
        title: "Mindful Holiday Eating",
        description: "Practice mindful eating at holiday gatherings and events.",
        points: 50,
        category: "health",
        tips: "Eat slowly, savor special treats, stay hydrated, and listen to hunger cues.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h47',
        week: 3,
        month: "December",
        title: "Active Family Time",
        description: "Incorporate physical activity into family gatherings and traditions.",
        points: 45,
        category: "health",
        tips: "Organize family walks, active games, or outdoor winter activities.",
        is_accepted: false,
        completed: false
      },
      {
        id: 'h48',
        week: 4,
        month: "December",
        title: "Year-End Health Reflection",
        description: "Reflect on your health journey this year and set wellness goals for the new year.",
        points: 60,
        category: "health",
        tips: "Celebrate successes, identify areas for growth, and create specific, achievable goals.",
        is_accepted: false,
        completed: false
      }
    ];

    // Mark challenges as completed based on localStorage data
    const savedSustainabilityCompleted = localStorage.getItem('sustainabilityCompletedChallenges');
    const savedHealthCompleted = localStorage.getItem('healthCompletedChallenges');

    const sustainabilityCompletedWeeks = savedSustainabilityCompleted ? JSON.parse(savedSustainabilityCompleted) : [];
    const healthCompletedWeeks = savedHealthCompleted ? JSON.parse(savedHealthCompleted) : [];

    // Mark sustainability challenges as completed
    sustainabilityChallengesData.forEach(challenge => {
      if (sustainabilityCompletedWeeks.includes(challenge.week)) {
        challenge.is_accepted = true;
        challenge.completed = true;
      }
    });

    // Mark health challenges as completed
    healthChallengesData.forEach(challenge => {
      if (healthCompletedWeeks.includes(challenge.week)) {
        challenge.is_accepted = true;
        challenge.completed = true;
      }
    });

    // Combine all challenges
    return [...sustainabilityChallengesData, ...healthChallengesData];
  });

  // Define badges data
  const [badges, setBadges] = useState([
    // Sustainability Badges
    {
      id: 's1',
      name: 'January Sustainability Master',
      description: 'Completed all January sustainability challenges',
      category: 'sustainability',
      month: 'January',
      earned: false,
      imageUrl: '/assets/badges/january-badge.svg'
    },
    {
      id: 's2',
      name: 'February Sustainability Master',
      description: 'Completed all February sustainability challenges',
      category: 'sustainability',
      month: 'February',
      earned: false,
      imageUrl: '/assets/badges/february-badge.svg'
    },
    {
      id: 's3',
      name: 'March Sustainability Master',
      description: 'Completed all March sustainability challenges',
      category: 'sustainability',
      month: 'March',
      earned: false,
      imageUrl: '/assets/badges/march-badge.svg'
    },
    {
      id: 's4',
      name: 'April Sustainability Master',
      description: 'Completed all April sustainability challenges',
      category: 'sustainability',
      month: 'April',
      earned: false,
      imageUrl: '/assets/badges/april-badge.svg'
    },
    {
      id: 's5',
      name: 'May Sustainability Master',
      description: 'Completed all May sustainability challenges',
      category: 'sustainability',
      month: 'May',
      earned: false,
      imageUrl: '/assets/badges/may-badge.svg'
    },
    {
      id: 's6',
      name: 'June Sustainability Master',
      description: 'Completed all June sustainability challenges',
      category: 'sustainability',
      month: 'June',
      earned: false,
      imageUrl: '/assets/badges/june-badge.svg'
    },
    {
      id: 's7',
      name: 'July Sustainability Master',
      description: 'Completed all July sustainability challenges',
      category: 'sustainability',
      month: 'July',
      earned: false,
      imageUrl: '/assets/badges/july-badge.svg'
    },
    {
      id: 's8',
      name: 'August Sustainability Master',
      description: 'Completed all August sustainability challenges',
      category: 'sustainability',
      month: 'August',
      earned: false,
      imageUrl: '/assets/badges/august-badge.svg'
    },
    {
      id: 's9',
      name: 'September Sustainability Master',
      description: 'Completed all September sustainability challenges',
      category: 'sustainability',
      month: 'September',
      earned: false,
      imageUrl: '/assets/badges/september-badge.svg'
    },
    {
      id: 's10',
      name: 'October Sustainability Master',
      description: 'Completed all October sustainability challenges',
      category: 'sustainability',
      month: 'October',
      earned: false,
      imageUrl: '/assets/badges/october-badge.svg'
    },
    {
      id: 's11',
      name: 'November Sustainability Master',
      description: 'Completed all November sustainability challenges',
      category: 'sustainability',
      month: 'November',
      earned: false,
      imageUrl: '/assets/badges/november-badge.svg'
    },
    {
      id: 's12',
      name: 'December Sustainability Master',
      description: 'Completed all December sustainability challenges',
      category: 'sustainability',
      month: 'December',
      earned: false,
      imageUrl: '/assets/badges/december-badge.svg'
    },

    // Health Badges
    {
      id: 'h1',
      name: 'January Health Champion',
      description: 'Completed all January health challenges',
      category: 'health',
      month: 'January',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h2',
      name: 'February Health Champion',
      description: 'Completed all February health challenges',
      category: 'health',
      month: 'February',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h3',
      name: 'March Health Champion',
      description: 'Completed all March health challenges',
      category: 'health',
      month: 'March',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h4',
      name: 'April Health Champion',
      description: 'Completed all April health challenges',
      category: 'health',
      month: 'April',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h5',
      name: 'May Health Champion',
      description: 'Completed all May health challenges',
      category: 'health',
      month: 'May',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h6',
      name: 'June Health Champion',
      description: 'Completed all June health challenges',
      category: 'health',
      month: 'June',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h7',
      name: 'July Health Champion',
      description: 'Completed all July health challenges',
      category: 'health',
      month: 'July',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h8',
      name: 'August Health Champion',
      description: 'Completed all August health challenges',
      category: 'health',
      month: 'August',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h9',
      name: 'September Health Champion',
      description: 'Completed all September health challenges',
      category: 'health',
      month: 'September',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h10',
      name: 'October Health Champion',
      description: 'Completed all October health challenges',
      category: 'health',
      month: 'October',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h11',
      name: 'November Health Champion',
      description: 'Completed all November health challenges',
      category: 'health',
      month: 'November',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    },
    {
      id: 'h12',
      name: 'December Health Champion',
      description: 'Completed all December health challenges',
      category: 'health',
      month: 'December',
      earned: false,
      imageUrl: '/assets/badges/health-badge.svg'
    }
  ]);

  // Function to check if all challenges for a month and category are completed
  const checkAllChallengesCompleted = (month, category) => {
    const filteredChallenges = challenges.filter(
      challenge => challenge.month === month && challenge.category === category
    );
    return filteredChallenges.length > 0 &&
           filteredChallenges.every(challenge => challenge.completed);
  };

  // Function to update badge earned status
  const updateBadgeStatus = (month, category) => {
    const allCompleted = checkAllChallengesCompleted(month, category);
    if (allCompleted) {
      const badge = badges.find(b => b.month === month && b.category === category);
      if (badge && !earnedBadges.includes(badge.id)) {
        // Update badge status
        setEarnedBadges(prev => [...prev, badge.id]);
        setBadges(prevBadges => {
          return prevBadges.map(b =>
            b.id === badge.id
              ? { ...b, earned: true }
              : b
          );
        });
        console.log(`Badge ${badge.name} earned!`);
      }
    }
  };

  // Handle accepting a challenge
  const handleAcceptChallenge = (id) => {
    console.log('Accept challenge', id);

    setChallenges(prevChallenges => {
      return prevChallenges.map(challenge =>
        challenge.id === id
          ? { ...challenge, is_accepted: true }
          : challenge
      );
    });
  };

  // Handle completing a challenge
  const handleCompleteChallenge = (id, completed) => {
    console.log('Complete challenge', id, completed);

    // Find the challenge that was completed
    const challenge = challenges.find(c => c.id === id);
    if (challenge && completed) {
      // Update challenges state
      setChallenges(prevChallenges => {
        return prevChallenges.map(c =>
          c.id === id
            ? { ...c, completed: true, is_accepted: true }
            : c
        );
      });

      // Update the appropriate completed challenges array for backward compatibility
      if (challenge.category === 'sustainability') {
        setSustainabilityCompleted(prev => {
          if (prev.includes(challenge.week)) {
            return prev;
          } else {
            return [...prev, challenge.week];
          }
        });
      } else if (challenge.category === 'health') {
        setHealthCompleted(prev => {
          if (prev.includes(challenge.week)) {
            return prev;
          } else {
            return [...prev, challenge.week];
          }
        });
      }

      // Check if all challenges for the month and category are completed
      updateBadgeStatus(challenge.month, challenge.category);
    }
  };

  // Handle month change
  const handleMonthChange = (month) => {
    setSelectedMonth(month);
  };

  // Calculate progress for sustainability challenges
  const sustainabilityChallenges = challenges.filter(c => c.category === 'sustainability');
  const completedSustainabilityChallenges = sustainabilityChallenges.filter(c => c.completed);
  const sustainabilityProgress = Math.round((completedSustainabilityChallenges.length / sustainabilityChallenges.length) * 100);

  // Calculate progress for health challenges
  const healthChallenges = challenges.filter(c => c.category === 'health');
  const completedHealthChallenges = healthChallenges.filter(c => c.completed);
  const healthProgress = Math.round((completedHealthChallenges.length / healthChallenges.length) * 100);

  // Get challenges for the selected month and active tab category
  const currentMonthChallenges = challenges.filter(challenge =>
    challenge.month === selectedMonth &&
    (activeTab === 'sustainability' ? challenge.category === 'sustainability' : challenge.category === 'health')
  );

  // Check if all challenges for the current month and category are completed
  const allMonthChallengesCompleted = checkAllChallengesCompleted(
    selectedMonth,
    activeTab === 'sustainability' ? 'sustainability' : 'health'
  );

  // Get the current badge for the selected month and category
  const currentBadge = badges.find(badge =>
    badge.month === selectedMonth &&
    (activeTab === 'sustainability' ? badge.category === 'sustainability' : badge.category === 'health')
  );

  // Calculate challenge stats
  const challengeStats = {
    totalAccepted: challenges.filter(c => c.is_accepted).length,
    completed: challenges.filter(c => c.completed).length,
    points: challenges.filter(c => c.completed).reduce((sum, c) => sum + c.points, 0)
  };

  // Function to switch to badges tab
  const switchToBadgesTab = () => {
    setActiveTab("badges");
  };

  // Set loading to false after initial setup
  useEffect(() => {
    setIsLoading(false);
  }, []);

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Sustainability Challenge Dashboard</h1>
          <p className="lead">
            Complete weekly challenges to improve your sustainability practices and health habits while earning points!
          </p>
        </Col>
      </Row>

      {isLoading ? (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '300px' }}>
          <div className="text-center">
            <div className="spinner-border text-success" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3 text-muted">Loading your challenges...</p>
          </div>
        </div>
      ) : (
        <>
          <Row className="mb-4">
            <Col md={4}>
              <Card className="border-0 shadow-sm mb-4">
                <Card.Body className="p-4">
                  <h5 className="mb-3">Welcome</h5>
                  <div className="mb-3">
                    <p className="text-muted mb-1">Email</p>
                    <p className="fw-bold"><EMAIL></p>
                  </div>
                  <div>
                    <p className="text-muted mb-1">Name</p>
                    <p className="fw-bold">Demo User</p>
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col md={4}>
              <Card className="border-0 shadow-sm mb-4">
                <Card.Body className="p-4">
                  <h5 className="d-flex align-items-center mb-3">
                    <FaAward className="text-success me-2" />
                    Challenge Stats
                  </h5>
                  <div className="d-flex justify-content-between mb-2">
                    <span className="text-muted">Challenges Accepted</span>
                    <span className="fw-bold">{challengeStats.totalAccepted}</span>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span className="text-muted">Challenges Completed</span>
                    <span className="fw-bold">{challengeStats.completed}</span>
                  </div>
                  <div className="d-flex justify-content-between">
                    <span className="text-muted">Total Points Earned</span>
                    <span className="fw-bold">{challengeStats.points}</span>
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col md={4}>
              <Card className="border-0 shadow-sm mb-4">
                <Card.Body className="p-4">
                  <h5 className="mb-3">Account Stats</h5>
                  <div className="d-flex justify-content-between mb-2">
                    <span className="text-muted">Account created</span>
                    <span className="fw-bold">{new Date().toLocaleDateString()}</span>
                  </div>
                  <div className="d-flex justify-content-between">
                    <span className="text-muted">Last sign in</span>
                    <span className="fw-bold">{new Date().toLocaleDateString()}</span>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <Tab.Container activeKey={activeTab} onSelect={setActiveTab}>
            <Row className="mb-4">
              <Col>
                <Nav variant="pills" className="bg-light p-2 rounded">
                  <Nav.Item>
                    <Nav.Link
                      eventKey="sustainability"
                      className="rounded-pill"
                    >
                      <FaCalendarAlt className="me-2" />
                      Challenge Calendar
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link
                      eventKey="badges"
                      className="rounded-pill"
                    >
                      <FaAward className="me-2" />
                      Badges & Rewards
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </Col>
            </Row>

            <Row>
              <Col>
                <Tab.Content>
                  <Tab.Pane eventKey="sustainability">
                    <Row>
                      <Col md={8}>
                        {/* Badge earned notification */}
                        {allMonthChallengesCompleted && currentBadge && (
                          <div className="mb-4 bg-success bg-opacity-10 p-4 text-center rounded">
                            <div className="d-flex justify-content-center mb-3">
                              <div className="bg-success bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style={{ width: '80px', height: '80px' }}>
                                <FaAward className="text-success" size={40} />
                              </div>
                            </div>
                            <h3 className="h5 mb-2">Badge Earned: {currentBadge.name}</h3>
                            <p className="text-muted">{currentBadge.description}</p>
                            <Button
                              variant="outline-success"
                              size="sm"
                              className="mt-2"
                              onClick={switchToBadgesTab}
                            >
                              View Your Badges
                            </Button>
                          </div>
                        )}

                        <MonthlyCalendar
                          challenges={challenges.filter(c => c.category === 'sustainability')}
                          badges={badges.filter(b => b.category === 'sustainability')}
                          onAcceptChallenge={handleAcceptChallenge}
                          onCompleteChallenge={handleCompleteChallenge}
                          onMonthChange={handleMonthChange}
                          initialMonth={selectedMonth}
                        />
                      </Col>

                      <Col md={4}>
                        <ProgressTracker
                          challenges={currentMonthChallenges}
                        />

                        <Card className="border-0 shadow-sm mt-4">
                          <Card.Body className="p-4">
                            <h5 className="d-flex align-items-center mb-3">
                              <FaTrophy className="text-success me-2" />
                              Latest Achievement
                            </h5>
                            <div className="bg-light p-3 text-center rounded">
                              <div className="d-flex justify-content-center mb-2">
                                <div className="bg-success bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style={{ width: '50px', height: '50px' }}>
                                  <FaCheckCircle className="text-success" />
                                </div>
                              </div>
                              <h6>
                                {currentMonthChallenges.filter(c => c.completed).length > 0
                                  ? `Completed ${currentMonthChallenges.filter(c => c.completed).length} ${selectedMonth} challenges!`
                                  : `No ${selectedMonth} challenges completed yet`}
                              </h6>
                              <p className="text-muted small mb-0">
                                {currentMonthChallenges.filter(c => c.completed).length > 0
                                  ? "Keep up the good work!"
                                  : "Start your first challenge today!"}
                              </p>
                            </div>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>
                  </Tab.Pane>

                  <Tab.Pane eventKey="badges">
                    <Card className="border-0 shadow-sm mb-4">
                      <Card.Body className="p-4">
                        <h2 className="h4 mb-3 text-success">Monthly Sustainability Badges</h2>
                        <p className="text-muted">
                          Complete all challenges in a month to earn its badge. Earned badges are unlocked, while others remain locked.
                        </p>
                      </Card.Body>
                    </Card>

                    <Row xs={1} md={2} lg={3} className="g-4 mb-4">
                      {badges
                        .filter(badge => badge.category === 'sustainability')
                        .sort((a, b) => {
                          const monthOrder = [
                            "January", "February", "March", "April", "May", "June",
                            "July", "August", "September", "October", "November", "December"
                          ];
                          return monthOrder.indexOf(a.month) - monthOrder.indexOf(b.month);
                        })
                        .map(badge => (
                          <Col key={badge.id}>
                            <BadgeDisplay
                              badge={{
                                ...badge,
                                earned: earnedBadges.includes(badge.id) || badge.earned ||
                                        (badge.month === selectedMonth &&
                                         checkAllChallengesCompleted(badge.month, badge.category))
                              }}
                              relatedChallenges={challenges.filter(c =>
                                c.month === badge.month && c.category === badge.category
                              )}
                            />
                          </Col>
                        ))
                      }
                    </Row>

                    <Card className="border-0 shadow-sm mb-4">
                      <Card.Body className="p-4">
                        <h2 className="h4 mb-3 text-info">Health & Wellness Badges</h2>
                        <p className="text-muted">
                          Complete all health challenges in a month to earn these special badges.
                        </p>
                      </Card.Body>
                    </Card>

                    <Row xs={1} md={2} lg={3} className="g-4 mb-4">
                      {badges
                        .filter(badge => badge.category === 'health')
                        .sort((a, b) => {
                          const monthOrder = [
                            "January", "February", "March", "April", "May", "June",
                            "July", "August", "September", "October", "November", "December"
                          ];
                          return monthOrder.indexOf(a.month) - monthOrder.indexOf(b.month);
                        })
                        .map(badge => (
                          <Col key={badge.id}>
                            <BadgeDisplay
                              badge={{
                                ...badge,
                                earned: earnedBadges.includes(badge.id) || badge.earned ||
                                        (badge.month === selectedMonth &&
                                         checkAllChallengesCompleted(badge.month, badge.category))
                              }}
                              relatedChallenges={challenges.filter(c =>
                                c.month === badge.month && c.category === badge.category
                              )}
                            />
                          </Col>
                        ))
                      }
                    </Row>
                  </Tab.Pane>
                </Tab.Content>
              </Col>
            </Row>
          </Tab.Container>
        </>
      )}
    </Container>
  );
};

export default ChallengeCalendarPage;
