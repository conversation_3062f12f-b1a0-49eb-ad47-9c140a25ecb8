const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const { getUserStats, getUserActivity } = require('../controllers/userStatsController');

// @desc    Get user stats
// @route   GET /api/user/stats
// @access  Private
router.get('/stats', protect, getUserStats);

// @desc    Get user activity details
// @route   GET /api/user/activity/:type
// @access  Private
router.get('/activity/:type', protect, getUserActivity);

// @desc    Get user notifications
// @route   GET /api/user/notifications
// @access  Private
router.get('/notifications', protect, async (req, res) => {
  try {
    // In a real application, you would fetch this from your database
    const notifications = [
      { id: 1, type: 'challenge', message: 'New weekly challenge available!', date: '2 hours ago' },
      { id: 2, type: 'issue', message: 'Your reported issue has been resolved', date: '1 day ago' },
      { id: 3, type: 'event', message: 'Upcoming community cleanup this weekend', date: '2 days ago' }
    ];

    res.status(200).json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// @desc    Get user recommendations
// @route   GET /api/user/recommendations
// @access  Private
router.get('/recommendations', protect, async (req, res) => {
  try {
    // In a real application, you would fetch this from your database
    const recommendations = [
      { id: 1, type: 'challenge', title: 'Zero Waste Week', description: 'Try to produce zero waste for a week' },
      { id: 2, type: 'course', title: 'Urban Gardening Basics', description: 'Learn how to grow food in small spaces' },
      { id: 3, type: 'resource', title: 'Tool Library', description: 'Share and borrow tools in your community' }
    ];

    res.status(200).json(recommendations);
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
