const Event = require('../models/Event');
const GreenSpace = require('../models/GreenSpace');

// @desc    Get all events
// @route   GET /api/events
// @access  Public
exports.getEvents = async (req, res, next) => {
  try {
    let query;

    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = ['select', 'sort', 'page', 'limit'];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach(param => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

    // Finding resource
    query = Event.find(JSON.parse(queryStr))
      .populate('spaceId', 'name type neighborhood')
      .populate('createdBy', 'name');

    // Select Fields
    if (req.query.select) {
      const fields = req.query.select.split(',').join(' ');
      query = query.select(fields);
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('date');
    }

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const total = await Event.countDocuments();

    query = query.skip(startIndex).limit(limit);

    // Executing query
    const events = await query;

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }

    res.status(200).json({
      success: true,
      count: events.length,
      pagination,
      data: events
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get single event
// @route   GET /api/events/:id
// @access  Public
exports.getEvent = async (req, res, next) => {
  try {
    const event = await Event.findById(req.params.id)
      .populate('spaceId', 'name type neighborhood')
      .populate('createdBy', 'name');

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Event not found'
      });
    }

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Create new event
// @route   POST /api/events
// @access  Private
exports.createEvent = async (req, res, next) => {
  try {
    // Add user to req.body
    req.body.createdBy = req.user.id;

    // Check if green space exists
    const greenSpace = await GreenSpace.findById(req.body.spaceId);

    if (!greenSpace) {
      return res.status(404).json({
        success: false,
        error: 'Green space not found'
      });
    }

    const event = await Event.create(req.body);

    res.status(201).json({
      success: true,
      data: event
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Update event
// @route   PUT /api/events/:id
// @access  Private
exports.updateEvent = async (req, res, next) => {
  try {
    let event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Event not found'
      });
    }

    // Make sure user is event creator or admin
    if (event.createdBy.toString() !== req.user.id && !req.user.isAdmin) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update this event'
      });
    }

    event = await Event.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Delete event
// @route   DELETE /api/events/:id
// @access  Private
exports.deleteEvent = async (req, res, next) => {
  try {
    const event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Event not found'
      });
    }

    // Make sure user is event creator or admin
    if (event.createdBy.toString() !== req.user.id && !req.user.isAdmin) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to delete this event'
      });
    }

    await event.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Join event
// @route   POST /api/events/:id/join
// @access  Private
exports.joinEvent = async (req, res, next) => {
  try {
    const event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Event not found'
      });
    }

    // Check if user already joined
    if (event.participants.some(participant => participant.user.toString() === req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Already joined this event'
      });
    }

    // Add user to participants
    event.participants.push({ user: req.user.id });

    await event.save();

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Leave event
// @route   DELETE /api/events/:id/join
// @access  Private
exports.leaveEvent = async (req, res, next) => {
  try {
    const event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Event not found'
      });
    }

    // Remove user from participants
    event.participants = event.participants.filter(
      participant => participant.user.toString() !== req.user.id
    );

    await event.save();

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get events for a green space
// @route   GET /api/spaces/:spaceId/events
// @access  Public
exports.getSpaceEvents = async (req, res, next) => {
  try {
    const events = await Event.find({ spaceId: req.params.spaceId })
      .populate('createdBy', 'name')
      .sort('date');

    res.status(200).json({
      success: true,
      count: events.length,
      data: events
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get user's events
// @route   GET /api/events/my-events
// @access  Private
exports.getMyEvents = async (req, res, next) => {
  try {
    const events = await Event.find({
      'participants.user': req.user.id
    })
      .populate('spaceId', 'name type neighborhood')
      .sort('date');

    res.status(200).json({
      success: true,
      count: events.length,
      data: events
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get upcoming events
// @route   GET /api/events/upcoming
// @access  Private
exports.getUpcomingEvents = async (req, res, next) => {
  try {
    // Get current date
    const now = new Date();

    // Find events with date greater than or equal to today
    const events = await Event.find({
      date: { $gte: now }
    })
      .populate('spaceId', 'name type neighborhood')
      .populate('createdBy', 'name')
      .sort('date')
      .limit(5);

    // Format the events for the frontend
    const formattedEvents = events.map(event => {
      const eventDate = new Date(event.date);
      return {
        id: event._id,
        title: event.title,
        date: eventDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        location: event.spaceId ? event.spaceId.name : event.location,
        description: event.description,
        organizer: event.createdBy ? event.createdBy.name : 'Urban Pulse'
      };
    });

    res.status(200).json(formattedEvents);
  } catch (err) {
    console.error('Error fetching upcoming events:', err);
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};
