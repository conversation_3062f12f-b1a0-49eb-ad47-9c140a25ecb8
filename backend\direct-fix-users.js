const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

console.log('Direct User Account Fix <PERSON>');
console.log('============================');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async (conn) => {
  console.log('Connected to MongoDB');
  console.log(`Database: ${conn.connection.name}`);
  
  // Get the users collection directly
  const db = conn.connection.db;
  const usersCollection = db.collection('users');
  
  // Function to create or update a user directly in the database
  async function createOrUpdateUserDirect(email, name, password, isAdmin = false) {
    try {
      // Hash the password directly
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      // Check if user exists
      const existingUser = await usersCollection.findOne({ email });
      
      if (existingUser) {
        console.log(`User ${email} already exists, updating...`);
        
        // Update user
        await usersCollection.updateOne(
          { email },
          { 
            $set: { 
              name, 
              password: hashedPassword,
              isAdmin
            } 
          }
        );
        
        console.log(`User ${email} updated successfully`);
      } else {
        console.log(`User ${email} does not exist, creating...`);
        
        // Create new user
        await usersCollection.insertOne({
          name,
          email,
          password: hashedPassword,
          isAdmin,
          createdAt: new Date()
        });
        
        console.log(`User ${email} created successfully`);
      }
      
      // Verify password
      const updatedUser = await usersCollection.findOne({ email });
      const isMatch = await bcrypt.compare(password, updatedUser.password);
      console.log(`Password verification for ${email}: ${isMatch ? 'Success ✅' : 'Failed ❌'}`);
      
      return updatedUser;
    } catch (error) {
      console.error(`Error creating/updating user ${email}:`, error.message);
      throw error;
    }
  }
  
  try {
    // Create or update test user
    await createOrUpdateUserDirect('<EMAIL>', 'Test User', 'password123');
    
    // Create or update demo user
    await createOrUpdateUserDirect('<EMAIL>', 'Demo User', 'password123');
    
    // Create or update admin user
    await createOrUpdateUserDirect('<EMAIL>', 'Admin User', 'password123', true);
    
    console.log('\nAll users have been created or updated successfully');
    
    // List all users
    const users = await usersCollection.find({}).toArray();
    console.log(`\nFound ${users.length} users in database:`);
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}) ${user.isAdmin ? '[Admin]' : ''}`);
    });
    
    // Test login with each user
    console.log('\nTesting login for each user:');
    
    for (const testUser of [
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'password123' }
    ]) {
      const user = await usersCollection.findOne({ email: testUser.email });
      
      if (user) {
        const isMatch = await bcrypt.compare(testUser.password, user.password);
        console.log(`- ${testUser.email}: ${isMatch ? 'Success ✅' : 'Failed ❌'}`);
      } else {
        console.log(`- ${testUser.email}: User not found ❌`);
      }
    }
  } catch (error) {
    console.error('Error fixing users:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\nConnection closed');
    process.exit(0);
  }
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});
