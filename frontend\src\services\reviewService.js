import api from '../utils/api';

// Get all services
export const getServices = async (params) => {
  try {
    const res = await api.get('/services', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch services');
  }
};

// Get service by ID
export const getServiceById = async (id) => {
  try {
    const res = await api.get(`/services/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch service');
  }
};

// Get reviews for a service
export const getServiceReviews = async (serviceId, params) => {
  try {
    const res = await api.get(`/services/${serviceId}/reviews`, { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch service reviews');
  }
};

// Get review by ID
export const getReviewById = async (id) => {
  try {
    const res = await api.get(`/reviews/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch review');
  }
};

// Create new review
export const createReview = async (serviceId, reviewData) => {
  try {
    const res = await api.post(`/services/${serviceId}/reviews`, reviewData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create review');
  }
};

// Update review
export const updateReview = async (id, reviewData) => {
  try {
    const res = await api.put(`/reviews/${id}`, reviewData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update review');
  }
};

// Delete review
export const deleteReview = async (id) => {
  try {
    const res = await api.delete(`/reviews/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete review');
  }
};

// Mark review as helpful
export const markReviewHelpful = async (id) => {
  try {
    const res = await api.post(`/reviews/${id}/helpful`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to mark review as helpful');
  }
};

// Add comment to review
export const addReviewComment = async (reviewId, comment) => {
  try {
    const res = await api.post(`/reviews/${reviewId}/comments`, { text: comment });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to add comment');
  }
};

// Get service statistics
export const getServiceStats = async () => {
  try {
    const res = await api.get('/services/stats');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch service statistics');
  }
};

// Get user's reviews
export const getMyReviews = async () => {
  try {
    const res = await api.get('/reviews/my-reviews');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch your reviews');
  }
};
