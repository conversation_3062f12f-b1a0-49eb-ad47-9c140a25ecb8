const express = require('express');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const cors = require('cors');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Enable CORS for all routes
app.use(cors());

// Parse JSON request bodies
app.use(express.json());

// Middleware to log all requests
app.use((req, res, next) => {
  const start = Date.now();
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  
  if (req.body && Object.keys(req.body).length > 0) {
    // Clone the body and mask any passwords
    const safeBody = { ...req.body };
    if (safeBody.password) {
      safeBody.password = '********';
    }
    console.log('Body:', safeBody);
  }
  
  // Capture the original end method
  const originalEnd = res.end;
  
  // Override the end method to log the response
  res.end = function(chunk, encoding) {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] Response: ${res.statusCode} (${duration}ms)`);
    
    // Call the original end method
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
});

// Connect to MongoDB
console.log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
  
  // Start server
  const PORT = 5001;
  app.listen(PORT, () => {
    console.log(`Debug login server running on port ${PORT}`);
    console.log(`http://localhost:${PORT}`);
  });
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});

// Root route
app.get('/', (req, res) => {
  res.json({ 
    message: 'Debug login server is running',
    timestamp: new Date().toISOString()
  });
});

// Login route
app.post('/api/auth/login', async (req, res) => {
  console.log('\n=== LOGIN REQUEST ===');
  console.log('Login request received at:', new Date().toISOString());
  
  try {
    const { email, password } = req.body;
    
    // Validate input
    if (!email || !password) {
      console.log('Missing email or password');
      return res.status(400).json({ 
        success: false,
        message: 'Please provide email and password' 
      });
    }
    
    console.log(`Attempting login for email: ${email}`);
    
    // Get users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Find user by email
    const user = await usersCollection.findOne({ email });
    
    if (!user) {
      console.log(`User not found: ${email}`);
      return res.status(401).json({ 
        success: false,
        message: 'Invalid credentials' 
      });
    }
    
    console.log(`User found: ${user.name} (${user.email})`);
    console.log(`Password exists: ${!!user.password}`);
    
    if (!user.password) {
      console.log('User has no password set');
      return res.status(401).json({ 
        success: false,
        message: 'Account has no password set' 
      });
    }
    
    // Check password
    console.log('Comparing passwords...');
    const isMatch = await bcrypt.compare(password, user.password);
    
    console.log(`Password match result: ${isMatch}`);
    
    if (!isMatch) {
      console.log('Password does not match');
      return res.status(401).json({ 
        success: false,
        message: 'Invalid credentials' 
      });
    }
    
    console.log('Password matches, generating token');
    
    // Generate token
    const token = jwt.sign(
      { id: user._id, email: user.email, isAdmin: user.isAdmin || false },
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production',
      { expiresIn: '24h' }
    );
    
    console.log('Token generated successfully');
    
    // Prepare user data for response
    const userData = {
      id: user._id,
      name: user.name,
      email: user.email,
      isAdmin: user.isAdmin || false
    };
    
    console.log('Login successful, sending response');
    
    // Return success response
    res.json({
      success: true,
      token,
      user: userData
    });
    
    console.log('Response sent successfully');
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error during login',
      error: error.message
    });
  }
});

// Get user profile route
app.get('/api/auth/me', async (req, res) => {
  console.log('\n=== GET USER PROFILE ===');
  
  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        success: false,
        message: 'No token provided' 
      });
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify token
    const decoded = jwt.verify(
      token, 
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production'
    );
    
    console.log('Token verified, user ID:', decoded.id);
    
    // Get user from database
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    const user = await usersCollection.findOne({ _id: new mongoose.Types.ObjectId(decoded.id) });
    
    if (!user) {
      console.log('User not found');
      return res.status(404).json({ 
        success: false,
        message: 'User not found' 
      });
    }
    
    console.log(`User found: ${user.name} (${user.email})`);
    
    // Return user data
    res.json({
      success: true,
      data: {
        id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin || false
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error fetching profile',
      error: error.message
    });
  }
});
