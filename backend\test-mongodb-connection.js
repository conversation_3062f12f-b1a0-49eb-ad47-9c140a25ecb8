const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('MongoDB Connection Test');
console.log('======================');
console.log(`Attempting to connect using: ${process.env.MONGO_URI}`);

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(conn => {
  console.log('\nCONNECTION SUCCESSFUL! ✅');
  console.log('======================');
  console.log(`Connected to MongoDB at: ${conn.connection.host}`);
  console.log(`Database name: ${conn.connection.name}`);
  console.log(`MongoDB version: ${conn.version}`);
  
  // List all collections in the database
  return conn.connection.db.listCollections().toArray();
})
.then(collections => {
  if (collections.length === 0) {
    console.log('\nNo collections found in this database yet.');
  } else {
    console.log('\nCollections in this database:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
  }
  
  // Close the connection
  return mongoose.connection.close();
})
.then(() => {
  console.log('\nConnection closed.');
  process.exit(0);
})
.catch(error => {
  console.error('\nCONNECTION FAILED! ❌');
  console.error('======================');
  console.error(`Error: ${error.message}`);
  
  if (error.message.includes('ECONNREFUSED')) {
    console.error('\nPossible causes:');
    console.error('1. MongoDB is not running');
    console.error('2. The connection string is pointing to the wrong host/port');
    console.error('3. A firewall is blocking the connection');
  } else if (error.message.includes('Authentication failed')) {
    console.error('\nPossible causes:');
    console.error('1. Username or password in the connection string is incorrect');
    console.error('2. The user does not have access to the specified database');
  } else if (error.message.includes('timed out')) {
    console.error('\nPossible causes:');
    console.error('1. Network connectivity issues');
    console.error('2. The MongoDB server is not reachable');
    console.error('3. IP access list restrictions (for Atlas)');
  }
  
  console.error('\nPlease check your connection string and make sure MongoDB is running.');
  process.exit(1);
});
