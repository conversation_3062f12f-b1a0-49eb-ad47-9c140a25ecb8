import api from '../utils/api';

// Get all issues
export const getIssues = async (params) => {
  try {
    const res = await api.get('/issues', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch issues');
  }
};

// Get issue by ID
export const getIssueById = async (id) => {
  try {
    const res = await api.get(`/issues/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch issue');
  }
};

// Create new issue
export const createIssue = async (issueData) => {
  try {
    const res = await api.post('/issues', issueData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create issue');
  }
};

// Update issue
export const updateIssue = async (id, issueData) => {
  try {
    const res = await api.put(`/issues/${id}`, issueData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update issue');
  }
};

// Delete issue
export const deleteIssue = async (id) => {
  try {
    const res = await api.delete(`/issues/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete issue');
  }
};

// Add status update
export const addStatusUpdate = async (id, statusData) => {
  try {
    const res = await api.post(`/issues/${id}/status`, statusData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update issue status');
  }
};
