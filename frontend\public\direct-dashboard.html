<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 40px;
        }
        .dashboard-header {
            background-color: #0d6efd;
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            text-align: center;
            padding: 1.5rem;
        }
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .badge-level {
            font-size: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading your dashboard...</p>
        </div>

        <div id="dashboard-content" style="display: none;">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-1">Welcome back, <span id="user-name">User</span>!</h2>
                        <p class="mb-0">Your personalized urban sustainability dashboard</p>
                    </div>
                    <div class="col-md-4 text-md-end mt-3 mt-md-0">
                        <span id="user-level" class="badge bg-light text-dark p-2 badge-level">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-fill me-1" viewBox="0 0 16 16">
                                <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3Zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/>
                            </svg>
                            Bronze Level
                        </span>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <h4 class="mb-4">Your Activity</h4>
            <div class="row row-cols-2 row-cols-md-4 g-3 mb-4">
                <div class="col">
                    <div class="card stat-card">
                        <div class="stat-icon text-danger">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-exclamation-triangle-fill" viewBox="0 0 16 16">
                                <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                            </svg>
                        </div>
                        <h6>Issues</h6>
                        <div class="stat-value">3</div>
                    </div>
                </div>
                <div class="col">
                    <div class="card stat-card">
                        <div class="stat-icon text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-bus-front-fill" viewBox="0 0 16 16">
                                <path d="M16 7a1 1 0 0 1-1 1v3.5c0 .818-.393 1.544-1 2v2a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5V14H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2a2.496 2.496 0 0 1-1-2V8a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1V2.64C1 1.452 1.845.408 3.064.268A43.608 43.608 0 0 1 8 0c2.1 0 3.792.136 4.936.268C14.155.408 15 1.452 15 2.64V4a1 1 0 0 1 1 1v2ZM3.552 3.22A43.306 43.306 0 0 1 8 3c1.837 0 3.353.107 4.448.22a.5.5 0 0 0 .104-.994A44.304 44.304 0 0 0 8 2c-1.876 0-3.426.109-4.552.226a.5.5 0 1 0 .104.994ZM8 4c-1.876 0-3.426.109-4.552.226A.5.5 0 0 0 3 4.723v3.554a.5.5 0 0 0 .448.497C4.574 8.891 6.124 9 8 9c1.876 0 3.426-.109 4.552-.226A.5.5 0 0 0 13 8.277V4.723a.5.5 0 0 0-.448-.497A44.304 44.304 0 0 0 8 4Zm-3 7a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm8 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm-7 0a1 1 0 0 0 1 1h2a1 1 0 1 0 0-2H7a1 1 0 0 0-1 1Z"/>
                            </svg>
                        </div>
                        <h6>Feedback</h6>
                        <div class="stat-value">5</div>
                    </div>
                </div>
                <div class="col">
                    <div class="card stat-card">
                        <div class="stat-icon text-success">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-calendar-check-fill" viewBox="0 0 16 16">
                                <path d="M4 .5a.5.5 0 0 0-1 0V1H2a2 2 0 0 0-2 2v1h16V3a2 2 0 0 0-2-2h-1V.5a.5.5 0 0 0-1 0V1H4V.5zM16 14V5H0v9a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2zm-5.146-5.146-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 0 1 .708-.708L7.5 10.793l2.646-2.647a.5.5 0 0 1 .708.708z"/>
                            </svg>
                        </div>
                        <h6>Challenges</h6>
                        <div class="stat-value">7</div>
                    </div>
                </div>
                <div class="col">
                    <div class="card stat-card">
                        <div class="stat-icon text-warning">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-star-fill" viewBox="0 0 16 16">
                                <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                            </svg>
                        </div>
                        <h6>Reviews</h6>
                        <div class="stat-value">4</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <h4 class="mb-4">Quick Actions</h4>
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">Report an Issue</h5>
                            <p class="card-text">Report potholes, streetlights, trash issues and more with simple forms and photos.</p>
                            <a href="/issue-reporting" class="btn btn-primary">Report Now</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">Weekly Challenge</h5>
                            <p class="card-text">This week: Reduce your plastic usage by avoiding single-use plastics.</p>
                            <a href="/sustainable-living" class="btn btn-success">Join Challenge</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Return to App -->
            <div class="text-center mt-5">
                <p>This is a simplified dashboard view. For the full experience:</p>
                <a href="/" class="btn btn-primary">Go to Main Application</a>
            </div>
        </div>

        <div id="error-message" class="alert alert-danger text-center" style="display: none;">
            <h4>Authentication Required</h4>
            <p>You need to be logged in to view this page.</p>
            <a href="/public/direct-login.html" class="btn btn-primary mt-3">Go to Login</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            const userDataString = localStorage.getItem('user');
            
            if (token && userDataString) {
                try {
                    // Parse user data
                    const userData = JSON.parse(userDataString);
                    console.log('User data found:', userData);
                    
                    // Update user name
                    document.getElementById('user-name').textContent = userData.name || 'User';
                    
                    // Calculate user level based on activity
                    const totalActivities = 19; // This would be calculated from actual user data
                    let level = 'Bronze';
                    let color = '#CD7F32';
                    
                    if (totalActivities >= 30) {
                        level = 'Gold';
                        color = '#FFD700';
                    } else if (totalActivities >= 15) {
                        level = 'Silver';
                        color = '#C0C0C0';
                    }
                    
                    // Update user level badge
                    const levelBadge = document.getElementById('user-level');
                    levelBadge.textContent = level + ' Level';
                    levelBadge.style.border = `2px solid ${color}`;
                    
                    // Show dashboard content
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('dashboard-content').style.display = 'block';
                    
                    // Try to redirect to the React dashboard
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 3000);
                    
                } catch (error) {
                    console.error('Error parsing user data:', error);
                    showError();
                }
            } else {
                console.error('No token or user data found');
                showError();
            }
            
            function showError() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error-message').style.display = 'block';
            }
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
