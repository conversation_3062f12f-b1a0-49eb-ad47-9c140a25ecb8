:root {
  --primary-color: #2e7d32;
  --secondary-color: #1565c0;
  --accent-color: #f57c00;
  --light-color: #f5f5f5;
  --dark-color: #212121;
  --success-color: #43a047;
  --warning-color: #ffa000;
  --danger-color: #e53935;
  --info-color: #039be5;
}

body {
  font-family: 'Poppins', sans-serif;
  color: var(--dark-color);
  background-color: #f9f9f9;
}

/* Header Styles */
.navbar-brand {
  font-weight: 700;
  color: var(--primary-color);
}

/* Card Styles */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
}

/* <PERSON><PERSON> */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #1b5e20;
  border-color: #1b5e20;
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Form Styles */
.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
}

/* Footer Styles */
footer {
  background-color: var(--dark-color);
  color: white;
}

/* Map Styles */
.map-container {
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
}

/* Badge Styles */
.badge-sustainable {
  background-color: var(--success-color);
  color: white;
}

/* Leaderboard Styles */
.leaderboard-item {
  transition: background-color 0.3s ease;
}

.leaderboard-item:hover {
  background-color: rgba(46, 125, 50, 0.1);
}

/* Review Stars */
.star-rating {
  color: #ffc107;
  font-size: 1.2rem;
}

/* Resource Sharing */
.resource-card {
  border-left: 4px solid var(--primary-color);
}

/* Learning Hub */
.course-card {
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.course-card:hover {
  transform: translateY(-5px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .feature-card {
    margin-bottom: 1.5rem;
  }
  
  .map-container {
    height: 300px;
  }
}
