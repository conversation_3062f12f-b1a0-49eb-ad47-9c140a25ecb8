const express = require('express');
const {
  getIssues,
  getIssue,
  createIssue,
  updateIssue,
  deleteIssue,
  addStatusUpdate
} = require('../controllers/issueController');

const router = express.Router();

const { protect, authorize } = require('../middleware/auth');
const upload = require('../middleware/upload');

router
  .route('/')
  .get(getIssues)
  .post(protect, upload.single('photo'), createIssue);

router
  .route('/:id')
  .get(getIssue)
  .put(protect, updateIssue)
  .delete(protect, deleteIssue);

router.route('/:id/status').post(protect, authorize('admin'), addStatusUpdate);

module.exports = router;
