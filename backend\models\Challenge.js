const mongoose = require('mongoose');

const ChallengeSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: ['Waste Reduction', 'Transportation', 'Energy', 'Water', 'Food', 'Other']
  },
  image: {
    type: String
  },
  startDate: {
    type: Date,
    required: [true, 'Please add a start date']
  },
  endDate: {
    type: Date,
    required: [true, 'Please add an end date']
  },
  points: {
    type: Number,
    required: [true, 'Please add points value'],
    default: 100
  },
  tasks: [
    {
      title: {
        type: String,
        required: true
      },
      description: {
        type: String
      },
      points: {
        type: Number,
        default: 10
      }
    }
  ],
  participants: [
    {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      progress: {
        type: Number,
        default: 0
      },
      completedTasks: [
        {
          taskId: {
            type: mongoose.Schema.Types.ObjectId
          },
          completedAt: {
            type: Date,
            default: Date.now
          }
        }
      ],
      joinedAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Virtual for days left
ChallengeSchema.virtual('daysLeft').get(function() {
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = Math.abs(end - now);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return end > now ? diffDays : 0;
});

// Virtual for participant count
ChallengeSchema.virtual('participantCount').get(function() {
  return this.participants.length;
});

// Enable virtuals
ChallengeSchema.set('toJSON', { virtuals: true });
ChallengeSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Challenge', ChallengeSchema);
