import React, { useState, useRef } from 'react';
import { Con<PERSON>er, Row, Col, Card, Form, But<PERSON>, Spinner, Alert } from 'react-bootstrap';
import { FaRecycle, FaInfoCircle, FaLightbulb } from 'react-icons/fa';
import geminiService from '../services/geminiService';

const AIAssistantPage = () => {
  const [wasteDescription, setWasteDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const resultsRef = useRef(null);

  // Helper function to parse the response into idea objects
  const parseIdeasFromResponse = (response) => {
    try {
      console.log('Parsing response:', response);

      // Split the response by numbered items (1., 2., etc.)
      const ideas = [];
      const sections = response.split(/\d+\.\s+/).filter(Boolean);

      for (const section of sections) {
        // Try to extract title and content
        const titleMatch = section.match(/^([^:]+):/);

        if (titleMatch) {
          const title = titleMatch[1].trim();
          const content = section.substring(titleMatch[0].length).trim();
          ideas.push({ title, content });
        } else {
          // If no title format found, use the first line as title
          const lines = section.split('\n').filter(line => line.trim());
          if (lines.length > 0) {
            const title = lines[0].trim();
            const content = lines.slice(1).join('\n').trim();
            ideas.push({ title, content });
          }
        }
      }

      return ideas;
    } catch (error) {
      console.error('Error parsing ideas:', error);
      return [];
    }
  };

  // Get realistic ideas as a fallback
  const getMockIdeas = (materials) => {
    return [
      {
        title: "Urban Apartment Vertical Growing System",
        content: `Transform ${materials} into a space-efficient vertical growing system designed specifically for urban apartments with limited outdoor access. Create a modular wall-mounted structure with proper drainage solutions, integrated LED grow lights for low-light environments, and a self-watering reservoir system. Perfect for growing culinary herbs, microgreens, and compact vegetables year-round regardless of outdoor conditions.`
      },
      {
        title: "Customized Home Office Organization System",
        content: `Repurpose ${materials} into a tailored organization system for the modern home office. Design cable management solutions, monitor stands with integrated storage, document sorting systems, and desk accessories that maximize productivity in small workspaces. Incorporate adjustable components to adapt to changing work needs and technology requirements.`
      },
      {
        title: "Multi-functional Furniture for Small Spaces",
        content: `Create space-saving furniture from ${materials} that serves multiple purposes in compact urban living environments. Design storage ottomans, fold-down desks, modular shelving, or convertible tables that can be reconfigured based on immediate needs. Focus on lightweight yet durable construction methods and aesthetically pleasing finishes that complement contemporary interior design.`
      },
      {
        title: "Urban Balcony Rainwater Harvesting System",
        content: `Design a compact rainwater collection and filtration system using ${materials} specifically for apartment balconies or small outdoor spaces. Create a multi-stage filtration process, space-efficient storage solutions, and an automated distribution system for container plants. Include monitoring capabilities to track water savings and usage patterns to optimize conservation efforts.`
      },
      {
        title: "Neighborhood Biodiversity Enhancement Kit",
        content: `Develop a comprehensive urban wildlife support system from ${materials} that can be implemented in small city spaces. Include pollinator habitats for native bees, bird nesting structures appropriate for urban species, butterfly shelters, and educational components that connect to a community science monitoring program. Design the system to be visually appealing and serve as a conversation starter about urban ecology.`
      }
    ];
  };

  // Generate reuse ideas
  const generateReuseIdeas = async () => {
    if (!wasteDescription.trim()) {
      setError('Please describe your waste materials');
      return;
    }

    setError('');
    setLoading(true);

    try {
      console.log('Generating ideas for:', wasteDescription);

      // Create the prompt for Gemini
      const prompt = `I have the following waste materials: ${wasteDescription}.
      Please generate 5 innovative and practical ideas for reusing these materials.
      For each idea, provide a title and a detailed description of how to implement it.
      Focus on environmentally friendly solutions that are feasible for an average person to do at home.`;

      // Try to get response from Gemini API
      let response;
      try {
        console.log('Sending request to Gemini API...');
        response = await geminiService.getResponse(prompt);
        console.log('Received response from Gemini API:', response);
      } catch (apiError) {
        console.error('Gemini API error:', apiError);
        throw new Error('API request failed');
      }

      // Parse the response into idea objects
      const ideas = parseIdeasFromResponse(response);

      if (ideas.length === 0) {
        console.error('Failed to parse ideas from response');
        throw new Error('Failed to parse ideas');
      }

      console.log('Successfully parsed ideas:', ideas);
      setResults(ideas);

      // Scroll to results
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } catch (error) {
      console.error('Error generating reuse ideas:', error);

      // Use mock data as fallback
      console.log('Using mock data as fallback');
      const mockIdeas = getMockIdeas(wasteDescription);
      setResults(mockIdeas);

      // Still scroll to results since we're showing fallback data
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } finally {
      setLoading(false);
    }
  };

  // Clear the form
  const clearForm = () => {
    setWasteDescription('');
    setResults([]);
    setError('');
  };

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Reuse</h1>
          <p className="lead">
            Get innovative ideas for reusing waste materials and reduce your environmental impact.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-success bg-opacity-10 p-3 rounded">
            <FaRecycle className="text-success fs-1" />
          </div>
        </Col>
      </Row>

      <Row>
        <Col lg={8}>
          <Card className="border-0 shadow-sm mb-4">
            <Card.Body className="p-4">
              <h4 className="mb-4">Waste Material Reuse Ideas Generator</h4>

              {error && (
                <Alert variant="danger" className="mb-4">
                  {error}
                </Alert>
              )}

              <Form>
                <Form.Group className="mb-4">
                  <Form.Label>Describe your waste materials:</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    placeholder="Example: plastic bottles, cardboard boxes, old newspapers..."
                    value={wasteDescription}
                    onChange={(e) => setWasteDescription(e.target.value)}
                    disabled={loading}
                  />
                </Form.Group>

                <div className="d-flex gap-2">
                  <Button
                    variant="success"
                    onClick={generateReuseIdeas}
                    disabled={loading || !wasteDescription.trim()}
                    className="d-flex align-items-center"
                  >
                    {loading ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        Generating Ideas...
                      </>
                    ) : (
                      <>
                        <FaRecycle className="me-2" />
                        Generate Ideas
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline-secondary"
                    onClick={clearForm}
                    disabled={loading}
                  >
                    Clear
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>

          {results.length > 0 && (
            <div ref={resultsRef}>
              <h4 className="mb-3">Reuse Ideas for Your Materials</h4>
              {results.map((idea, index) => (
                <Card key={index} className="border-0 shadow-sm mb-4">
                  <Card.Body className="p-4">
                    <h5 className="mb-3">{index + 1}. {idea.title}</h5>
                    <p className="mb-0">{idea.content}</p>
                  </Card.Body>
                </Card>
              ))}
            </div>
          )}
        </Col>

        <Col lg={4}>
          <Card className="border-0 shadow-sm mb-4">
            <Card.Body>
              <h5 className="d-flex align-items-center mb-3">
                <FaInfoCircle className="text-success me-2" />
                About Reuse
              </h5>
              <p>
                Reusing waste materials is a key component of sustainable living. It helps:
              </p>
              <ul className="mb-0">
                <li>Reduce landfill waste</li>
                <li>Conserve natural resources</li>
                <li>Save energy and reduce pollution</li>
                <li>Decrease your carbon footprint</li>
                <li>Save money by repurposing items</li>
              </ul>
            </Card.Body>
          </Card>

          <Card className="border-0 shadow-sm">
            <Card.Body>
              <h5 className="d-flex align-items-center mb-3">
                <FaLightbulb className="text-warning me-2" />
                Suggested Materials
              </h5>
              <ul className="mb-0">
                <li>Plastic bottles and containers</li>
                <li>Glass jars and bottles</li>
                <li>Cardboard boxes and tubes</li>
                <li>Old clothing and fabric scraps</li>
                <li>Newspaper and magazines</li>
                <li>Wood pallets and scrap wood</li>
                <li>Metal cans and containers</li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default AIAssistantPage;
