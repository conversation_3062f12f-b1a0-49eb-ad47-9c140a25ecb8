<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Successful - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .success-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .success-icon {
            width: 80px;
            height: 80px;
            background-color: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }
        .success-icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        .spinner {
            width: 3rem;
            height: 3rem;
            margin: 1.5rem auto;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
        </div>
        <h2 class="mb-3">Login Successful!</h2>
        <p class="mb-4">You have been successfully logged in to Urban Pulse.</p>
        <div class="spinner-border text-primary spinner" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p>Redirecting to your dashboard...</p>
    </div>

    <script>
        // Check if user data exists in localStorage
        function checkUserData() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');

            if (token && user) {
                try {
                    // Parse user data
                    const userData = JSON.parse(user);
                    console.log('User data found:', userData);
                    return true;
                } catch (error) {
                    console.error('Error parsing user data:', error);
                    return false;
                }
            }
            return false;
        }

        // Redirect to appropriate page
        function redirectUser() {
            if (checkUserData()) {
                // User is logged in, redirect to our refresh page
                window.location.href = '/public/refresh-auth.html';
            } else {
                // No user data found, redirect to login
                window.location.href = '/public/direct-login.html';
            }
        }

        // Wait a moment to show the success message, then redirect
        setTimeout(redirectUser, 2000);
    </script>
</body>
</html>
