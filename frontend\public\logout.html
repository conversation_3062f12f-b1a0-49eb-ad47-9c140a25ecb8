<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logging Out - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logout-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .logout-icon {
            width: 80px;
            height: 80px;
            background-color: #0d6efd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }
        .logout-icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        .spinner {
            width: 3rem;
            height: 3rem;
            margin: 1.5rem auto;
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
        </div>
        <h2 class="mb-3">Logging Out</h2>
        <p class="mb-4">Please wait while we log you out of Urban Pulse.</p>
        <div class="spinner-border text-primary spinner" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <script>
        // Clear authentication data
        function logout() {
            // Clear localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            // Redirect to home page
            setTimeout(() => {
                window.location.href = '/';
            }, 1500);
        }
        
        // Execute logout
        logout();
    </script>
</body>
</html>
