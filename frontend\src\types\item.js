/**
 * @typedef {'Tools' | 'Books' | 'Kitchen' | 'Electronics' | 'Sports' | 'Garden' | 'Toys' | 'Music' | 'Other'} ItemCategory
 */

/**
 * @typedef {'Available' | 'Borrowed' | 'Sold'} ItemAvailability
 */

/**
 * @typedef {'Exchange' | 'Sell' | 'Both'} ItemTransactionType
 */

/**
 * @typedef {Object} Item
 * @property {string} id
 * @property {string} title
 * @property {ItemCategory} category
 * @property {string} description
 * @property {string} imageUrl
 * @property {ItemAvailability} availability
 * @property {ItemTransactionType} transactionType
 * @property {number} [price]
 * @property {string} ownerId
 * @property {string} ownerName
 * @property {string} [ownerEmail]
 * @property {string} location
 * @property {string} createdAt
 * @property {Object} [coordinates]
 * @property {number} [coordinates.latitude]
 * @property {number} [coordinates.longitude]
 */

// This file provides JSDoc type definitions for JavaScript
