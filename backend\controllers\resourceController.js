const Resource = require('../models/Resource');
const updateUserStats = require('../utils/updateUserStats');

// @desc    Get all resources
// @route   GET /api/resources
// @access  Public
exports.getResources = async (req, res, next) => {
  try {
    let query;

    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = ['select', 'sort', 'page', 'limit'];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach(param => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

    // Finding resource
    query = Resource.find(JSON.parse(queryStr)).populate('owner', 'name');

    // Select Fields
    if (req.query.select) {
      const fields = req.query.select.split(',').join(' ');
      query = query.select(fields);
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const total = await Resource.countDocuments();

    query = query.skip(startIndex).limit(limit);

    // Executing query
    const resources = await query;

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }

    res.status(200).json({
      success: true,
      count: resources.length,
      pagination,
      data: resources
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get single resource
// @route   GET /api/resources/:id
// @access  Public
exports.getResource = async (req, res, next) => {
  try {
    const resource = await Resource.findById(req.params.id).populate('owner', 'name');

    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Resource not found'
      });
    }

    res.status(200).json({
      success: true,
      data: resource
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Create new resource
// @route   POST /api/resources
// @access  Private
exports.createResource = async (req, res, next) => {
  try {
    // Add user to req.body
    req.body.owner = req.user.id;

    const resource = await Resource.create(req.body);

    // Update user stats
    await updateUserStats(req.user.id, 'resource', resource);

    res.status(201).json({
      success: true,
      data: resource
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Update resource
// @route   PUT /api/resources/:id
// @access  Private
exports.updateResource = async (req, res, next) => {
  try {
    let resource = await Resource.findById(req.params.id);

    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Resource not found'
      });
    }

    // Make sure user is resource owner
    if (resource.owner.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update this resource'
      });
    }

    resource = await Resource.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    // If status was updated, emit socket event
    if (req.body.status && req.body.status !== resource.status) {
      const socket = require('../config/socket');
      socket.emitResourceStatusUpdate(resource._id, {
        resourceId: resource._id,
        status: req.body.status,
        title: resource.title,
        ownerId: req.user.id,
        ownerName: req.user.name
      });
    }

    res.status(200).json({
      success: true,
      data: resource
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Delete resource
// @route   DELETE /api/resources/:id
// @access  Private
exports.deleteResource = async (req, res, next) => {
  try {
    const resource = await Resource.findById(req.params.id);

    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Resource not found'
      });
    }

    // Make sure user is resource owner
    if (resource.owner.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to delete this resource'
      });
    }

    await resource.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Request to borrow a resource
// @route   POST /api/resources/:id/request
// @access  Private
exports.requestResource = async (req, res, next) => {
  try {
    const { startDate, endDate, message } = req.body;

    const resource = await Resource.findById(req.params.id);

    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Resource not found'
      });
    }

    // Check if user is the owner
    if (resource.owner.toString() === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'You cannot request your own resource'
      });
    }

    // Check if user already has a pending request
    const existingRequest = resource.requests.find(
      request => request.user.toString() === req.user.id && request.status === 'Pending'
    );

    if (existingRequest) {
      return res.status(400).json({
        success: false,
        error: 'You already have a pending request for this resource'
      });
    }

    // Add request
    resource.requests.push({
      user: req.user.id,
      startDate,
      endDate,
      message,
      status: 'Pending'
    });

    await resource.save();

    res.status(200).json({
      success: true,
      data: resource
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Respond to a resource request
// @route   PUT /api/resources/:id/request/:requestId
// @access  Private
exports.respondToRequest = async (req, res, next) => {
  try {
    const { status } = req.body;

    if (!['Approved', 'Rejected', 'Completed'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status'
      });
    }

    const resource = await Resource.findById(req.params.id);

    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Resource not found'
      });
    }

    // Make sure user is resource owner
    if (resource.owner.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to respond to requests for this resource'
      });
    }

    // Find the request
    const requestIndex = resource.requests.findIndex(
      request => request._id.toString() === req.params.requestId
    );

    if (requestIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Request not found'
      });
    }

    // Update request status
    resource.requests[requestIndex].status = status;

    // If approved, update availability calendar
    if (status === 'Approved') {
      const { startDate, endDate } = resource.requests[requestIndex];
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Loop through dates and mark as unavailable
      for (let day = new Date(start); day <= end; day.setDate(day.getDate() + 1)) {
        resource.availabilityCalendar.push({
          date: new Date(day),
          isAvailable: false
        });
      }
    }

    await resource.save();

    res.status(200).json({
      success: true,
      data: resource
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get user's shared resources
// @route   GET /api/resources/my-resources
// @access  Private
exports.getMyResources = async (req, res, next) => {
  try {
    const resources = await Resource.find({ owner: req.user.id });

    res.status(200).json({
      success: true,
      count: resources.length,
      data: resources
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get user's borrowed resources
// @route   GET /api/resources/borrowed
// @access  Private
exports.getBorrowedResources = async (req, res, next) => {
  try {
    const resources = await Resource.find({
      'requests.user': req.user.id,
      'requests.status': { $in: ['Approved', 'Completed'] }
    }).populate('owner', 'name');

    // Filter to only include the user's requests
    const borrowedResources = resources.map(resource => {
      const userRequests = resource.requests.filter(
        request => request.user.toString() === req.user.id &&
                  ['Approved', 'Completed'].includes(request.status)
      );

      return {
        _id: resource._id,
        title: resource.title,
        category: resource.category,
        owner: resource.owner,
        requests: userRequests
      };
    });

    res.status(200).json({
      success: true,
      count: borrowedResources.length,
      data: borrowedResources
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Update resource status
// @route   PUT /api/resources/:id/status
// @access  Private (Owner only)
exports.updateResourceStatus = async (req, res, next) => {
  try {
    const { status } = req.body;

    if (!status || !['Available', 'Sold'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status'
      });
    }

    let resource = await Resource.findById(req.params.id);

    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Resource not found'
      });
    }

    // Make sure user is resource owner
    if (resource.owner.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update this resource status'
      });
    }

    // Update the status
    resource.status = status;
    await resource.save();

    // Emit socket event for real-time updates
    const socket = require('../config/socket');
    socket.emitResourceStatusUpdate(resource._id, {
      resourceId: resource._id,
      status,
      title: resource.title,
      ownerId: req.user.id,
      ownerName: req.user.name
    });

    res.status(200).json({
      success: true,
      data: resource
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Add message to resource
// @route   POST /api/resources/:id/messages
// @access  Private
exports.addResourceMessage = async (req, res, next) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Message content is required'
      });
    }

    const resource = await Resource.findById(req.params.id).populate('owner', 'name');

    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Resource not found'
      });
    }

    // Create the message
    const message = {
      user: req.user.id,
      userName: req.user.name,
      content,
      createdAt: new Date(),
      isOwner: resource.owner._id.toString() === req.user.id
    };

    // Add message to resource
    resource.messages.push(message);
    await resource.save();

    // Emit socket event for real-time updates
    const socket = require('../config/socket');
    socket.emitResourceMessage(resource._id, {
      resourceId: resource._id,
      message,
      resourceTitle: resource.title,
      ownerId: resource.owner._id,
      ownerName: resource.owner.name
    });

    res.status(201).json({
      success: true,
      data: message
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};
