import api from '../utils/api';

// Get all challenges
export const getChallenges = async (params) => {
  try {
    const res = await api.get('/challenges', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch challenges');
  }
};

// Get challenge by ID
export const getChallengeById = async (id) => {
  try {
    const res = await api.get(`/challenges/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch challenge');
  }
};

// Create new challenge
export const createChallenge = async (challengeData) => {
  try {
    const res = await api.post('/challenges', challengeData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create challenge');
  }
};

// Update challenge
export const updateChallenge = async (id, challengeData) => {
  try {
    const res = await api.put(`/challenges/${id}`, challengeData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update challenge');
  }
};

// Delete challenge
export const deleteChallenge = async (id) => {
  try {
    const res = await api.delete(`/challenges/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete challenge');
  }
};

// Join challenge
export const joinChallenge = async (id) => {
  try {
    const res = await api.post(`/challenges/${id}/join`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to join challenge');
  }
};

// Leave challenge
export const leaveChallenge = async (id) => {
  try {
    const res = await api.delete(`/challenges/${id}/join`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to leave challenge');
  }
};

// Complete task
export const completeTask = async (challengeId, taskId) => {
  try {
    const res = await api.post(`/challenges/${challengeId}/tasks/${taskId}/complete`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to complete task');
  }
};

// Schedule task
export const scheduleTask = async (challengeId, taskId, date) => {
  try {
    const res = await api.post(`/challenges/${challengeId}/tasks/${taskId}/schedule`, { date });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to schedule task');
  }
};

// Get leaderboard
export const getLeaderboard = async () => {
  try {
    const res = await api.get('/challenges/leaderboard');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch leaderboard');
  }
};

// Get user's challenges
export const getMyChallenges = async () => {
  try {
    const res = await api.get('/challenges/my-challenges');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch your challenges');
  }
};
