const express = require('express');
const { getSpaceEvents } = require('../controllers/eventController');

// Include other controllers
// const { getSpaces, getSpace, createSpace, updateSpace, deleteSpace } = require('../controllers/spaceController');

const router = express.Router();

const { protect } = require('../middleware/auth');

// Re-route into other resource routers
router.use('/:spaceId/events', getSpaceEvents);

// router
//   .route('/')
//   .get(getSpaces)
//   .post(protect, authorize('admin'), createSpace);

// router
//   .route('/:id')
//   .get(getSpace)
//   .put(protect, authorize('admin'), updateSpace)
//   .delete(protect, authorize('admin'), deleteSpace);

module.exports = router;
