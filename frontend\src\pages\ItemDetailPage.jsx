import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Badge, Button, Form, Alert, Modal, Toast, ToastContainer, Tabs, Tab } from 'react-bootstrap';
import { FaArrowLeft, FaUser, FaMapMarkerAlt, FaCalendarAlt, FaExchangeAlt, FaTag, FaHeart, FaComments, FaCheckCircle } from 'react-icons/fa';
import { mockItems } from '../data/mockItems';
import { useAuth } from '../contexts/AuthContext';
import notificationService from '../services/notificationService';
import socketService from '../services/socketService';
import ResourceChat from '../components/resource-sharing/ResourceChat';

const ItemDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [item, setItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [requestMessage, setRequestMessage] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [requestSuccess, setRequestSuccess] = useState(false);
  const [showInterestToast, setShowInterestToast] = useState(false);
  const [hasExpressedInterest, setHasExpressedInterest] = useState(false);

  // State for status update modal
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [statusUpdateSuccess, setStatusUpdateSuccess] = useState(false);

  // State for active tab
  const [activeTab, setActiveTab] = useState('details');

  // State for status update toast
  const [showStatusToast, setShowStatusToast] = useState(false);

  useEffect(() => {
    // In a real app, this would be an API call
    const fetchItem = () => {
      setLoading(true);
      try {
        const foundItem = mockItems.find(item => item.id === id);
        if (foundItem) {
          setItem(foundItem);

          // Set initial status
          if (foundItem.status) {
            setNewStatus(foundItem.status);
          } else {
            setNewStatus('Available');
          }
        } else {
          setError('Item not found');
        }
      } catch (err) {
        setError('Failed to load item details');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchItem();
  }, [id]);

  // Set up socket connection and event listeners
  useEffect(() => {
    if (!item) return;

    // Connect to socket if not already connected
    if (!socketService.isConnected) {
      socketService.connect();
    }

    // Join resource room to receive updates
    socketService.joinResourceRoom(id);

    // Listen for resource status updates
    const statusUnsubscribe = socketService.on('resource-status-update', handleStatusUpdateFromSocket);

    // Clean up when component unmounts
    return () => {
      statusUnsubscribe();
    };
  }, [item, id]);

  // Handle resource status updates from socket
  const handleStatusUpdateFromSocket = (data) => {
    if (data.resourceId === id) {
      // Update the item status
      setItem(prevItem => ({
        ...prevItem,
        status: data.status
      }));

      // Show toast notification
      setShowStatusToast(true);

      // Show browser notification
      notificationService.showResourceStatusUpdate({
        resourceId: id,
        title: item.title,
        status: data.status
      });
    }
  };

  const handleRequestSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would be an API call
    setTimeout(() => {
      setRequestSuccess(true);
      setTimeout(() => {
        setShowRequestModal(false);
        setRequestSuccess(false);
        setRequestMessage('');
      }, 2000);
    }, 1000);
  };

  const handleExpressInterest = () => {
    if (!currentUser) {
      // Redirect to login if no user is logged in
      navigate('/login', { state: { from: `/resource-sharing/items/${id}` } });
      return;
    }

    // Check if the current user is the owner
    if (item.ownerId === currentUser.id) {
      alert("You cannot express interest in your own item");
      return;
    }

    // In a real app, this would be an API call to record the interest
    // For now, we'll just show a notification to simulate the functionality

    // Create interest notification data
    const interestData = {
      userId: currentUser.id,
      userName: currentUser.name || 'A user',
      resourceId: item.id,
      resourceTitle: item.title,
      ownerId: item.ownerId,
      timestamp: new Date(),
      message: `I'm interested in your ${item.title}. Is it still available?`
    };

    // Send notification to the item owner
    notificationService.showResourceInterest(interestData);

    // Add to notification service for in-app display
    notificationService.addNotification({
      title: `Interest in ${item.title}`,
      message: `You expressed interest in ${item.title}`,
      type: 'interest',
      url: `/resource-sharing/items/${item.id}?tab=chat`,
      read: true // Mark as read for the sender
    });

    // Emit socket event for real-time notification
    socketService.emitResourceInterest(item.id, currentUser.name || 'A user', interestData);

    // Show success toast to the user
    setHasExpressedInterest(true);
    setShowInterestToast(true);

    // Switch to chat tab
    setActiveTab('chat');

    // Add initial message to chat
    if (item.messages) {
      // In a real app, this would be an API call
      // For now, we'll just simulate adding a message
      socketService.emitResourceMessage(
        item.id,
        interestData.message,
        false
      );
    }
  };

  // Handle status update
  const handleStatusUpdate = () => {
    if (!currentUser) {
      // Redirect to login if no user is logged in
      navigate('/login', { state: { from: `/resource-sharing/items/${id}` } });
      return;
    }

    // Check if the current user is the owner
    if (item.ownerId !== currentUser.id) {
      alert("Only the owner can update the status of this item");
      return;
    }

    // Show status update modal
    setShowStatusModal(true);
  };

  // Handle status update submission
  const handleStatusUpdateSubmit = async (e) => {
    e.preventDefault();

    if (!newStatus) return;

    try {
      // In a real app, this would be an API call
      // await api.put(`/resources/${id}/status`, { status: newStatus });

      // For demo purposes, update the item locally
      setItem(prevItem => ({
        ...prevItem,
        status: newStatus
      }));

      // Emit socket event for real-time updates
      socketService.emitResourceStatusUpdate(id, newStatus);

      // Show success message
      setStatusUpdateSuccess(true);

      // Close modal after a delay
      setTimeout(() => {
        setShowStatusModal(false);
        setStatusUpdateSuccess(false);
      }, 1500);
    } catch (err) {
      console.error('Error updating status:', err);
      alert('Failed to update status. Please try again.');
    }
  };

  if (loading) {
    return (
      <Container className="py-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading item details...</p>
        </div>
      </Container>
    );
  }

  if (error || !item) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          {error || 'Item not found'}
        </Alert>
        <Button variant="primary" as={Link} to="/resource-sharing">
          <FaArrowLeft className="me-2" />
          Back to Resources
        </Button>
      </Container>
    );
  }

  return (
    <Container className="py-5">
      <Button
        variant="outline-primary"
        className="mb-4"
        onClick={() => navigate('/resource-sharing')}
      >
        <FaArrowLeft className="me-2" />
        Back to Resources
      </Button>

      <Row>
        <Col lg={6} className="mb-4">
          <img
            src={item.imageUrl}
            alt={item.title}
            className="img-fluid rounded shadow-sm"
            style={{ maxHeight: '400px', width: '100%', objectFit: 'cover' }}
          />
        </Col>

        <Col lg={6}>
          <Card className="border-0 shadow-sm">
            <Card.Body className="p-4">
              <div className="d-flex justify-content-between align-items-start mb-3">
                <div>
                  <h1 className="h2 mb-2">{item.title}</h1>
                  <div className="d-flex align-items-center">
                    <Badge bg="primary" className="me-2">{item.category}</Badge>
                    <Badge
                      bg={item.status === 'Sold' ? 'danger' : 'success'}
                      className="me-2"
                    >
                      {item.status || item.availability || 'Available'}
                    </Badge>

                    {/* Status Update Button (Only visible to owner) */}
                    {currentUser && item.ownerId === currentUser.id && (
                      <Button
                        variant="outline-primary"
                        size="sm"
                        className="ms-2"
                        onClick={handleStatusUpdate}
                      >
                        Update Status
                      </Button>
                    )}
                  </div>
                </div>
                {item.price && item.transactionType !== 'Exchange' && (
                  <div className="bg-light p-2 rounded">
                    <span className="h4 mb-0 text-primary">${item.price}</span>
                  </div>
                )}
              </div>

              <p className="lead mb-4">{item.description}</p>

              <div className="mb-4">
                <div className="d-flex align-items-center mb-2">
                  <FaUser className="text-primary me-2" />
                  <span><strong>Owner:</strong> {item.ownerName}</span>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <FaMapMarkerAlt className="text-primary me-2" />
                  <span><strong>Location:</strong> {item.location}</span>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <FaCalendarAlt className="text-primary me-2" />
                  <span><strong>Posted:</strong> {new Date(item.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <FaExchangeAlt className="text-primary me-2" />
                  <span><strong>Transaction Type:</strong> {item.transactionType}</span>
                </div>
              </div>

              {item.availability === 'Available' && (
                <div className="d-grid gap-2">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => setShowRequestModal(true)}
                  >
                    Request This Item
                  </Button>

                  <Button
                    variant={hasExpressedInterest ? "success" : "outline-danger"}
                    size="lg"
                    onClick={handleExpressInterest}
                    disabled={hasExpressedInterest}
                    className="d-flex align-items-center justify-content-center"
                  >
                    <FaHeart className="me-2" />
                    {hasExpressedInterest ? "Interest Expressed" : "I'm Interested"}
                  </Button>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Interest Toast */}
      <ToastContainer position="bottom-end" className="p-3" style={{ zIndex: 1060 }}>
        <Toast
          show={showInterestToast}
          onClose={() => setShowInterestToast(false)}
          delay={5000}
          autohide
          bg="success"
          text="white"
        >
          <Toast.Header closeButton>
            <strong className="me-auto">Interest Expressed</strong>
          </Toast.Header>
          <Toast.Body>
            The owner of {item?.title} has been notified of your interest. They may contact you soon!
          </Toast.Body>
        </Toast>
      </ToastContainer>

      {/* Request Modal */}
      <Modal show={showRequestModal} onHide={() => setShowRequestModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Request "{item.title}"</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {requestSuccess ? (
            <Alert variant="success">
              Your request has been sent successfully! The owner will contact you soon.
            </Alert>
          ) : (
            <Form onSubmit={handleRequestSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={startDate.toISOString().split('T')[0]}
                  onChange={(e) => setStartDate(new Date(e.target.value))}
                  required
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={endDate.toISOString().split('T')[0]}
                  onChange={(e) => setEndDate(new Date(e.target.value))}
                  required
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Message to Owner</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  placeholder="Introduce yourself and explain why you need this item..."
                  value={requestMessage}
                  onChange={(e) => setRequestMessage(e.target.value)}
                  required
                />
              </Form.Group>
              <div className="d-grid">
                <Button variant="primary" type="submit">
                  Send Request
                </Button>
              </div>
            </Form>
          )}
        </Modal.Body>
      </Modal>

      {/* Status Update Modal */}
      <Modal show={showStatusModal} onHide={() => setShowStatusModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Update Item Status</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {statusUpdateSuccess ? (
            <Alert variant="success">
              Status updated successfully!
            </Alert>
          ) : (
            <Form onSubmit={handleStatusUpdateSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>Current Status</Form.Label>
                <Form.Control
                  type="text"
                  value={item.status || item.availability || 'Available'}
                  disabled
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>New Status</Form.Label>
                <Form.Select
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                  required
                >
                  <option value="">Select Status</option>
                  <option value="Available">Available</option>
                  <option value="Sold">Sold</option>
                </Form.Select>
              </Form.Group>
              <div className="d-grid">
                <Button variant="primary" type="submit">
                  Update Status
                </Button>
              </div>
            </Form>
          )}
        </Modal.Body>
      </Modal>

      {/* Status Update Toast */}
      <ToastContainer position="bottom-end" className="p-3" style={{ zIndex: 1060 }}>
        <Toast
          show={showStatusToast}
          onClose={() => setShowStatusToast(false)}
          delay={5000}
          autohide
          bg={item?.status === 'Sold' ? 'danger' : 'success'}
          text="white"
        >
          <Toast.Header closeButton>
            <strong className="me-auto">Status Updated</strong>
          </Toast.Header>
          <Toast.Body>
            The status of {item?.title} has been updated to {item?.status || 'Available'}.
          </Toast.Body>
        </Toast>
      </ToastContainer>

      {/* Chat Section */}
      <Row className="mt-4">
        <Col>
          <Card className="border-0 shadow-sm">
            <Card.Body>
              <Tabs
                activeKey={activeTab}
                onSelect={(k) => setActiveTab(k)}
                className="mb-4"
              >
                <Tab eventKey="details" title="Details">
                  <h4>Additional Details</h4>
                  <p>
                    This item was posted on {new Date(item.createdAt).toLocaleDateString()} by {item.ownerName}.
                  </p>
                  <p>
                    If you're interested in this item, you can express your interest or send a message to the owner.
                  </p>
                </Tab>
                <Tab eventKey="chat" title={<span><FaComments className="me-2" />Chat with Owner</span>}>
                  <ResourceChat
                    resourceId={item.id}
                    resourceTitle={item.title}
                    ownerId={item.ownerId}
                    ownerName={item.ownerName}
                  />
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ItemDetailPage;
