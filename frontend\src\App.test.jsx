import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import App from './App';

// Mock components to simplify testing
vi.mock('./components/layout/Header', () => ({
  default: () => <div data-testid="header">Header</div>
}));

vi.mock('./components/layout/Footer', () => ({
  default: () => <div data-testid="footer">Footer</div>
}));

vi.mock('./pages/HomePage', () => ({
  default: () => <div data-testid="home-page">Home Page</div>
}));

describe('App Component', () => {
  it('renders header and footer', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <App />
        </AuthProvider>
      </BrowserRouter>
    );
    
    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });
  
  it('renders home page by default', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <App />
        </AuthProvider>
      </BrowserRouter>
    );
    
    expect(screen.getByTestId('home-page')).toBeInTheDocument();
  });
});
