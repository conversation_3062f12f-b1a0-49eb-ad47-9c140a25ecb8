// This script forcibly updates the navbar based on authentication state
(function() {
    console.log('Force navbar update script loaded');

    // Function to check if user is logged in
    function isUserLoggedIn() {
        const token = localStorage.getItem('token');
        const userDataString = localStorage.getItem('user');

        if (token && userDataString) {
            try {
                // Parse user data
                const userData = JSON.parse(userDataString);
                console.log('User is logged in as:', userData.name || 'User');
                return {
                    isLoggedIn: true,
                    userData: userData
                };
            } catch (error) {
                console.error('Error parsing user data:', error);
                return {
                    isLoggedIn: false,
                    userData: null
                };
            }
        }

        return {
            isLoggedIn: false,
            userData: null
        };
    }

    // Function to create authenticated navbar elements
    function createAuthenticatedNavElements(userData) {
        // Create dashboard link
        const dashboardLink = document.createElement('a');
        dashboardLink.className = 'nav-link';
        dashboardLink.href = '/dashboard';
        // Add event listener to prevent default behavior and use history API
        dashboardLink.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = '/dashboard';
        });
        dashboardLink.textContent = 'My Dashboard';

        // Create profile dropdown
        const dropdownContainer = document.createElement('div');
        dropdownContainer.className = 'dropdown';

        const dropdownToggle = document.createElement('a');
        dropdownToggle.className = 'nav-link dropdown-toggle';
        dropdownToggle.href = '#';
        dropdownToggle.id = 'profile-dropdown';
        dropdownToggle.setAttribute('role', 'button');
        dropdownToggle.setAttribute('data-bs-toggle', 'dropdown');
        dropdownToggle.setAttribute('aria-expanded', 'false');
        dropdownToggle.textContent = userData.name || 'Profile';

        const dropdownMenu = document.createElement('ul');
        dropdownMenu.className = 'dropdown-menu';
        dropdownMenu.setAttribute('aria-labelledby', 'profile-dropdown');

        const profileItem = document.createElement('li');
        const profileLink = document.createElement('a');
        profileLink.className = 'dropdown-item';
        profileLink.href = '/profile';
        // Add event listener to prevent default behavior and use history API
        profileLink.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = '/profile';
        });
        profileLink.textContent = 'My Profile';
        profileItem.appendChild(profileLink);

        const divider = document.createElement('li');
        divider.innerHTML = '<hr class="dropdown-divider">';

        const logoutItem = document.createElement('li');
        const logoutLink = document.createElement('a');
        logoutLink.className = 'dropdown-item';
        logoutLink.href = '#';
        logoutLink.textContent = 'Logout';
        logoutLink.addEventListener('click', function(e) {
            e.preventDefault();
            // Clear auth data
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            // Redirect to home
            window.location.href = '/';
        });
        logoutItem.appendChild(logoutLink);

        dropdownMenu.appendChild(profileItem);
        dropdownMenu.appendChild(divider);
        dropdownMenu.appendChild(logoutItem);

        dropdownContainer.appendChild(dropdownToggle);
        dropdownContainer.appendChild(dropdownMenu);

        return {
            dashboardLink: dashboardLink,
            profileDropdown: dropdownContainer
        };
    }

    // Function to check if we're in a React application
    function isReactApp() {
        // Check for React-specific elements or attributes
        return (
            document.getElementById('root') !== null || // React's default root element
            document.querySelector('[data-reactroot]') !== null || // React attribute
            document.querySelector('[data-reactid]') !== null // Older React attribute
        );
    }

    // Function to update the navbar
    function updateNavbar() {
        const { isLoggedIn, userData } = isUserLoggedIn();

        // Check if we're in a React application
        const isReact = isReactApp();
        if (isReact) {
            console.log('React application detected, skipping manual navbar update');
            // For React apps, we'll just dispatch an event to notify about auth state
            const event = new CustomEvent('auth-state-change', {
                detail: { isLoggedIn, userData }
            });
            window.dispatchEvent(event);
            return;
        }

        if (isLoggedIn) {
            console.log('Updating navbar for authenticated user');

            // Find the navbar elements - try multiple selectors to ensure we find the navbar
            const navbarNav = document.querySelector('.navbar-nav') ||
                             document.querySelector('.navbar .nav') ||
                             document.querySelector('nav .nav');

            if (!navbarNav) {
                console.error('Could not find navbar-nav element, will retry later');
                return;
            }

            // Check if we already have the authenticated elements
            const existingDashboard = document.querySelector('a.nav-link[href="/dashboard"]');
            const existingProfileDropdown = document.querySelector('#profile-dropdown');

            if (existingDashboard && existingProfileDropdown) {
                console.log('Authenticated navbar elements already exist');
                return;
            }

            // Find the login/signup elements to replace - try multiple selectors
            const loginLinks = document.querySelectorAll('a[href="/public/direct-login.html"], a[href="/login"], a[href*="login"]');
            const signupButtons = document.querySelectorAll('a[href="/public/register-redirect.html"], a[href="/register"], button[onclick*="/public/register-redirect.html"], button[onclick*="register"]');
            const adminLoginLinks = document.querySelectorAll('a[href="/admin-login"]');

            // Hide login links
            loginLinks.forEach(link => {
                if (link) {
                    console.log('Hiding login link:', link);
                    link.style.display = 'none';
                }
            });

            // Hide signup buttons
            signupButtons.forEach(button => {
                if (button) {
                    console.log('Hiding signup button:', button);
                    button.style.display = 'none';
                }
            });

            // Hide admin login links
            adminLoginLinks.forEach(link => {
                if (link) {
                    console.log('Hiding admin login link:', link);
                    link.style.display = 'none';
                }
            });

            // Create authenticated elements
            const { dashboardLink, profileDropdown } = createAuthenticatedNavElements(userData);

            // Add the new elements to the navbar
            navbarNav.appendChild(dashboardLink);
            navbarNav.appendChild(profileDropdown);

            console.log('Added authenticated elements to navbar');

            // Initialize Bootstrap dropdown
            if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
                dropdownElementList.map(function (dropdownToggleEl) {
                    return new bootstrap.Dropdown(dropdownToggleEl);
                });
                console.log('Initialized Bootstrap dropdowns');
            } else {
                console.warn('Bootstrap not available, dropdowns may not work');

                // Try to load Bootstrap if it's not available
                if (!document.querySelector('script[src*="bootstrap.bundle.min.js"]')) {
                    const bootstrapScript = document.createElement('script');
                    bootstrapScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js';
                    document.body.appendChild(bootstrapScript);
                    console.log('Added Bootstrap script to page');
                }
            }
        } else {
            console.log('User is not logged in, no navbar update needed');
        }
    }

    // Function to initialize the script
    function init() {
        console.log('Initializing force navbar update');

        // Try to update the navbar immediately
        updateNavbar();

        // Set up an interval to check and update the navbar periodically
        // Use a shorter interval initially, then extend it
        let attempts = 0;
        const maxAttempts = 10;

        const checkInterval = setInterval(function() {
            console.log(`Navbar update attempt ${attempts + 1} of ${maxAttempts}`);
            updateNavbar();
            attempts++;

            // After max attempts, slow down the checking to reduce resource usage
            if (attempts >= maxAttempts) {
                console.log('Switching to less frequent navbar checks');
                clearInterval(checkInterval);
                setInterval(updateNavbar, 5000); // Check every 5 seconds after initial attempts
            }
        }, 1000);
    }

    // Wait for the DOM to be fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // If DOM is already loaded, wait a short time to ensure React has rendered
        setTimeout(init, 500);
    }
})();
