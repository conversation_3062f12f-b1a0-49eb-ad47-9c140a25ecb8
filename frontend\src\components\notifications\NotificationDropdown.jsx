import React, { useState, useEffect } from 'react';
import { Dropdown, Badge } from 'react-bootstrap';
import { <PERSON>a<PERSON>ell, FaH<PERSON>t, FaExclamationCircle, FaInfoCircle, FaCheckCircle } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import notificationService from '../../services/notificationService';
import './NotificationDropdown.css';

const NotificationDropdown = () => {
  const [notifications, setNotifications] = useState([]);

  // Get unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  // Listen for notifications
  useEffect(() => {
    // Get initial notifications
    setNotifications(notificationService.getNotifications());

    // Add listener for new notifications
    const unsubscribe = notificationService.addListener((notification) => {
      setNotifications(notificationService.getNotifications());

      // Play a sound for interest notifications
      if (notification && notification.type === 'interest' && !notification.read) {
        try {
          // Create a simple beep sound
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.type = 'sine';
          oscillator.frequency.value = 800;
          gainNode.gain.value = 0.1;

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.start();
          setTimeout(() => oscillator.stop(), 200);
        } catch (error) {
          console.error('Error playing notification sound:', error);
        }
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // Handle marking a notification as read
  const handleMarkAsRead = (id) => {
    notificationService.markAsRead(id);
    setNotifications(notificationService.getNotifications());
  };

  // Handle marking all notifications as read
  const handleMarkAllAsRead = () => {
    notificationService.markAllAsRead();
    setNotifications(notificationService.getNotifications());
  };

  // Get icon based on notification type
  const getIcon = (type) => {
    switch (type) {
      case 'interest':
        return <FaHeart className="text-danger" />;
      case 'warning':
        return <FaExclamationCircle className="text-warning" />;
      case 'success':
        return <FaCheckCircle className="text-success" />;
      case 'info':
      default:
        return <FaInfoCircle className="text-primary" />;
    }
  };

  return (
    <Dropdown align="end">
      <Dropdown.Toggle variant="link" id="notification-dropdown" className="nav-link position-relative p-0">
        <FaBell size={20} />
        {unreadCount > 0 && (
          <Badge
            bg="danger"
            pill
            className="position-absolute top-0 start-100 translate-middle"
            style={{ fontSize: '0.6rem' }}
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </Dropdown.Toggle>

      <Dropdown.Menu className="shadow-sm notification-dropdown">
        <div className="d-flex justify-content-between align-items-center p-3 border-bottom">
          <h6 className="mb-0">Notifications</h6>
          {notifications.length > 0 && (
            <button
              className="btn btn-sm text-primary p-0 border-0 bg-transparent"
              onClick={handleMarkAllAsRead}
            >
              Mark all as read
            </button>
          )}
        </div>

        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-muted">
              <p className="mb-0">No notifications</p>
            </div>
          ) : (
            notifications.map((notification) => (
              <Dropdown.Item
                key={notification.id}
                as="div"
                className={`p-3 border-bottom ${!notification.read ? 'bg-light' : ''} ${notification.type === 'interest' ? 'notification-interest' : ''}`}
                onClick={() => handleMarkAsRead(notification.id)}
              >
                <div className="d-flex">
                  <div className="me-3">
                    {getIcon(notification.type)}
                  </div>
                  <div className="flex-grow-1">
                    <div className="d-flex justify-content-between align-items-center mb-1">
                      <strong>{notification.title}</strong>
                      <small className="text-muted">
                        {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                      </small>
                    </div>
                    <p className="mb-1 small">{notification.message}</p>

                    {/* Special handling for interest notifications */}
                    {notification.type === 'interest' && notification.resourceTitle && (
                      <div className="interest-notification-details mt-1 p-2 border rounded bg-light">
                        <small className="d-block text-primary mb-1">
                          <strong>Item:</strong> {notification.resourceTitle}
                        </small>
                        {notification.userName && (
                          <small className="d-block text-primary">
                            <strong>From:</strong> {notification.userName}
                          </small>
                        )}
                      </div>
                    )}

                    {notification.url && (
                      <Link
                        to={notification.url}
                        className={`btn btn-sm ${notification.type === 'interest' ? 'btn-outline-danger' : 'btn-outline-primary'} mt-2`}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsRead(notification.id);
                        }}
                      >
                        {notification.type === 'interest' ? 'Respond to Interest' : 'View details'}
                      </Link>
                    )}
                  </div>
                </div>
              </Dropdown.Item>
            ))
          )}
        </div>
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default NotificationDropdown;
