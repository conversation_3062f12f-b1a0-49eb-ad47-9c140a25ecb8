#!/bin/bash

echo "Starting UrbanPulse application..."
echo
echo "This script will start both the frontend and backend servers."
echo
echo "Press Ctrl+C to stop the servers."
echo

# Start the frontend server
cd frontend && npm run dev &
FRONTEND_PID=$!

# Start the backend server
cd ../backend && npm run dev &
BACKEND_PID=$!

# Function to handle Ctrl+C
function cleanup {
  echo
  echo "Stopping servers..."
  kill $FRONTEND_PID
  kill $BACKEND_PID
  exit 0
}

# Register the cleanup function for when Ctrl+C is pressed
trap cleanup SIGINT

echo "Servers started successfully!"
echo
echo "Frontend: http://localhost:3000"
echo "Backend: http://localhost:5001"
echo

# Wait for both processes to finish
wait $FRONTEND_PID $BACKEND_PID
