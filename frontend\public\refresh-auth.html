<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refreshing Authentication - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .refresh-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .refresh-icon {
            width: 80px;
            height: 80px;
            background-color: #0d6efd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }
        .refresh-icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        .spinner {
            width: 3rem;
            height: 3rem;
            margin: 1.5rem auto;
        }
    </style>
</head>
<body>
    <div class="refresh-container">
        <div class="refresh-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
        </div>
        <h2 class="mb-3">Refreshing Authentication</h2>
        <p class="mb-4">Please wait while we refresh your authentication state.</p>
        <div class="spinner-border text-primary spinner" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div id="status-message" class="mt-3"></div>
    </div>

    <script>
        // Function to check if user is logged in
        function checkAuthState() {
            const token = localStorage.getItem('token');
            const userDataString = localStorage.getItem('user');
            
            if (token && userDataString) {
                try {
                    // Parse user data
                    const userData = JSON.parse(userDataString);
                    document.getElementById('status-message').innerHTML = `
                        <div class="alert alert-success">
                            Logged in as: <strong>${userData.name || 'User'}</strong>
                        </div>
                    `;
                    return true;
                } catch (error) {
                    document.getElementById('status-message').innerHTML = `
                        <div class="alert alert-danger">
                            Error parsing user data: ${error.message}
                        </div>
                    `;
                    return false;
                }
            }
            
            document.getElementById('status-message').innerHTML = `
                <div class="alert alert-warning">
                    Not logged in. Please <a href="/public/direct-login.html">login</a> first.
                </div>
            `;
            return false;
        }
        
        // Function to redirect to home page
        function redirectToHome() {
            const isLoggedIn = checkAuthState();
            
            setTimeout(() => {
                if (isLoggedIn) {
                    // Redirect to home page with a special parameter to force refresh
                    window.location.href = '/?auth_refresh=' + new Date().getTime();
                } else {
                    // Show login option
                    document.querySelector('.spinner').style.display = 'none';
                    document.getElementById('status-message').innerHTML += `
                        <div class="mt-3">
                            <a href="/public/direct-login.html" class="btn btn-primary">Go to Login</a>
                        </div>
                    `;
                }
            }, 2000);
        }
        
        // Run the check and redirect
        redirectToHome();
    </script>
</body>
</html>
