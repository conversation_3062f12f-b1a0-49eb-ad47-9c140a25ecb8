import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import { FaArrowLeft, FaUpload } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import { mockItems, saveItems, allCategories, allLocations } from '../data/mockItems';

const ShareItemPage = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    title: '',
    category: '',
    description: '',
    imageUrl: 'https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3', // Default image
    transactionType: 'Exchange',
    price: '',
    location: '',
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors({
        ...errors,
        imageUrl: 'Image size must be less than 5MB'
      });
      return;
    }

    // Validate file type
    if (!file.type.match('image.*')) {
      setErrors({
        ...errors,
        imageUrl: 'Only image files are allowed'
      });
      return;
    }

    // Create a URL for the file
    const reader = new FileReader();
    reader.onload = (event) => {
      setFormData({
        ...formData,
        imageUrl: event.target.result
      });

      // Clear any errors
      if (errors.imageUrl) {
        setErrors({
          ...errors,
          imageUrl: null
        });
      }
    };
    reader.readAsDataURL(file);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.location) newErrors.location = 'Location is required';
    if (formData.transactionType !== 'Exchange' && (!formData.price || isNaN(formData.price) || Number(formData.price) <= 0)) {
      newErrors.price = 'Valid price is required for selling items';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);

    // In a real app, this would be an API call
    setTimeout(() => {
      // Create new item
      const newItem = {
        id: String(Date.now()),
        ...formData,
        price: formData.price ? Number(formData.price) : undefined,
        availability: 'Available',
        ownerId: 'current-user-id', // In a real app, this would come from auth context
        ownerName: 'Current User', // In a real app, this would come from auth context
        createdAt: new Date().toISOString(),
      };

      // Add to items and save to localStorage
      const updatedItems = [newItem, ...mockItems];
      saveItems(updatedItems);

      setLoading(false);
      setSuccess(true);

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/resource-sharing');
      }, 2000);
    }, 1500);
  };

  return (
    <Container className="py-5">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Button
            variant="outline-primary"
            className="mb-4"
            as={Link}
            to="/resource-sharing"
          >
            <FaArrowLeft className="me-2" />
            Back to Resources
          </Button>

          <Card className="border-0 shadow-sm">
            <Card.Body className="p-4">
              <h1 className="h3 mb-4">Share an Item</h1>

              {success && (
                <Alert variant="success">
                  Your item has been shared successfully! Redirecting...
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>Item Title</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    isInvalid={!!errors.title}
                    placeholder="e.g., Power Drill, Camping Tent, etc."
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.title}
                  </Form.Control.Feedback>
                </Form.Group>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Category</Form.Label>
                      <Form.Select
                        name="category"
                        value={formData.category}
                        onChange={handleChange}
                        isInvalid={!!errors.category}
                      >
                        <option value="">Select a category</option>
                        {allCategories.map(category => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        {errors.category}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Location</Form.Label>
                      <Form.Select
                        name="location"
                        value={formData.location}
                        onChange={handleChange}
                        isInvalid={!!errors.location}
                      >
                        <option value="">Select a location</option>
                        {allLocations.map(location => (
                          <option key={location} value={location}>
                            {location}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        {errors.location}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    isInvalid={!!errors.description}
                    placeholder="Describe your item, its condition, and any other relevant details..."
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.description}
                  </Form.Control.Feedback>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Transaction Type</Form.Label>
                  <Form.Select
                    name="transactionType"
                    value={formData.transactionType}
                    onChange={handleChange}
                  >
                    <option value="Exchange">Exchange Only</option>
                    <option value="Sell">Sell Only</option>
                    <option value="Both">Both (Exchange or Sell)</option>
                  </Form.Select>
                </Form.Group>

                {formData.transactionType !== 'Exchange' && (
                  <Form.Group className="mb-3">
                    <Form.Label>Price ($)</Form.Label>
                    <Form.Control
                      type="number"
                      name="price"
                      value={formData.price}
                      onChange={handleChange}
                      isInvalid={!!errors.price}
                      placeholder="Enter price in dollars"
                      min="0"
                      step="0.01"
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.price}
                    </Form.Control.Feedback>
                  </Form.Group>
                )}

                <Form.Group className="mb-4">
                  <Form.Label>Item Image</Form.Label>
                  <div className="d-flex align-items-center mb-3">
                    <div
                      className="border rounded me-3"
                      style={{ width: '100px', height: '100px', overflow: 'hidden' }}
                    >
                      <img
                        src={formData.imageUrl}
                        alt="Preview"
                        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                      />
                    </div>
                    <Button
                      variant="outline-primary"
                      onClick={() => document.getElementById('imageUpload').click()}
                    >
                      <FaUpload className="me-2" />
                      Upload Image
                    </Button>
                    <input
                      id="imageUpload"
                      type="file"
                      accept="image/*"
                      style={{ display: 'none' }}
                      onChange={handleImageUpload}
                    />
                  </div>
                  <Form.Text className="text-muted">
                    Upload an image of your item. Maximum size: 5MB. Supported formats: JPG, PNG, GIF.
                  </Form.Text>
                  {errors.imageUrl && (
                    <Form.Text className="text-danger">
                      {errors.imageUrl}
                    </Form.Text>
                  )}
                </Form.Group>

                <Form.Group className="mb-4">
                  <Form.Check
                    type="checkbox"
                    id="termsCheck"
                    label="I agree to the terms of use and community guidelines"
                    required
                  />
                </Form.Group>

                <div className="d-grid">
                  <Button
                    variant="primary"
                    type="submit"
                    size="lg"
                    disabled={loading}
                  >
                    {loading ? 'Submitting...' : 'Share Item'}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ShareItemPage;
