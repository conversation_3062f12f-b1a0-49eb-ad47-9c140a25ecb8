import React, { useState } from 'react';
import { Container, <PERSON>, Col, Card, Button, Modal } from 'react-bootstrap';
import { FaTree } from 'react-icons/fa';

const SimpleGreenSpacePage = () => {
  const [showModal, setShowModal] = useState(false);
  const [selectedSpace, setSelectedSpace] = useState(null);

  // Realistic data for green spaces
  const greenSpaces = [
    {
      id: 1,
      name: 'Riverside Memorial Park',
      description: 'A 12-acre urban park featuring mature oak trees, walking paths, two playgrounds, and open green spaces for recreational activities. Recently renovated with improved lighting and accessibility features.',
      image: 'https://images.unsplash.com/photo-1563911302283-d2bc129e7570?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 2,
      name: 'Willow Creek Greenway Trail',
      description: 'A 4.8-mile paved trail following Willow Creek, connecting multiple neighborhoods and parks. Features scenic overlooks, native plant restoration areas, and wildlife viewing opportunities. Part of the city\'s green corridor initiative.',
      image: 'https://images.unsplash.com/photo-1564221710304-0b37c8b9d729?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 3,
      name: 'Eastside Urban Harvest Garden',
      description: 'A 1.5-acre community garden established in 2015, featuring 48 individual plots, a communal herb garden, and educational growing spaces. Hosts regular workshops on urban agriculture and sustainable growing practices.',
      image: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    }
  ];

  const handleViewSpace = (space) => {
    setSelectedSpace(space);
    setShowModal(true);
  };

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Green Space Mapping (Simple)</h1>
          <p className="lead">
            Discover parks, gardens, and join community events for a greener neighborhood.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-success bg-opacity-10 p-3 rounded">
            <FaTree className="text-success fs-1" />
          </div>
        </Col>
      </Row>

      <Row xs={1} md={3} className="g-4">
        {greenSpaces.map((space) => (
          <Col key={space.id}>
            <Card className="h-100 shadow-sm">
              <Card.Img
                variant="top"
                src={space.image}
                alt={space.name}
                style={{ height: '200px', objectFit: 'cover' }}
              />
              <Card.Body>
                <Card.Title>{space.name}</Card.Title>
                <Card.Text>{space.description}</Card.Text>
                <Button
                  variant="outline-success"
                  onClick={() => handleViewSpace(space)}
                >
                  View Details
                </Button>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Green Space Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Green Space Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedSpace && (
            <>
              <img
                src={selectedSpace.image}
                alt={selectedSpace.name}
                className="img-fluid rounded mb-3"
              />
              <h4>{selectedSpace.name}</h4>
              <p>{selectedSpace.description}</p>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          <Button variant="success">
            Get Directions
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default SimpleGreenSpacePage;
