.resource-chat {
  display: flex;
  flex-direction: column;
  height: 400px;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  overflow: hidden;
}

.chat-header {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.message {
  display: flex;
  max-width: 80%;
}

.message.user {
  align-self: flex-start;
}

.message.owner {
  align-self: flex-end;
}

.message-content {
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: #f1f3f5;
}

.message.owner .message-content {
  background-color: #e7f5ff;
  border-bottom-right-radius: 0;
}

.message.user .message-content {
  background-color: #f8f9fa;
  border-bottom-left-radius: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
}

.message-sender {
  font-weight: 600;
  color: #495057;
}

.message-time {
  color: #868e96;
}

.message-body {
  word-break: break-word;
}

.chat-input {
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  background-color: #fff;
}
