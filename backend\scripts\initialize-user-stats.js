const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Import models
const User = require('../models/User');
const UserStats = require('../models/UserStats');
const Issue = require('../models/Issue');
const Event = require('../models/Event');
const Resource = require('../models/Resource');
const Challenge = require('../models/Challenge');
const Course = require('../models/Course');
const ServiceReview = require('../models/ServiceReview');

// Connect to MongoDB
console.log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');

  try {
    // Get all users
    const users = await User.find();
    console.log(`Found ${users.length} users`);

    // Process each user
    for (const user of users) {
      console.log(`Processing user: ${user.name} (${user._id})`);
      
      // Check if user stats already exist
      const existingStats = await UserStats.findOne({ user: user._id });
      
      if (existingStats) {
        console.log(`Stats already exist for user ${user.name}, skipping...`);
        continue;
      }
      
      // Get counts from various collections
      const issuesCount = await Issue.countDocuments({ user: user._id });
      const eventsCount = await Event.countDocuments({ 
        'participants.user': user._id 
      });
      const resourcesCount = await Resource.countDocuments({ owner: user._id });
      const challengesCount = await Challenge.countDocuments({ 
        'participants.user': user._id,
        'participants.progress': 100 // Only count completed challenges
      });
      const coursesCount = await Course.countDocuments({
        'enrollments.user': user._id,
        'enrollments.progress': 100 // Only count completed courses
      });
      const reviewsCount = await ServiceReview.countDocuments({ user: user._id });
      
      // For transport feedbacks, we'll use a placeholder since we don't have the model
      const transportCount = 0;
      
      console.log(`Activity counts for ${user.name}:`);
      console.log(`- Issues: ${issuesCount}`);
      console.log(`- Events: ${eventsCount}`);
      console.log(`- Resources: ${resourcesCount}`);
      console.log(`- Challenges: ${challengesCount}`);
      console.log(`- Courses: ${coursesCount}`);
      console.log(`- Reviews: ${reviewsCount}`);
      
      // Get detailed activity data
      const issueDetails = await getIssueDetails(user._id);
      const eventDetails = await getEventDetails(user._id);
      const resourceDetails = await getResourceDetails(user._id);
      const challengeDetails = await getChallengeDetails(user._id);
      const courseDetails = await getCourseDetails(user._id);
      const reviewDetails = await getReviewDetails(user._id);
      
      // Create user stats record
      const userStats = new UserStats({
        user: user._id,
        issuesReported: issuesCount,
        transportFeedbacks: transportCount,
        challengesCompleted: challengesCount,
        reviewsSubmitted: reviewsCount,
        resourcesShared: resourcesCount,
        eventsAttended: eventsCount,
        coursesCompleted: coursesCount,
        issueDetails,
        eventDetails,
        resourceDetails,
        challengeDetails,
        courseDetails,
        reviewDetails,
        transportDetails: [],
        lastUpdated: new Date()
      });
      
      await userStats.save();
      console.log(`Created stats for user ${user.name}`);
    }
    
    console.log('User stats initialization complete!');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing user stats:', error);
    process.exit(1);
  }
})
.catch((err) => {
  console.error('Failed to connect to MongoDB', err);
  process.exit(1);
});

// Helper functions to get detailed activity data
async function getIssueDetails(userId) {
  const issues = await Issue.find({ user: userId }).select('_id title category status createdAt');
  return issues.map(issue => ({
    issueId: issue._id,
    title: issue.title,
    category: issue.category,
    status: issue.status,
    reportedAt: issue.createdAt
  }));
}

async function getEventDetails(userId) {
  const events = await Event.find({ 'participants.user': userId }).select('_id title organizer');
  return events.map(event => {
    const participant = event.participants.find(p => p.user.toString() === userId.toString());
    return {
      eventId: event._id,
      title: event.title,
      organizer: event.organizer,
      attendedAt: participant ? participant.joinedAt : new Date()
    };
  });
}

async function getResourceDetails(userId) {
  const resources = await Resource.find({ owner: userId }).select('_id title category createdAt');
  return resources.map(resource => ({
    resourceId: resource._id,
    title: resource.title,
    category: resource.category,
    sharedAt: resource.createdAt
  }));
}

async function getChallengeDetails(userId) {
  const challenges = await Challenge.find({ 
    'participants.user': userId,
    'participants.progress': 100
  }).select('_id title category points');
  
  return challenges.map(challenge => {
    const participant = challenge.participants.find(p => p.user.toString() === userId.toString());
    return {
      challengeId: challenge._id,
      title: challenge.title,
      category: challenge.category,
      points: challenge.points,
      completedAt: participant ? participant.completedTasks[participant.completedTasks.length - 1]?.completedAt : new Date()
    };
  });
}

async function getCourseDetails(userId) {
  const courses = await Course.find({
    'enrollments.user': userId,
    'enrollments.progress': 100
  }).select('_id title category');
  
  return courses.map(course => {
    const enrollment = course.enrollments.find(e => e.user.toString() === userId.toString());
    return {
      courseId: course._id,
      title: course.title,
      category: course.category,
      completedAt: enrollment ? enrollment.completedLessons[enrollment.completedLessons.length - 1]?.completedAt : new Date()
    };
  });
}

async function getReviewDetails(userId) {
  const reviews = await ServiceReview.find({ user: userId }).select('_id serviceId rating createdAt');
  
  // Get service names for each review
  const reviewDetails = [];
  for (const review of reviews) {
    let serviceName = 'Unknown Service';
    try {
      const service = await mongoose.model('Service').findById(review.serviceId);
      if (service) {
        serviceName = service.name;
      }
    } catch (error) {
      console.error('Error fetching service name:', error);
    }
    
    reviewDetails.push({
      reviewId: review._id,
      serviceId: review.serviceId,
      serviceName,
      rating: review.rating,
      submittedAt: review.createdAt
    });
  }
  
  return reviewDetails;
}
