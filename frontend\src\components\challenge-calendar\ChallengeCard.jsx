import React from 'react';
import { <PERSON>, Badge, Button } from 'react-bootstrap';
import { FaCalendarAlt, FaCheckCircle, FaInfoCircle } from 'react-icons/fa';

const ChallengeCard = ({ challenge, onAccept, onComplete, showCalendar }) => {
  const handleAccept = () => {
    if (onAccept) {
      onAccept(challenge.id);
    }
  };

  const handleComplete = () => {
    if (onComplete) {
      onComplete(challenge.id, true);
    }
  };

  return (
    <Card className={`h-100 ${challenge.completed ? 'border-success' : ''}`}>
      <Card.Body>
        <div className="d-flex justify-content-between mb-2">
          <span className="text-muted">Week {challenge.week}</span>
          <Badge bg="secondary">{challenge.points} points</Badge>
        </div>
        <Card.Title>{challenge.title}</Card.Title>
        <Card.Text>{challenge.description}</Card.Text>
        <Badge bg="success" className="mb-3">{challenge.category}</Badge>
        <div className="bg-light p-2 rounded mb-3">
          <strong>Tips:</strong> {challenge.tips}
        </div>
        
        {!challenge.is_accepted && !challenge.completed ? (
          <Button
            variant="outline-success"
            className="w-100"
            onClick={handleAccept}
          >
            Accept Challenge
          </Button>
        ) : challenge.completed ? (
          <Button
            variant="success"
            className="w-100"
            disabled
          >
            <FaCheckCircle className="me-2" />
            Completed
          </Button>
        ) : (
          <Button
            variant="outline-success"
            className="w-100"
            onClick={handleComplete}
          >
            Mark as Complete
          </Button>
        )}
      </Card.Body>
    </Card>
  );
};

export default ChallengeCard;
