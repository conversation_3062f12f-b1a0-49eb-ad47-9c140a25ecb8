import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, Badge, <PERSON><PERSON>, <PERSON><PERSON>, Ta<PERSON>, Tab } from 'react-bootstrap';
import { format } from 'date-fns';

const ActivityDetails = ({ userId, token }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activityData, setActivityData] = useState({
    issues: [],
    transport: [],
    challenges: [],
    reviews: [],
    resources: [],
    events: [],
    courses: []
  });

  useEffect(() => {
    const fetchActivityData = async () => {
      try {
        setLoading(true);
        
        if (!token) {
          throw new Error('Authentication token not found');
        }
        
        // Fetch all activity data
        const response = await fetch('/api/user/activity/all', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch activity data: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Received activity data:', data);
        
        if (data.success && data.data) {
          setActivityData(data.data);
        } else {
          setActivityData({
            issues: [],
            transport: [],
            challenges: [],
            reviews: [],
            resources: [],
            events: [],
            courses: []
          });
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching activity data:', error);
        setError(error.message);
        setLoading(false);
      }
    };
    
    if (userId && token) {
      fetchActivityData();
    }
  }, [userId, token]);
  
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };
  
  const getStatusBadge = (status) => {
    let variant = 'secondary';
    
    if (status === 'Open') variant = 'danger';
    if (status === 'In Progress') variant = 'warning';
    if (status === 'Resolved') variant = 'success';
    
    return <Badge bg={variant}>{status}</Badge>;
  };
  
  if (loading) {
    return (
      <div className="text-center p-4">
        <Spinner animation="border" variant="primary" />
        <p className="mt-2">Loading your activity data...</p>
      </div>
    );
  }
  
  if (error) {
    return <Alert variant="danger">{error}</Alert>;
  }
  
  return (
    <Card className="border-0 shadow-sm">
      <Card.Body>
        <h4 className="mb-4">Your Activity Details</h4>
        
        <Tabs defaultActiveKey="issues" className="mb-4">
          <Tab eventKey="issues" title={`Issues (${activityData.issues?.length || 0})`}>
            {activityData.issues?.length > 0 ? (
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Category</th>
                    <th>Status</th>
                    <th>Reported On</th>
                  </tr>
                </thead>
                <tbody>
                  {activityData.issues.map((issue, index) => (
                    <tr key={issue.issueId || index}>
                      <td>{issue.title}</td>
                      <td>{issue.category}</td>
                      <td>{getStatusBadge(issue.status)}</td>
                      <td>{formatDate(issue.reportedAt)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            ) : (
              <p className="text-center text-muted my-4">No issues reported yet</p>
            )}
          </Tab>
          
          <Tab eventKey="events" title={`Events (${activityData.events?.length || 0})`}>
            {activityData.events?.length > 0 ? (
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>Event</th>
                    <th>Organizer</th>
                    <th>Attended On</th>
                  </tr>
                </thead>
                <tbody>
                  {activityData.events.map((event, index) => (
                    <tr key={event.eventId || index}>
                      <td>{event.title}</td>
                      <td>{event.organizer}</td>
                      <td>{formatDate(event.attendedAt)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            ) : (
              <p className="text-center text-muted my-4">No events attended yet</p>
            )}
          </Tab>
          
          <Tab eventKey="challenges" title={`Challenges (${activityData.challenges?.length || 0})`}>
            {activityData.challenges?.length > 0 ? (
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>Challenge</th>
                    <th>Category</th>
                    <th>Points</th>
                    <th>Completed On</th>
                  </tr>
                </thead>
                <tbody>
                  {activityData.challenges.map((challenge, index) => (
                    <tr key={challenge.challengeId || index}>
                      <td>{challenge.title}</td>
                      <td>{challenge.category}</td>
                      <td>{challenge.points}</td>
                      <td>{formatDate(challenge.completedAt)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            ) : (
              <p className="text-center text-muted my-4">No challenges completed yet</p>
            )}
          </Tab>
          
          <Tab eventKey="resources" title={`Resources (${activityData.resources?.length || 0})`}>
            {activityData.resources?.length > 0 ? (
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>Resource</th>
                    <th>Category</th>
                    <th>Shared On</th>
                  </tr>
                </thead>
                <tbody>
                  {activityData.resources.map((resource, index) => (
                    <tr key={resource.resourceId || index}>
                      <td>{resource.title}</td>
                      <td>{resource.category}</td>
                      <td>{formatDate(resource.sharedAt)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            ) : (
              <p className="text-center text-muted my-4">No resources shared yet</p>
            )}
          </Tab>
          
          <Tab eventKey="courses" title={`Courses (${activityData.courses?.length || 0})`}>
            {activityData.courses?.length > 0 ? (
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>Course</th>
                    <th>Category</th>
                    <th>Completed On</th>
                  </tr>
                </thead>
                <tbody>
                  {activityData.courses.map((course, index) => (
                    <tr key={course.courseId || index}>
                      <td>{course.title}</td>
                      <td>{course.category}</td>
                      <td>{formatDate(course.completedAt)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            ) : (
              <p className="text-center text-muted my-4">No courses completed yet</p>
            )}
          </Tab>
        </Tabs>
      </Card.Body>
    </Card>
  );
};

export default ActivityDetails;
