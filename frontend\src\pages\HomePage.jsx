import React from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { FaExclamationTriangle, FaBus, FaLeaf, FaStar, FaExchangeAlt, FaTree, FaGraduationCap, FaRecycle, FaChartLine, FaCalendarAlt } from 'react-icons/fa';
import GeminiChatWidget from '../components/ai/GeminiChatWidget';
import VoiceAssistant from '../components/voice-assistance';

const HomePage = () => {
  const navigate = useNavigate();
  const features = [
    {
      id: 1,
      title: 'Urban Issue Reporting',
      description: 'Report potholes, streetlights, trash issues and more with simple forms and photos.',
      icon: <FaExclamationTriangle className="feature-icon mb-3" />,
      link: '/issue-reporting',
      color: '#e53935'
    },
    {
      id: 2,
      title: 'Public Transport Feedback',
      description: 'Suggest improvements to bus routes, schedules, and services through votes and comments.',
      icon: <FaBus className="feature-icon mb-3" />,
      link: '/transport-feedback',
      color: '#1565c0'
    },
    {
      id: 3,
      title: 'Challenge Calendars',
      description: 'Complete weekly sustainability and health challenges to earn badges and track your progress.',
      icon: <FaCalendarAlt className="feature-icon mb-3" />,
      link: '/sustainable-living',
      color: '#43a047'
    },
    {
      id: 4,
      title: 'Public Service Reviews',
      description: 'Rate and review public services like garbage collection, water supply, and more.',
      icon: <FaStar className="feature-icon mb-3" />,
      link: '/public-services',
      color: '#ffa000'
    },
    {
      id: 5,
      title: 'Resource Sharing Platform',
      description: 'Share unused resources with neighbors - tools, parking spaces, garden produce, and more.',
      icon: <FaExchangeAlt className="feature-icon mb-3" />,
      link: '/resource-sharing',
      color: '#6d4c41'
    },
    {
      id: 6,
      title: 'Carbon Footprint',
      description: 'Track your daily activities and calculate your carbon footprint to reduce your environmental impact.',
      icon: <FaLeaf className="feature-icon mb-3" />,
      link: '/green-spaces',
      color: '#2e7d32'
    },
    {
      id: 7,
      title: 'Urban Learning Hub',
      description: 'Access courses on sustainable living, waste management, and eco-friendly practices.',
      icon: <FaGraduationCap className="feature-icon mb-3" />,
      link: '/learning-hub',
      color: '#5e35b1'
    },
    {
      id: 8,
      title: 'Reuse',
      description: 'Get innovative ideas for reusing waste materials and reduce your environmental impact.',
      icon: <FaRecycle className="feature-icon mb-3" />,
      link: '/ai-assistant',
      color: '#4CAF50'
    }
  ];

  return (
    <>
      <VoiceAssistant />
      <Container data-testid="home-page">
        {/* Hero Section */}
        <Row className="align-items-center py-5">
          <Col lg={6} className="mb-4 mb-lg-0">
            <h1 className="display-4 fw-bold mb-3">Smart Urban Living</h1>
            <p className="lead mb-4">
              Empowering citizens to create smarter, more sustainable urban communities through collaboration and innovation.
            </p>
            <div className="d-flex gap-2">
              <Button
                onClick={() => window.location.href = '/public/register-redirect.html'}
                variant="primary"
                size="lg"
              >
                Get Started
              </Button>
              <Button
                onClick={() => window.location.href = '/learning-hub'}
                variant="outline-primary"
                size="lg"
              >
                Learn More
              </Button>
            </div>
          </Col>
          <Col lg={6}>
            <div className="position-relative">
              <img
                src="https://images.unsplash.com/photo-1518005068251-37900150dfca?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80"
                alt="Smart City"
                className="img-fluid rounded shadow"
              />
              <div className="position-absolute bottom-0 end-0 m-3" style={{ width: '300px', maxWidth: '100%' }}>
                <GeminiChatWidget title="Ask about Urban Pulse" />
              </div>
            </div>
          </Col>
        </Row>

      {/* Features Section */}
      <section className="py-5">
        <h2 className="text-center mb-5">Our Features</h2>
        <Row xs={1} md={2} lg={3} className="g-4">
          {features.map((feature) => (
            <Col key={feature.id}>
              <Card className="h-100 feature-card border-0 shadow-sm">
                <Card.Body className="d-flex flex-column text-center p-4">
                  {feature.icon}
                  <Card.Title className="mb-3">{feature.title}</Card.Title>
                  <Card.Text className="text-muted mb-4">{feature.description}</Card.Text>
                  <Button
                    onClick={() => window.location.href = feature.link}
                    variant="outline-primary"
                    className="mt-auto"
                  >
                    Explore
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      </section>

      {/* Call to Action */}
      <section className="py-5 text-center">
        <div className="p-5 bg-light rounded-3 shadow-sm">
          <h2 className="mb-3">Join Our Community Today</h2>
          <p className="lead mb-4">
            Be part of the movement to make our city smarter, greener, and more livable.
          </p>
          <Button
            onClick={() => window.location.href = '/public/register-redirect.html'}
            variant="primary"
            size="lg"
          >
            Sign Up Now
          </Button>
        </div>
      </section>
    </Container>
    </>
  );
};

export default HomePage;
