const express = require('express');
const {
  getResources,
  getResource,
  createResource,
  updateResource,
  deleteResource,
  requestResource,
  respondToRequest,
  getMyResources,
  getBorrowedResources,
  updateResourceStatus,
  addResourceMessage
} = require('../controllers/resourceController');

const router = express.Router();

const { protect } = require('../middleware/auth');

router
  .route('/')
  .get(getResources)
  .post(protect, createResource);

router
  .route('/my-resources')
  .get(protect, getMyResources);

router
  .route('/borrowed')
  .get(protect, getBorrowedResources);

router
  .route('/:id')
  .get(getResource)
  .put(protect, updateResource)
  .delete(protect, deleteResource);

router
  .route('/:id/request')
  .post(protect, requestResource);

router
  .route('/:id/request/:requestId')
  .put(protect, respondToRequest);

router
  .route('/:id/status')
  .put(protect, updateResourceStatus);

router
  .route('/:id/messages')
  .post(protect, addResourceMessage);

module.exports = router;
