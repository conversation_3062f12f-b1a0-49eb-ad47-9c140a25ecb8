import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Tab, Nav } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const ProfilePage = () => {
  const { currentUser } = useAuth();
  const [name, setName] = useState(currentUser?.name || '');
  const [email, setEmail] = useState(currentUser?.email || '');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  // Mock data for user activity
  const userActivity = {
    issuesReported: 5,
    transportFeedbacks: 3,
    challengesCompleted: 7,
    reviewsSubmitted: 4,
    resourcesShared: 2,
    eventsAttended: 3,
    coursesCompleted: 2
  };

  const handleProfileUpdate = (e) => {
    e.preventDefault();
    
    setError('');
    setSuccess('');
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setSuccess('Profile updated successfully!');
      setLoading(false);
    }, 1000);
  };

  const handlePasswordChange = (e) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      return setError('Passwords do not match');
    }
    
    setError('');
    setSuccess('');
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setSuccess('Password changed successfully!');
      setLoading(false);
      setPassword('');
      setConfirmPassword('');
    }, 1000);
  };

  return (
    <Container>
      <h1 className="mb-4">My Profile</h1>
      
      <Tab.Container defaultActiveKey="profile">
        <Row>
          <Col md={3} className="mb-4">
            <Card className="border-0 shadow-sm">
              <Card.Body>
                <div className="text-center mb-4">
                  <div className="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '80px', height: '80px' }}>
                    <span className="fs-1">{name.charAt(0)}</span>
                  </div>
                  <h5 className="mb-0">{name}</h5>
                  <p className="text-muted small">{email}</p>
                </div>
                
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="profile">Profile Information</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="password">Change Password</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="activity">My Activity</Nav.Link>
                  </Nav.Item>
                </Nav>
              </Card.Body>
            </Card>
          </Col>
          
          <Col md={9}>
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-4">
                <Tab.Content>
                  <Tab.Pane eventKey="profile">
                    <h4 className="mb-4">Profile Information</h4>
                    
                    {error && <Alert variant="danger">{error}</Alert>}
                    {success && <Alert variant="success">{success}</Alert>}
                    
                    <Form onSubmit={handleProfileUpdate}>
                      <Form.Group className="mb-3" controlId="name">
                        <Form.Label>Full Name</Form.Label>
                        <Form.Control
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          required
                        />
                      </Form.Group>
                      
                      <Form.Group className="mb-4" controlId="email">
                        <Form.Label>Email</Form.Label>
                        <Form.Control
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          disabled
                        />
                        <Form.Text className="text-muted">
                          Email cannot be changed.
                        </Form.Text>
                      </Form.Group>
                      
                      <Button 
                        variant="primary" 
                        type="submit" 
                        disabled={loading}
                      >
                        {loading ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </Form>
                  </Tab.Pane>
                  
                  <Tab.Pane eventKey="password">
                    <h4 className="mb-4">Change Password</h4>
                    
                    {error && <Alert variant="danger">{error}</Alert>}
                    {success && <Alert variant="success">{success}</Alert>}
                    
                    <Form onSubmit={handlePasswordChange}>
                      <Form.Group className="mb-3" controlId="password">
                        <Form.Label>New Password</Form.Label>
                        <Form.Control
                          type="password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                          minLength={6}
                        />
                      </Form.Group>
                      
                      <Form.Group className="mb-4" controlId="confirmPassword">
                        <Form.Label>Confirm New Password</Form.Label>
                        <Form.Control
                          type="password"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          required
                          minLength={6}
                        />
                      </Form.Group>
                      
                      <Button 
                        variant="primary" 
                        type="submit" 
                        disabled={loading}
                      >
                        {loading ? 'Changing...' : 'Change Password'}
                      </Button>
                    </Form>
                  </Tab.Pane>
                  
                  <Tab.Pane eventKey="activity">
                    <h4 className="mb-4">My Activity</h4>
                    
                    <Row xs={1} md={2} className="g-4">
                      <Col>
                        <Card className="border-0 bg-light">
                          <Card.Body>
                            <h6 className="mb-2">Issues Reported</h6>
                            <p className="fs-2 fw-bold mb-0">{userActivity.issuesReported}</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col>
                        <Card className="border-0 bg-light">
                          <Card.Body>
                            <h6 className="mb-2">Transport Feedbacks</h6>
                            <p className="fs-2 fw-bold mb-0">{userActivity.transportFeedbacks}</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col>
                        <Card className="border-0 bg-light">
                          <Card.Body>
                            <h6 className="mb-2">Challenges Completed</h6>
                            <p className="fs-2 fw-bold mb-0">{userActivity.challengesCompleted}</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col>
                        <Card className="border-0 bg-light">
                          <Card.Body>
                            <h6 className="mb-2">Reviews Submitted</h6>
                            <p className="fs-2 fw-bold mb-0">{userActivity.reviewsSubmitted}</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col>
                        <Card className="border-0 bg-light">
                          <Card.Body>
                            <h6 className="mb-2">Resources Shared</h6>
                            <p className="fs-2 fw-bold mb-0">{userActivity.resourcesShared}</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col>
                        <Card className="border-0 bg-light">
                          <Card.Body>
                            <h6 className="mb-2">Events Attended</h6>
                            <p className="fs-2 fw-bold mb-0">{userActivity.eventsAttended}</p>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col>
                        <Card className="border-0 bg-light">
                          <Card.Body>
                            <h6 className="mb-2">Courses Completed</h6>
                            <p className="fs-2 fw-bold mb-0">{userActivity.coursesCompleted}</p>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>
                  </Tab.Pane>
                </Tab.Content>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Tab.Container>
    </Container>
  );
};

export default ProfilePage;
