const axios = require('axios');

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

// Demo user credentials
const demoUser = {
  email: '<EMAIL>',
  password: 'password123'
};

// Admin user credentials
const adminUser = {
  email: '<EMAIL>',
  password: 'password123'
};

// Function to test login
async function testLogin(user) {
  console.log(`Testing login with email: ${user.email}`);
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', user);
    
    console.log('Login successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('Login failed!');
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else if (error.request) {
      console.error('No response received. Is the server running?');
    } else {
      console.error('Error:', error.message);
    }
    
    return null;
  }
}

// Test all users
async function runTests() {
  console.log('=== TESTING LOGIN FUNCTIONALITY ===');
  
  console.log('\n1. Testing with test user:');
  await testLogin(testUser);
  
  console.log('\n2. Testing with demo user:');
  await testLogin(demoUser);
  
  console.log('\n3. Testing with admin user:');
  await testLogin(adminUser);
  
  console.log('\n=== TESTING COMPLETED ===');
}

// Run the tests
runTests();
