const express = require('express');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('./models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
app.use(express.json());

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});

// Root route
app.get('/', (req, res) => {
  res.json({ message: 'Auth test server is running' });
});

// Register route
app.post('/api/auth/register', async (req, res) => {
  try {
    console.log('Registration request received:', {
      body: { ...req.body, password: req.body.password ? '********' : undefined }
    });

    const { name, email, password } = req.body;

    // Validate input fields
    if (!name || !email || !password) {
      console.log('Registration validation failed: Missing required fields');
      return res.status(400).json({
        success: false,
        error: 'Please provide name, email and password'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });

    if (existingUser) {
      console.log('Registration failed: User already exists');
      return res.status(400).json({
        success: false,
        error: 'User already exists'
      });
    }

    // Create user
    console.log('Creating new user in database');
    const user = await User.create({
      name,
      email,
      password
    });

    // Create token
    const token = jwt.sign(
      { id: user._id, isAdmin: user.isAdmin || false },
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production',
      {
        expiresIn: process.env.JWT_EXPIRE || '30d'
      }
    );

    // Don't include password in response
    const userResponse = user.toObject();
    delete userResponse.password;

    console.log('User created successfully, sending response');
    res.status(201).json({
      success: true,
      token,
      data: userResponse
    });
  } catch (err) {
    console.error('Registration error:', err);
    
    // Handle mongoose validation errors
    if (err.name === 'ValidationError') {
      const messages = Object.values(err.errors).map(val => val.message);
      console.log('Mongoose validation error:', messages);
      
      return res.status(400).json({
        success: false,
        error: messages.join(', ')
      });
    }
    
    // Handle duplicate key error
    if (err.code === 11000) {
      console.log('Duplicate key error (likely email)');
      return res.status(400).json({
        success: false,
        error: 'Email already in use'
      });
    }
    
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
});

// Start server
const PORT = 5005;
app.listen(PORT, () => {
  console.log(`Test auth server running on port ${PORT}`);
  console.log(`Test the registration endpoint at: http://localhost:${PORT}/api/auth/register`);
});
