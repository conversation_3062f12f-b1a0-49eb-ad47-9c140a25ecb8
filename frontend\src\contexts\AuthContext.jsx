import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../utils/api';
import socketService from '../services/socketService';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();

  // Function to check authentication state from localStorage
  const checkAuthState = () => {
    console.log('Checking auth state from localStorage');
    const token = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    if (token && storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setCurrentUser(userData);
        setIsAuthenticated(true);

        // Connect to socket service
        socketService.connect();

        // Verify token validity with backend
        const verifyToken = async () => {
          try {
            const res = await api.get('/auth/me');
            setCurrentUser(res.data.data);
          } catch (error) {
            console.error('Token verification failed:', error);
            // If token is invalid, log out
            logout();
          }
        };

        verifyToken();
        return true;
      } catch (e) {
        console.error('Error parsing stored user data:', e);
        logout();
        return false;
      }
    } else {
      // No token or user data found, user is not authenticated
      setCurrentUser(null);
      setIsAuthenticated(false);
      return false;
    }
  };

  // Check auth state on component mount
  useEffect(() => {
    checkAuthState();
    setLoading(false);
  }, []);

  // Listen for storage events to detect login/logout in other tabs or windows
  useEffect(() => {
    // Function to handle storage changes
    const handleStorageChange = (e) => {
      console.log('Storage changed:', e);
      if (e.key === 'token' || e.key === 'user') {
        checkAuthState();
      }
    };

    // Add event listener
    window.addEventListener('storage', handleStorageChange);

    // Check for auth_update parameter in URL which indicates a login from static HTML
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('auth_update')) {
      console.log('Auth update parameter detected, checking auth state');
      checkAuthState();

      // Remove the parameter from URL to avoid repeated checks
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }

    // Clean up event listener
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Register function
  const register = async (name, email, password) => {
    setError('');
    try {
      console.log('AuthContext: Registering user with:', { name, email, password: '********' });

      const res = await api.post('/auth/register', {
        name,
        email,
        password
      });

      console.log('AuthContext: Registration API response:', res.data);

      // For server.js response format (message property)
      if (res.data.message === 'User registered successfully') {
        console.log('Registration successful with message format');
        // Don't set authentication state - just return success
        return { success: true };
      }
      // For authController.js response format (success property)
      else if (res.data.success) {
        console.log('Registration successful with success format');
        // Don't set authentication state - just return success
        return { success: true };
      } else {
        // Handle case where success is false but no error was thrown
        const errorMessage = res.data.error || 'Registration failed with unknown error';
        console.error('AuthContext: Registration failed with success=false:', errorMessage);
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('AuthContext: Registration error:', error);

      // Detailed error logging
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        console.error('Response headers:', error.response.headers);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      const errorMessage = error.response?.data?.error || 'Registration failed';
      setError(errorMessage);
      throw error.response ? error.response.data : new Error(errorMessage);
    }
  };

  // Login function
  const login = async (email, password) => {
    setError('');
    try {
      console.log('Attempting login with:', { email, password: '********' });

      const res = await api.post('/auth/login', {
        email,
        password
      });

      console.log('Login response:', res.data);

      // Check for both response formats
      if (res.data.success || (res.data.token && res.data.user)) {
        // Store token
        localStorage.setItem('token', res.data.token);

        // Get user data if needed
        let userData;
        if (res.data.data) {
          userData = res.data.data;
        } else if (res.data.user) {
          userData = res.data.user;
        } else {
          // If no user data in response, fetch it
          try {
            const userRes = await api.get('/auth/me');
            userData = userRes.data.data;
          } catch (userError) {
            console.error('Error fetching user data:', userError);
            // Use a minimal user object if we can't fetch details
            userData = {
              id: 'unknown',
              name: 'User',
              email: email
            };
          }
        }

        console.log('User data:', userData);

        // Store user data
        localStorage.setItem('user', JSON.stringify(userData));
        setCurrentUser(userData);
        setIsAuthenticated(true);

        // Connect to socket service
        try {
          socketService.connect();
        } catch (socketError) {
          console.error('Error connecting to socket service:', socketError);
          // Non-critical error, continue with login
        }

        // Dispatch a custom event to notify components about the login
        console.log('Dispatching userLoggedIn event');
        window.dispatchEvent(new Event('userLoggedIn'));

        // Force a refresh of the navbar by dispatching a storage event
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'user',
          newValue: JSON.stringify(userData),
          oldValue: null,
          storageArea: localStorage
        }));

        // Return user data (don't navigate here)
        return userData;
      } else {
        // Handle case where the response doesn't indicate success
        const errorMessage = res.data.error || 'Login failed with unknown error';
        console.error('Login failed with success=false:', errorMessage);
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Login error:', error);

      // Detailed error logging
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      const errorMessage = error.response?.data?.error || 'Login failed';
      setError(errorMessage);
      throw error.response ? error.response.data : new Error(errorMessage);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await api.get('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // First update state
      setCurrentUser(null);
      setIsAuthenticated(false);

      // Then clear localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Disconnect from socket service
      socketService.disconnect();

      // Navigate to login page
      navigate('/login');

      // Force a page reload to ensure all components update
      // This helps with the static HTML pages that might not be using the React context
      setTimeout(() => {
        window.location.reload();
      }, 100);
    }
  };

  // Update user profile
  const updateProfile = async (userData) => {
    try {
      const res = await api.put('/auth/update-profile', userData);

      if (res.data.success) {
        localStorage.setItem('user', JSON.stringify(res.data.data));
        setCurrentUser(res.data.data);
        return res.data.data;
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Profile update failed';
      setError(errorMessage);
      throw error.response ? error.response.data : new Error(errorMessage);
    }
  };

  const value = {
    currentUser,
    login,
    register,
    logout,
    loading,
    error,
    isAuthenticated,
    updateProfile,
    setError
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
