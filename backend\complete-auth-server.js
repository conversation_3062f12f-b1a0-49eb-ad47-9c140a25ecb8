const express = require('express');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Enable CORS for all routes
app.use(cors());

// Parse JSON request bodies
app.use(express.json());

// Setup logging
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const logFile = path.join(logDir, 'auth-server.log');
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  // Log to console
  console.log(logMessage.trim());
  
  // Log to file
  logStream.write(logMessage);
}

// Middleware to log all requests
app.use((req, res, next) => {
  const start = Date.now();
  log(`${req.method} ${req.url}`);
  
  if (req.body && Object.keys(req.body).length > 0) {
    // Clone the body and mask any passwords
    const safeBody = { ...req.body };
    if (safeBody.password) {
      safeBody.password = '********';
    }
    log(`Request body: ${JSON.stringify(safeBody)}`);
  }
  
  // Capture the original end method
  const originalEnd = res.end;
  
  // Override the end method to log the response
  res.end = function(chunk, encoding) {
    const duration = Date.now() - start;
    log(`Response: ${res.statusCode} (${duration}ms)`);
    
    // Call the original end method
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
});

// Connect to MongoDB
log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  log('Connected to MongoDB');
  
  // Start server
  const PORT = 5005;
  app.listen(PORT, () => {
    log(`Complete auth server running on port ${PORT}`);
    log(`http://localhost:${PORT}`);
  });
})
.catch(error => {
  log(`MongoDB connection error: ${error.message}`);
  process.exit(1);
});

// Root route
app.get('/', (req, res) => {
  res.json({ 
    message: 'Complete auth server is running',
    timestamp: new Date().toISOString()
  });
});

// Create test users if they don't exist
async function createTestUsers() {
  log('Checking for test users...');
  
  const db = mongoose.connection.db;
  const usersCollection = db.collection('users');
  
  const testUsers = [
    { email: '<EMAIL>', name: 'Test User', password: 'password123', isAdmin: false },
    { email: '<EMAIL>', name: 'Demo User', password: 'password123', isAdmin: false },
    { email: '<EMAIL>', name: 'Admin User', password: 'password123', isAdmin: true }
  ];
  
  for (const userData of testUsers) {
    const existingUser = await usersCollection.findOne({ email: userData.email });
    
    if (!existingUser) {
      log(`Creating test user: ${userData.email}`);
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);
      
      // Create the user
      await usersCollection.insertOne({
        name: userData.name,
        email: userData.email,
        password: hashedPassword,
        isAdmin: userData.isAdmin,
        createdAt: new Date()
      });
      
      log(`Created user: ${userData.email}`);
    } else {
      log(`Test user already exists: ${userData.email}`);
      
      // Update the password to ensure it's correct
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);
      
      await usersCollection.updateOne(
        { email: userData.email },
        { $set: { password: hashedPassword } }
      );
      
      log(`Updated password for: ${userData.email}`);
    }
  }
  
  log('Test users setup complete');
}

// Call the function to create test users
createTestUsers().catch(error => {
  log(`Error creating test users: ${error.message}`);
});

// Login route
app.post('/api/auth/login', async (req, res) => {
  log('=== LOGIN REQUEST ===');
  
  try {
    const { email, password } = req.body;
    
    // Validate input
    if (!email || !password) {
      log('Missing email or password');
      return res.status(400).json({ 
        success: false,
        message: 'Please provide email and password' 
      });
    }
    
    log(`Attempting login for email: ${email}`);
    
    // Get users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Find user by email
    const user = await usersCollection.findOne({ email });
    
    if (!user) {
      log(`User not found: ${email}`);
      return res.status(401).json({ 
        success: false,
        message: 'Invalid credentials' 
      });
    }
    
    log(`User found: ${user.name} (${user.email})`);
    log(`Password exists: ${!!user.password}`);
    
    if (!user.password) {
      log('User has no password set');
      return res.status(401).json({ 
        success: false,
        message: 'Account has no password set' 
      });
    }
    
    // Check password
    log('Comparing passwords...');
    const isMatch = await bcrypt.compare(password, user.password);
    
    log(`Password match result: ${isMatch}`);
    
    if (!isMatch) {
      log('Password does not match');
      return res.status(401).json({ 
        success: false,
        message: 'Invalid credentials' 
      });
    }
    
    log('Password matches, generating token');
    
    // Generate token
    const token = jwt.sign(
      { id: user._id, email: user.email, isAdmin: user.isAdmin || false },
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production',
      { expiresIn: '24h' }
    );
    
    log('Token generated successfully');
    
    // Prepare user data for response
    const userData = {
      id: user._id,
      name: user.name,
      email: user.email,
      isAdmin: user.isAdmin || false
    };
    
    log('Login successful, sending response');
    
    // Return success response
    res.json({
      success: true,
      token,
      user: userData
    });
    
    log('Response sent successfully');
  } catch (error) {
    log(`Login error: ${error.message}`);
    log(error.stack);
    res.status(500).json({ 
      success: false,
      message: 'Server error during login',
      error: error.message
    });
  }
});

// Register route
app.post('/api/auth/register', async (req, res) => {
  log('=== REGISTER REQUEST ===');
  
  try {
    const { name, email, password } = req.body;
    
    // Validate input
    if (!name || !email || !password) {
      log('Missing required fields');
      return res.status(400).json({ 
        success: false,
        message: 'Please provide name, email, and password' 
      });
    }
    
    log(`Attempting to register user: ${email}`);
    
    // Get users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Check if user already exists
    const existingUser = await usersCollection.findOne({ email });
    
    if (existingUser) {
      log(`User already exists: ${email}`);
      return res.status(400).json({ 
        success: false,
        message: 'User already exists' 
      });
    }
    
    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Create the user
    const result = await usersCollection.insertOne({
      name,
      email,
      password: hashedPassword,
      isAdmin: false,
      createdAt: new Date()
    });
    
    log(`User created: ${email}`);
    
    // Generate token
    const token = jwt.sign(
      { id: result.insertedId, email, isAdmin: false },
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production',
      { expiresIn: '24h' }
    );
    
    log('Token generated successfully');
    
    // Return success response
    res.status(201).json({
      success: true,
      token,
      user: {
        id: result.insertedId,
        name,
        email,
        isAdmin: false
      }
    });
    
    log('Response sent successfully');
  } catch (error) {
    log(`Register error: ${error.message}`);
    log(error.stack);
    res.status(500).json({ 
      success: false,
      message: 'Server error during registration',
      error: error.message
    });
  }
});

// Get user profile route
app.get('/api/auth/me', async (req, res) => {
  log('=== GET USER PROFILE ===');
  
  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      log('No token provided');
      return res.status(401).json({ 
        success: false,
        message: 'No token provided' 
      });
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify token
    const decoded = jwt.verify(
      token, 
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production'
    );
    
    log(`Token verified, user ID: ${decoded.id}`);
    
    // Get user from database
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    const user = await usersCollection.findOne({ _id: new mongoose.Types.ObjectId(decoded.id) });
    
    if (!user) {
      log('User not found');
      return res.status(404).json({ 
        success: false,
        message: 'User not found' 
      });
    }
    
    log(`User found: ${user.name} (${user.email})`);
    
    // Return user data
    res.json({
      success: true,
      data: {
        id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin || false
      }
    });
  } catch (error) {
    log(`Get profile error: ${error.message}`);
    log(error.stack);
    res.status(500).json({ 
      success: false,
      message: 'Server error fetching profile',
      error: error.message
    });
  }
});
