.voice-assistant-container {
  position: relative;
  z-index: 1000;
}

.voice-assistant-icon {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #2e7d32;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1000;
}

.voice-assistant-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.voice-assistant-icon.speaking {
  animation: pulse 1.5s infinite;
}

.voice-assistant-dropdown {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 200px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  z-index: 1000;
}

.dropdown-option {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-option:hover {
  background-color: #f0f0f0;
}

.status-indicator {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 1000;
}

.interface-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.interface-container {
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.interface-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #2e7d32;
  color: white;
}

.interface-header h2 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interface-content {
  padding: 20px;
}

.search-input-container,
.question-input-container {
  display: flex;
  margin-bottom: 15px;
}

.search-input-container input,
.question-input-container input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 16px;
}

.search-input-container button,
.question-input-container button {
  padding: 10px 15px;
  background-color: #2e7d32;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.action-btn-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #e0e0e0;
}

.action-btn.listening {
  animation: pulse 1.5s infinite;
  background-color: #ff5722;
  color: white;
}

.search-results {
  max-height: 300px;
  overflow-y: auto;
}

.search-result-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.search-result-item h3 a {
  color: #1a73e8;
  text-decoration: none;
}

.search-result-item h3 a:hover {
  text-decoration: underline;
}

.search-result-item p {
  margin: 0;
  font-size: 14px;
  color: #555;
}

.answer-container {
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 5px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.answer-text {
  font-size: 16px;
  color: #333;
  line-height: 1.6;
}

.answer-source {
  font-size: 12px;
  color: #777;
  margin-top: 10px;
  font-style: italic;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(46, 125, 50, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0);
  }
}
