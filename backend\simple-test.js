const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

console.log('Simple MongoDB Connection Test');
console.log('=============================');

// Log the MongoDB URI (with password masked)
const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse';
const maskedUri = mongoUri.replace(/:([^@]*)@/, ':****@');
console.log(`Attempting to connect to: ${maskedUri}`);

// Connect to MongoDB
mongoose.connect(mongoUri, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async (conn) => {
  console.log('\nCONNECTION SUCCESSFUL! ✅');
  console.log(`Connected to MongoDB at: ${conn.connection.host}`);
  console.log(`Database name: ${conn.connection.name}`);
  
  // Import User model
  const User = require('./models/User');
  
  // Count users
  const userCount = await User.countDocuments();
  console.log(`\nFound ${userCount} users in the database`);
  
  // Check for test users
  const testUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
  console.log('\nTest User (<EMAIL>):', testUser ? 'Found ✅' : 'Not found ❌');
  
  if (testUser) {
    console.log('- Name:', testUser.name);
    console.log('- Admin:', testUser.isAdmin ? 'Yes' : 'No');
    console.log('- Password exists:', testUser.password ? 'Yes' : 'No');
    
    // Test password match
    if (testUser.password) {
      try {
        const isMatch = await testUser.matchPassword('password123');
        console.log('- Password match test:', isMatch ? 'Success ✅' : 'Failed ❌');
      } catch (error) {
        console.error('- Password match error:', error.message);
      }
    }
  }
  
  // Check for demo user
  const demoUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
  console.log('\nDemo User (<EMAIL>):', demoUser ? 'Found ✅' : 'Not found ❌');
  
  if (demoUser) {
    console.log('- Name:', demoUser.name);
    console.log('- Admin:', demoUser.isAdmin ? 'Yes' : 'No');
    console.log('- Password exists:', demoUser.password ? 'Yes' : 'No');
    
    // Test password match
    if (demoUser.password) {
      try {
        const isMatch = await demoUser.matchPassword('password123');
        console.log('- Password match test:', isMatch ? 'Success ✅' : 'Failed ❌');
      } catch (error) {
        console.error('- Password match error:', error.message);
      }
    }
  }
  
  // Check for admin user
  const adminUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
  console.log('\nAdmin User (<EMAIL>):', adminUser ? 'Found ✅' : 'Not found ❌');
  
  if (adminUser) {
    console.log('- Name:', adminUser.name);
    console.log('- Admin:', adminUser.isAdmin ? 'Yes' : 'No');
    console.log('- Password exists:', adminUser.password ? 'Yes' : 'No');
    
    // Test password match
    if (adminUser.password) {
      try {
        const isMatch = await adminUser.matchPassword('password123');
        console.log('- Password match test:', isMatch ? 'Success ✅' : 'Failed ❌');
      } catch (error) {
        console.error('- Password match error:', error.message);
      }
    }
  }
  
  // Close the connection
  await mongoose.connection.close();
  console.log('\nConnection closed.');
  process.exit(0);
})
.catch(error => {
  console.error('\nCONNECTION FAILED! ❌');
  console.error(`Error: ${error.message}`);
  process.exit(1);
});
