// Realistic items that will be used if no items are found in localStorage
const initialMockItems = [
  {
    id: '1',
    title: 'DeWalt 20V MAX Cordless Drill',
    category: 'Tools',
    description: 'DeWalt 20V MAX cordless drill with 2 lithium-ion batteries, charger, and carrying case. Lightly used for home renovations.',
    imageUrl: 'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Available',
    transactionType: 'Exchange',
    ownerId: '123', // This is our test user
    ownerName: '<PERSON>',
    location: 'Downtown',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '2',
    title: 'Contemporary Fiction Collection',
    category: 'Books',
    description: 'Collection of 12 contemporary fiction bestsellers including works by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. All in excellent condition.',
    imageUrl: 'https://images.unsplash.com/photo-1551269901-5c5e14c25df7?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Available',
    transactionType: 'Both',
    price: 65,
    ownerId: 'user2',
    ownerName: 'Sophia Chen',
    location: 'Riverside',
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '3',
    title: 'KitchenAid Artisan Stand Mixer',
    category: 'Kitchen',
    description: 'KitchenAid Artisan 5-quart stand mixer in matte black. Includes whisk, paddle, and dough hook attachments. Used only a few times.',
    imageUrl: 'https://images.unsplash.com/photo-1594762484484-460e256b2427?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Borrowed',
    transactionType: 'Exchange',
    ownerId: 'user3',
    ownerName: 'Marcus Johnson',
    location: 'Northside',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '4',
    title: 'REI Half Dome 4-Person Tent',
    category: 'Sports',
    description: 'REI Co-op Half Dome 4-person backpacking tent. Weatherproof, easy setup, and includes footprint. Used for 2 camping trips, excellent condition.',
    imageUrl: 'https://images.unsplash.com/photo-1504851149312-7a075b496cc7?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Available',
    transactionType: 'Exchange',
    ownerId: '123', // This is our test user
    ownerName: 'James Rodriguez',
    location: 'Downtown',
    createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '5',
    title: 'Canon EOS Rebel T7 DSLR Camera',
    category: 'Electronics',
    description: 'Canon EOS Rebel T7 DSLR Camera with 18-55mm lens, 24.1MP sensor. Includes camera bag, 64GB SD card, and extra battery. Perfect for photography enthusiasts.',
    imageUrl: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Available',
    transactionType: 'Sell',
    price: 385,
    ownerId: 'user4',
    ownerName: 'Priya Patel',
    location: 'Eastside',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '6',
    title: 'Felco F-2 Classic Manual Hand Pruner',
    category: 'Garden',
    description: 'Professional-grade Felco F-2 Classic Manual Hand Pruner. Swiss-made precision cutting tool, recently sharpened and in excellent condition.',
    imageUrl: 'https://images.unsplash.com/photo-1620944862897-9c78b3d94ae8?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Available',
    transactionType: 'Both',
    price: 25,
    ownerId: 'user3',
    ownerName: 'Marcus Johnson',
    location: 'Westside',
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '7',
    title: 'Strategic Board Game Collection',
    category: 'Toys',
    description: 'Collection of 5 strategic board games: Settlers of Catan (with Seafarers expansion), Ticket to Ride Europe, Pandemic Legacy, Wingspan, and Azul. All complete with instructions.',
    imageUrl: 'https://images.unsplash.com/photo-1610890716171-6b1bb98ffd09?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Available',
    transactionType: 'Exchange',
    ownerId: 'user2',
    ownerName: 'Sophia Chen',
    location: 'Riverside',
    createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '8',
    title: 'Fender Player Stratocaster Electric Guitar',
    category: 'Music',
    description: 'Fender Player Stratocaster in Tidepool blue finish. Includes Fender Frontman 15G amplifier, gig bag, strap, tuner, and extra strings. Excellent condition, barely used.',
    imageUrl: 'https://images.unsplash.com/photo-1550291652-6ea9114a47b1?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    availability: 'Available',
    transactionType: 'Sell',
    price: 475,
    ownerId: 'user4',
    ownerName: 'Priya Patel',
    location: 'Downtown',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
  }
];

// Function to load items from localStorage or use initialMockItems if none exist
const loadItems = () => {
  try {
    const savedItems = localStorage.getItem('shared_items');
    if (savedItems) {
      const parsedItems = JSON.parse(savedItems);
      // Convert string timestamps back to Date objects if needed
      return parsedItems.map((item) => ({
        ...item,
        // Ensure createdAt is properly formatted
        createdAt: item.createdAt || new Date().toISOString()
      }));
    }
  } catch (error) {
    console.error('Error loading items from localStorage:', error);
  }

  // If no items in localStorage or error occurred, use initial mock items
  return [...initialMockItems];
};

// Export the items array that will be used throughout the app
export const mockItems = loadItems();

// Function to save items to localStorage
export const saveItems = (items) => {
  try {
    localStorage.setItem('shared_items', JSON.stringify(items));
  } catch (error) {
    console.error('Error saving items to localStorage:', error);
  }
};

export const allCategories = [
  'Tools',
  'Books',
  'Kitchen',
  'Electronics',
  'Sports',
  'Garden',
  'Toys',
  'Music',
  'Other'
];

export const allLocations = [
  'Downtown',
  'Riverside',
  'Northside',
  'Eastside',
  'Westside'
];
