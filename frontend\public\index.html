<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Urban Pulse</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f8f9fa;
        }
        .container {
            text-align: center;
            padding: 20px;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            max-width: 600px;
        }
        h1 {
            color: #0d6efd;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            font-size: 16px;
        }
        .btn-outline {
            background-color: white;
            color: #0d6efd;
            border: 1px solid #0d6efd;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #0d6efd;
            animation: spin 1s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to Urban Pulse</h1>
        <p>Empowering citizens to create smarter, more sustainable urban communities through collaboration and innovation.</p>
        
        <div id="loading" class="spinner"></div>
        
        <div id="buttons">
            <button onclick="redirectTo('/public/login-redirect.html')" class="btn">Login</button>
            <button onclick="redirectTo('/public/register-redirect.html')" class="btn">Sign Up</button>
            <button onclick="redirectTo('/')" class="btn btn-outline">Go to Home Page</button>
        </div>
        
        <div id="message" style="margin-top: 20px;"></div>
    </div>

    <script>
        function redirectTo(url) {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('buttons').style.display = 'none';
            document.getElementById('message').innerHTML = 'Redirecting, please wait...';
            
            setTimeout(function() {
                window.location.href = url;
            }, 1000);
        }
        
        // Check if we're in the root path
        if (window.location.pathname === '/' && window.location.hash === '') {
            console.log('At root path, attempting to load the app...');
            // Try to redirect to the main app after a short delay
            setTimeout(function() {
                window.location.href = '/';
            }, 2000);
        }
    </script>
</body>
</html>
