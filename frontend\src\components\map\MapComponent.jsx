import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default marker icon issue in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});

// Map click handler component
function MapClickHandler({ onClick }) {
  useMapEvents({
    click: onClick
  });
  return null;
}

const MapComponent = ({
  center = [51.505, -0.09], // Default to London
  zoom = 13,
  markers = [],
  height = '400px',
  width = '100%',
  onMapClick = null
}) => {
  // Force map to invalidate size after component mounts
  const MapInvalidator = () => {
    const map = useMapEvents({});

    useEffect(() => {
      if (map) {
        setTimeout(() => {
          map.invalidateSize();
        }, 100);
      }
    }, [map]);

    return null;
  };

  return (
    <MapContainer
      center={center}
      zoom={zoom}
      style={{ height, width }}
    >
      {onMapClick && <MapClickHandler onClick={onMapClick} />}
      <MapInvalidator />

      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />

      {markers.map((marker, index) => (
        <Marker key={index} position={[marker.lat, marker.lng]}>
          {marker.popup && (
            <Popup>
              <div dangerouslySetInnerHTML={{ __html: marker.popup }} />
            </Popup>
          )}
        </Marker>
      ))}
    </MapContainer>
  );
};

export default MapComponent;
