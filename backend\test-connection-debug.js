const mongoose = require('mongoose');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

console.log('Starting MongoDB connection test...');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
console.log(`Checking for .env file at: ${envPath}`);
if (fs.existsSync(envPath)) {
  console.log('.env file found');
} else {
  console.log('.env file NOT found');
}

// Load environment variables
dotenv.config();
console.log('Environment variables loaded');

// Check if MONGO_URI is defined
if (process.env.MONGO_URI) {
  console.log(`MONGO_URI found: ${process.env.MONGO_URI}`);
} else {
  console.log('MONGO_URI not found in environment variables');
  process.exit(1);
}

console.log('Attempting to connect to MongoDB...');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(conn => {
  console.log('\nCONNECTION SUCCESSFUL! ✅');
  console.log(`Connected to MongoDB at: ${conn.connection.host}`);
  console.log(`Database name: ${conn.connection.name}`);
  
  // Close the connection
  mongoose.connection.close();
  console.log('Connection closed.');
  process.exit(0);
})
.catch(error => {
  console.error('\nCONNECTION FAILED! ❌');
  console.error(`Error: ${error.message}`);
  process.exit(1);
});
