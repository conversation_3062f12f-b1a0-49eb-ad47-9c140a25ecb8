<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>UrbanPulse - Smart Urban Living</title>
    <meta name="description" content="A comprehensive platform for smart urban living solutions" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" />
    <!-- Bootstrap CSS for navbar dropdown functionality -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Script to check and inject auth navbar update -->
    <script>
      // Check if user is logged in
      function checkAuthState() {
        const token = localStorage.getItem('token');
        const userDataString = localStorage.getItem('user');

        if (token && userDataString) {
          try {
            // Parse user data
            const userData = JSON.parse(userDataString);
            console.log('Auth check: User is logged in as', userData.name || 'User');
            return true;
          } catch (error) {
            console.error('Auth check: Error parsing user data', error);
            return false;
          }
        }

        console.log('Auth check: User is not logged in');
        return false;
      }

      // Function to inject the navbar update script
      function injectNavbarUpdateScript() {
        // Check if the script is already injected
        if (document.querySelector('script[src="/public/force-navbar-update.js"]')) {
          console.log('Navbar update script already injected');
          return;
        }

        console.log('Injecting navbar update script');
        const script = document.createElement('script');
        script.src = '/public/force-navbar-update.js';
        document.head.appendChild(script);
      }

      // Check if user is logged in and inject script if needed
      if (checkAuthState()) {
        console.log('User is logged in, will inject navbar update script');
        // Wait for DOM to be ready
        window.addEventListener('DOMContentLoaded', function() {
          // Inject the script with a slight delay to ensure React has rendered the navbar
          setTimeout(injectNavbarUpdateScript, 500);
        });
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <!-- Bootstrap JS for navbar dropdown functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Authentication scripts -->
    <script src="/public/auth-bridge.js"></script>
    <script src="/public/fix-navbar.js"></script>
    <!-- Disable navbar-auth-check.js for React app to prevent duplicates -->
    <!-- <script src="/public/navbar-auth-check.js"></script> -->
  </body>
</html>
