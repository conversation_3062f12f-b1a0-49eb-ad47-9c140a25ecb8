const UserStats = require('../models/UserStats');

/**
 * Update user stats when a new activity is performed
 * @param {string} userId - The user ID
 * @param {string} activityType - Type of activity (issue, transport, challenge, review, resource, event, course)
 * @param {Object} activityData - Data about the activity
 */
const updateUserStats = async (userId, activityType, activityData) => {
  try {
    console.log(`Updating user stats for ${userId}, activity: ${activityType}`);
    
    // Find or create user stats
    let userStats = await UserStats.findOne({ user: userId });
    
    if (!userStats) {
      console.log('Creating new user stats record');
      userStats = new UserStats({
        user: userId,
        issuesReported: 0,
        transportFeedbacks: 0,
        challengesCompleted: 0,
        reviewsSubmitted: 0,
        resourcesShared: 0,
        eventsAttended: 0,
        coursesCompleted: 0,
        issueDetails: [],
        transportDetails: [],
        challengeDetails: [],
        reviewDetails: [],
        resourceDetails: [],
        eventDetails: [],
        courseDetails: []
      });
    }
    
    // Update the appropriate stats based on activity type
    switch (activityType) {
      case 'issue':
        userStats.issuesReported += 1;
        userStats.issueDetails.push({
          issueId: activityData._id,
          title: activityData.title,
          category: activityData.category,
          status: activityData.status || 'Open',
          reportedAt: activityData.createdAt || new Date()
        });
        break;
        
      case 'transport':
        userStats.transportFeedbacks += 1;
        userStats.transportDetails.push({
          feedbackId: activityData._id,
          title: activityData.title,
          category: activityData.category,
          submittedAt: activityData.createdAt || new Date()
        });
        break;
        
      case 'challenge':
        // Only increment if this is a completion, not just joining
        if (activityData.completed) {
          userStats.challengesCompleted += 1;
          userStats.challengeDetails.push({
            challengeId: activityData._id,
            title: activityData.title,
            category: activityData.category,
            points: activityData.points || 0,
            completedAt: activityData.completedAt || new Date()
          });
        }
        break;
        
      case 'review':
        userStats.reviewsSubmitted += 1;
        userStats.reviewDetails.push({
          reviewId: activityData._id,
          serviceId: activityData.serviceId,
          serviceName: activityData.serviceName || 'Unknown Service',
          rating: activityData.rating,
          submittedAt: activityData.createdAt || new Date()
        });
        break;
        
      case 'resource':
        userStats.resourcesShared += 1;
        userStats.resourceDetails.push({
          resourceId: activityData._id,
          title: activityData.title,
          category: activityData.category,
          sharedAt: activityData.createdAt || new Date()
        });
        break;
        
      case 'event':
        userStats.eventsAttended += 1;
        userStats.eventDetails.push({
          eventId: activityData._id,
          title: activityData.title,
          organizer: activityData.organizer,
          attendedAt: activityData.joinedAt || new Date()
        });
        break;
        
      case 'course':
        // Only increment if this is a completion, not just enrollment
        if (activityData.completed) {
          userStats.coursesCompleted += 1;
          userStats.courseDetails.push({
            courseId: activityData._id,
            title: activityData.title,
            category: activityData.category,
            completedAt: activityData.completedAt || new Date()
          });
        }
        break;
        
      default:
        console.warn(`Unknown activity type: ${activityType}`);
    }
    
    // Update the last updated timestamp
    userStats.lastUpdated = new Date();
    
    // Save the updated stats
    await userStats.save();
    console.log(`User stats updated successfully for ${userId}`);
    
    return userStats;
  } catch (error) {
    console.error('Error updating user stats:', error);
    // Don't throw the error - we don't want to break the main operation
    // if stats update fails
  }
};

module.exports = updateUserStats;
