import React, { useState, useEffect, useRef } from 'react';
import { Form, Button, Card, ListGroup, Spinner } from 'react-bootstrap';
import { FaPaperPlane, FaUser, FaUserShield } from 'react-icons/fa';
import socketService from '../services/socketService';
import api from '../utils/api';
import { formatDistanceToNow } from 'date-fns';

const ChatRoom = ({ roomId, roomType, currentUser }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef(null);

  // Create a default user if none is provided (for demo purposes)
  const chatUser = currentUser || {
    id: 'guest-user',
    name: 'Guest User',
    isAdmin: false
  };

  // Fetch previous messages when component mounts
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);

        // In a real app, this would fetch messages from the API
        // For now, we'll use mock data
        try {
          const response = await api.get(`/chat/${roomType}/${roomId}`);
          setMessages(response.data.data || []);
        } catch (error) {
          console.log('Using mock data instead of API call');

          // Realistic data for initial messages based on room type
          let mockMessages = [];

          if (roomType === 'green-space') {
            mockMessages = [
              {
                message: `Welcome to the ${roomId === 1 ? 'Riverside Memorial Park' : roomId === 2 ? 'Willow Creek Greenway Trail' : roomId === 3 ? 'Eastside Urban Harvest Garden' : roomId === 4 ? 'Oakridge Nature Preserve' : roomId === 5 ? 'Maple Street Adventure Playground' : roomId === 6 ? 'Heritage Square Urban Plaza' : 'Green Space'} community chat!`,
                user: {
                  id: 'system',
                  name: 'Urban Pulse',
                  isAdmin: true
                },
                timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
              },
              {
                message: "Has anyone noticed the new native plant installations near the entrance? They're already attracting so many pollinators!",
                user: {
                  id: 'elena-rodriguez',
                  name: 'Elena Rodriguez',
                  isAdmin: false
                },
                timestamp: new Date(Date.now() - 36 * 60 * 60 * 1000) // 36 hours ago
              },
              {
                message: "Yes, they're part of our new biodiversity initiative. We've planted over 20 species of native wildflowers and grasses. There's an interpretive sign coming next week with QR codes linking to more information about each species.",
                user: {
                  id: 'park-manager',
                  name: 'Marcus Johnson',
                  isAdmin: true
                },
                timestamp: new Date(Date.now() - 30 * 60 * 60 * 1000) // 30 hours ago
              },
              {
                message: "I've been bringing my kids here for years, and it's wonderful to see these improvements. Are there any volunteer opportunities to help maintain the new plantings?",
                user: {
                  id: 'david-wong',
                  name: 'David Wong',
                  isAdmin: false
                },
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
              },
              {
                message: "Absolutely! We have a volunteer day scheduled for this Saturday from 9am-12pm. Tools and refreshments provided. Just bring gloves and sun protection. You can sign up through the Events tab or just show up!",
                user: {
                  id: 'park-manager',
                  name: 'Marcus Johnson',
                  isAdmin: true
                },
                timestamp: new Date(Date.now() - 23 * 60 * 60 * 1000) // 23 hours ago
              },
              {
                message: "I'll be there with my family! Looking forward to helping out.",
                user: {
                  id: 'elena-rodriguez',
                  name: 'Elena Rodriguez',
                  isAdmin: false
                },
                timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000) // 12 hours ago
              }
            ];
          } else {
            // Default messages for other room types
            mockMessages = [
              {
                message: `Welcome to the ${roomType} chat room!`,
                user: {
                  id: 'system',
                  name: 'Urban Pulse',
                  isAdmin: true
                },
                timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
              },
              {
                message: "Hello everyone! I'm new to this community and looking forward to connecting with neighbors who share similar interests.",
                user: {
                  id: 'sophia-chen',
                  name: 'Sophia Chen',
                  isAdmin: false
                },
                timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000) // 48 hours ago
              },
              {
                message: "Welcome Sophia! Great to have you join us. We have several community events planned this month that you might be interested in. Check the Events tab for details.",
                user: {
                  id: 'community-coordinator',
                  name: 'Community Coordinator',
                  isAdmin: true
                },
                timestamp: new Date(Date.now() - 47 * 60 * 60 * 1000) // 47 hours ago
              },
              {
                message: "Thanks for the warm welcome! I just signed up for the workshop next weekend. Looking forward to meeting everyone in person!",
                user: {
                  id: 'sophia-chen',
                  name: 'Sophia Chen',
                  isAdmin: false
                },
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
              }
            ];
          }

          setMessages(mockMessages);
        }
      } catch (error) {
        console.error('Error fetching messages:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();

    // Join the room
    const roomIdentifier = `${roomType}-${roomId}`;
    socketService.joinGreenSpace(roomId);

    // Listen for new messages
    socketService.on('new-message', (message) => {
      setMessages(prevMessages => [...prevMessages, message]);
    });

    // Cleanup
    return () => {
      socketService.off('new-message');
    };
  }, [roomId, roomType]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!newMessage.trim()) return;

    try {
      setSending(true);
      const roomIdentifier = `${roomType}-${roomId}`;

      // Send message through socket
      socketService.sendMessage(roomIdentifier, newMessage, {
        id: chatUser.id,
        name: chatUser.name,
        isAdmin: chatUser.isAdmin || chatUser.email?.includes('admin') || false
      });

      // Also save to database (wrapped in try/catch to handle API errors)
      try {
        await api.post(`/chat/${roomType}/${roomId}`, {
          message: newMessage,
          userId: chatUser.id,
          userName: chatUser.name
        });
      } catch (apiError) {
        console.log('API call failed, but message was sent via socket:', apiError);
        // We don't need to handle this error as the socket message was already sent
      }

      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  return (
    <Card className="chat-room border-0 shadow-sm">
      <Card.Header className="bg-light d-flex justify-content-between align-items-center">
        <h5 className="mb-0">Community Chat</h5>
        <Badge bg="success" pill>{messages.length} messages</Badge>
      </Card.Header>

      <ListGroup variant="flush" className="chat-messages" style={{ height: '300px', overflowY: 'auto' }}>
        {loading ? (
          <div className="text-center p-4">
            <Spinner animation="border" variant="success" />
            <p className="mt-2">Loading messages...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center p-4 text-muted">
            <p>No messages yet. Be the first to start the conversation!</p>
          </div>
        ) : (
          messages.map((msg, index) => (
            <ListGroup.Item
              key={index}
              className={`border-0 py-2 ${msg.user?.id === chatUser.id ? 'bg-light' : ''}`}
            >
              <div className="d-flex">
                <div className="me-2">
                  {msg.user?.isAdmin ? (
                    <div className="bg-danger bg-opacity-10 rounded-circle p-1">
                      <FaUserShield className="text-danger" size={18} />
                    </div>
                  ) : (
                    <div className="bg-primary bg-opacity-10 rounded-circle p-1">
                      <FaUser className="text-primary" size={18} />
                    </div>
                  )}
                </div>
                <div className="flex-grow-1">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <strong className={msg.user?.isAdmin ? 'text-danger' : ''}>
                      {msg.user?.name || 'Anonymous'}
                      {msg.user?.isAdmin && ' (Admin)'}
                      {msg.user?.id === chatUser.id && ' (You)'}
                    </strong>
                    <small className="text-muted">
                      {msg.timestamp ? formatDistanceToNow(new Date(msg.timestamp), { addSuffix: true }) : 'just now'}
                    </small>
                  </div>
                  <p className="mb-0">{msg.message}</p>
                </div>
              </div>
            </ListGroup.Item>
          ))
        )}
        <div ref={messagesEndRef} />
      </ListGroup>

      <Card.Footer className="bg-white border-top">
        <Form onSubmit={handleSubmit}>
          <div className="d-flex">
            <Form.Control
              type="text"
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={sending}
              className="border-end-0 rounded-end-0"
              autoFocus
            />
            <Button
              variant="success"
              type="submit"
              className="rounded-start-0"
              disabled={sending || !newMessage.trim()}
            >
              {sending ? (
                <Spinner animation="border" size="sm" />
              ) : (
                <FaPaperPlane />
              )}
            </Button>
          </div>
          {!currentUser && (
            <div className="alert alert-info mt-2 py-2 small">
              <strong>Note:</strong> You are chatting as a guest. <a href="/login">Log in</a> for a personalized experience.
            </div>
          )}
        </Form>
      </Card.Footer>
    </Card>
  );
};

export default ChatRoom;
