import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Card, Form, Button, Badge, Modal, Tab, Nav } from 'react-bootstrap';
import { FaTree, FaMapMarkerAlt, FaCalendarAlt, FaUsers, FaFilter, FaSearch, FaUserPlus, FaComments, FaCheck } from 'react-icons/fa';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import MapComponent from '../components/map/MapComponent';
import ChatRoom from '../components/ChatRoom';
import api from '../utils/api';
import { useAuth } from '../contexts/AuthContext';

const GreenSpacePage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [spaceType, setSpaceType] = useState('');
  const [neighborhood, setNeighborhood] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedSpace, setSelectedSpace] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [calendarDate, setCalendarDate] = useState(new Date());
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [showChat, setShowChat] = useState(false);

  // Get current user from auth context
  const { currentUser: authUser } = useAuth();

  // Create a mock user for testing if no user is logged in
  const [currentUser, setCurrentUser] = useState(() => {
    if (authUser) return authUser;

    // Mock user for testing
    return {
      id: 'test-user-1',
      name: 'Test User',
      email: '<EMAIL>',
      isAdmin: false
    };
  });

  // Update currentUser when authUser changes
  useEffect(() => {
    if (authUser) {
      setCurrentUser(authUser);
    }
  }, [authUser]);

  // Realistic data for green space types
  const spaceTypes = [
    { id: 'park', name: 'Parks' },
    { id: 'garden', name: 'Community Gardens' },
    { id: 'trail', name: 'Walking/Jogging Trails' },
    { id: 'playground', name: 'Playgrounds' },
    { id: 'nature', name: 'Nature Reserves' },
    { id: 'plaza', name: 'Urban Plazas' }
  ];

  // Realistic data for neighborhoods
  const neighborhoods = [
    { id: 'downtown', name: 'Downtown' },
    { id: 'eastside', name: 'East Side' },
    { id: 'westside', name: 'West Side' },
    { id: 'northside', name: 'North Side' },
    { id: 'southside', name: 'South Side' }
  ];

  // Realistic data for green spaces
  const greenSpaces = [
    {
      id: 1,
      name: 'Riverside Memorial Park',
      type: 'park',
      description: 'A 12-acre urban park featuring mature oak trees, walking paths, two playgrounds, and open green spaces for recreational activities. Recently renovated with improved lighting and accessibility features.',
      neighborhood: 'downtown',
      amenities: ['Walking Paths', 'Playgrounds', 'Picnic Area', 'Public Restrooms', 'Parking', 'Dog-friendly Area', 'Basketball Court'],
      hours: '6:00 AM - 10:00 PM',
      accessibility: 'Wheelchair accessible paths, ADA-compliant restrooms, and inclusive playground equipment',
      image: 'https://images.unsplash.com/photo-1563911302283-d2bc129e7570?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 2,
      name: 'Willow Creek Greenway Trail',
      type: 'trail',
      description: 'A 4.8-mile paved trail following Willow Creek, connecting multiple neighborhoods and parks. Features scenic overlooks, native plant restoration areas, and wildlife viewing opportunities. Part of the city\'s green corridor initiative.',
      neighborhood: 'westside',
      amenities: ['Bike Racks', 'Benches', 'Water Fountains', 'Mile Markers', 'Interpretive Signage', 'Bird Watching Areas'],
      hours: '5:00 AM - 11:00 PM',
      accessibility: 'Paved trail with gentle grades, accessible entry points at major intersections',
      image: 'https://images.unsplash.com/photo-1564221710304-0b37c8b9d729?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 3,
      name: 'Eastside Urban Harvest Garden',
      type: 'garden',
      description: 'A 1.5-acre community garden established in 2015, featuring 48 individual plots, a communal herb garden, and educational growing spaces. Hosts regular workshops on urban agriculture and sustainable growing practices.',
      neighborhood: 'eastside',
      amenities: ['Garden Plots', 'Tool Shed', 'Compost Bins', 'Water Access', 'Greenhouse', 'Community Meeting Space', 'Seed Library'],
      hours: 'Dawn to Dusk',
      accessibility: 'Raised beds available for wheelchair users, wide paths between garden sections',
      image: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 4,
      name: 'Oakridge Nature Preserve',
      type: 'nature',
      description: 'A 35-acre protected natural area featuring wetlands, oak savanna, and native prairie habitats. Home to over 120 bird species and diverse plant communities. Includes a small interpretive center with educational exhibits.',
      neighborhood: 'northside',
      amenities: ['Hiking Trails', 'Bird Watching Stations', 'Educational Signage', 'Parking', 'Interpretive Center', 'Guided Tours', 'Research Areas'],
      hours: '7:00 AM - 8:00 PM (Interpretive Center: 9:00 AM - 5:00 PM)',
      accessibility: 'Accessible interpretive center, boardwalk through wetland areas, limited accessibility on natural terrain trails',
      image: 'https://images.unsplash.com/photo-1511497584788-************?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 5,
      name: 'Maple Street Adventure Playground',
      type: 'playground',
      description: 'An innovative 2-acre playground designed with input from local children, featuring nature-inspired play structures, sensory play areas, and adventure elements. Includes separate areas for different age groups and abilities.',
      neighborhood: 'southside',
      amenities: ['Inclusive Play Equipment', 'Swings', 'Splash Pad', 'Seating', 'Shade Structures', 'Restrooms', 'Water Fountains', 'Picnic Tables'],
      hours: '8:00 AM - 9:00 PM (Splash pad operational May-September)',
      accessibility: 'Universal design principles throughout, sensory-friendly areas, accessible play equipment for all abilities',
      image: 'https://images.unsplash.com/photo-1579704043197-ef2bbf4a8133?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 6,
      name: 'Heritage Square Urban Plaza',
      type: 'plaza',
      description: 'A 0.8-acre urban plaza completed in 2019 as part of the downtown revitalization project. Features sustainable design elements including permeable surfaces, native plantings, and solar-powered lighting. Hosts farmers markets and community events.',
      neighborhood: 'downtown',
      amenities: ['Seating', 'Public Art Installations', 'Free Wi-Fi', 'Food Vendor Areas', 'Performance Space', 'Interactive Water Feature', 'Bike Parking'],
      hours: '24 hours',
      accessibility: 'Fully accessible with level surfaces, multiple seating options, and clear pathways',
      image: 'https://images.unsplash.com/photo-1603228254119-e6a4d095dc59?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    }
  ];

  // Realistic data for community events
  const communityEvents = [
    {
      id: 1,
      title: 'Spring Park Clean-Up & Native Planting',
      spaceId: 1,
      spaceName: 'Riverside Memorial Park',
      description: 'Join our seasonal park clean-up and help plant native wildflowers in our new pollinator garden. All necessary tools, gloves, and materials will be provided. Suitable for all ages and abilities, with tasks available for everyone. Refreshments provided by Local Roots Café.',
      date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '9:00 AM - 12:30 PM',
      organizer: 'Parks & Recreation Department with Urban Ecology Center',
      participants: 42,
      image: 'https://images.unsplash.com/photo-1618477461853-cf6ed80faba5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 2,
      title: 'Urban Agriculture Workshop: Summer Vegetables',
      spaceId: 3,
      spaceName: 'Eastside Urban Harvest Garden',
      description: 'Learn practical techniques for growing productive summer vegetables in urban settings. This hands-on workshop covers soil preparation, companion planting, natural pest management, and water conservation. Participants will take home seedlings and resource materials. Registration required, sliding scale fee.',
      date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '10:00 AM - 12:30 PM',
      organizer: 'Urban Harvest Collective with City Extension Office',
      participants: 23,
      image: 'https://images.unsplash.com/photo-1581578017093-cd30fce4eeb7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 3,
      title: 'Migratory Bird Walk & Citizen Science Count',
      spaceId: 4,
      spaceName: 'Oakridge Nature Preserve',
      description: 'Join expert ornithologist Dr. Maria Alvarez for a guided walk focusing on spring migratory birds. Learn identification techniques, habitat requirements, and participate in our seasonal bird count contributing to national migration data. Binoculars available to borrow, but bring your own if possible. Suitable for all experience levels.',
      date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '7:00 AM - 9:30 AM',
      organizer: 'Audubon Society with Oakridge Nature Center',
      participants: 18,
      image: 'https://images.unsplash.com/photo-1516467508483-a7212febe31a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 4,
      title: 'Riparian Restoration: Native Tree Planting',
      spaceId: 2,
      spaceName: 'Willow Creek Greenway Trail',
      description: 'Participate in our ongoing riparian restoration project by helping plant native trees and shrubs along Willow Creek. This effort helps prevent erosion, improves water quality, and enhances wildlife habitat. Training, tools, and refreshments provided. Wear sturdy shoes and weather-appropriate clothing. Community service hours available for students.',
      date: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '9:00 AM - 1:00 PM',
      organizer: 'Watershed Alliance with City Urban Forestry Division',
      participants: 35,
      image: 'https://images.unsplash.com/photo-1503785640985-f62e3aeee448?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 5,
      title: 'Family Nature Play Day',
      spaceId: 5,
      spaceName: 'Maple Street Adventure Playground',
      description: 'A day of guided nature play activities for families with children ages 2-12. Stations include mud kitchen, fort building, nature art, sensory exploration, and wildlife discovery. Naturalists will be on hand to facilitate activities and answer questions. No registration required, drop in anytime during the event hours.',
      date: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '10:00 AM - 2:00 PM',
      organizer: 'Parks Department with Children & Nature Network',
      participants: 65,
      image: 'https://images.unsplash.com/photo-1472162072942-cd5147eb3902?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    }
  ];

  // Realistic data for user's events
  const userEvents = [
    {
      id: 101,
      title: 'Spring Park Clean-Up & Native Planting',
      spaceName: 'Riverside Memorial Park',
      date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '9:00 AM - 12:30 PM',
      status: 'Upcoming'
    },
    {
      id: 102,
      title: 'Urban Agriculture Workshop: Spring Planting',
      spaceName: 'Eastside Urban Harvest Garden',
      date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '10:00 AM - 12:30 PM',
      status: 'Attended'
    },
    {
      id: 103,
      title: 'Winter Bird Count',
      spaceName: 'Oakridge Nature Preserve',
      date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      time: '7:30 AM - 10:30 AM',
      status: 'Attended'
    }
  ];

  const handleViewSpace = useCallback((space) => {
    setSelectedSpace(space);
    setShowModal(true);
    // Reset chat visibility when opening a new space
    setShowChat(false);
  }, []);

  // Make the function available globally for map marker clicks
  // Using a safer approach than directly assigning to window
  React.useEffect(() => {
    window.viewSpace = (spaceId) => {
      const space = greenSpaces.find(s => s.id === parseInt(spaceId));
      if (space) {
        handleViewSpace(space);
      }
    };

    // Cleanup function to remove the global function when component unmounts
    return () => {
      delete window.viewSpace;
    };
  }, [greenSpaces, handleViewSpace]);

  const handleViewEvent = (event) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const handleJoinEvent = (eventId) => {
    // In a real app, this would make an API call to join the event
    console.log(`Joined event ${eventId}`);
    setShowEventModal(false);
  };

  // Function to prepare calendar events
  useEffect(() => {
    // In a real app, this would fetch events from the API
    // Transform communityEvents to calendar events format
    const events = communityEvents.map(event => ({
      id: event.id,
      title: event.title,
      date: new Date(event.date)
    }));

    setCalendarEvents(events);
  }, []);

  // Function to check if a date has events
  const tileContent = ({ date, view }) => {
    if (view === 'month') {
      const hasEvent = calendarEvents.some(
        event => event.date.toDateString() === date.toDateString()
      );

      return hasEvent ? (
        <div className="position-absolute bottom-0 start-50 translate-middle-x">
          <div
            className="rounded-circle bg-success"
            style={{ width: '6px', height: '6px' }}
          ></div>
        </div>
      ) : null;
    }
    return null;
  };

  // Function to handle calendar click
  const handleCalendarClick = (value) => {
    setCalendarDate(value);

    // Filter events for the selected date
    const selectedDateEvents = communityEvents.filter(
      event => new Date(event.date).toDateString() === value.toDateString()
    );

    if (selectedDateEvents.length > 0) {
      // If there's only one event on this date, show its details
      if (selectedDateEvents.length === 1) {
        handleViewEvent(selectedDateEvents[0]);
      }
      // Otherwise, we could show a list of events for that day
      // For now, we'll just log them
      console.log('Events on this date:', selectedDateEvents);
    }
  };

  // Fetch green spaces from API
  useEffect(() => {
    // In a real app, this would fetch green spaces from the API
    // For now, we're using the mock data

    // Example API call:
    // const fetchGreenSpaces = async () => {
    //   try {
    //     const response = await api.get('/green-spaces');
    //     setGreenSpaces(response.data.data);
    //   } catch (error) {
    //     console.error('Error fetching green spaces:', error);
    //   }
    // };
    //
    // fetchGreenSpaces();
  }, []);

  const filteredSpaces = greenSpaces.filter(space =>
    (searchTerm === '' || space.name.toLowerCase().includes(searchTerm.toLowerCase()) || space.description.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (spaceType === '' || space.type === spaceType) &&
    (neighborhood === '' || space.neighborhood === neighborhood)
  );

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Green Space Mapping</h1>
          <p className="lead">
            Discover parks, gardens, and join community events for a greener neighborhood.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-success bg-opacity-10 p-3 rounded">
            <FaTree className="text-success fs-1" />
          </div>
        </Col>
      </Row>

      <Tab.Container defaultActiveKey="map">
        <Row>
          <Col lg={3} className="mb-4">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-0">
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="map" className="rounded-0 px-4 py-3">
                      <FaMapMarkerAlt className="me-2" />
                      Green Spaces
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="events" className="rounded-0 px-4 py-3">
                      <FaCalendarAlt className="me-2" />
                      Community Events
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="my-events" className="rounded-0 px-4 py-3">
                      <FaUsers className="me-2" />
                      My Events
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </Card.Body>
            </Card>

            <Card className="border-0 shadow-sm mt-4">
              <Card.Body className="p-4">
                <h5 className="mb-3">Filter Spaces</h5>

                <Form.Group className="mb-3" controlId="searchTerm">
                  <Form.Label>Search</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Search green spaces..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </Form.Group>

                <Form.Group className="mb-3" controlId="spaceTypeFilter">
                  <Form.Label>Type</Form.Label>
                  <Form.Select
                    value={spaceType}
                    onChange={(e) => setSpaceType(e.target.value)}
                  >
                    <option value="">All Types</option>
                    {spaceTypes.map((type) => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>

                <Form.Group className="mb-3" controlId="neighborhoodFilter">
                  <Form.Label>Neighborhood</Form.Label>
                  <Form.Select
                    value={neighborhood}
                    onChange={(e) => setNeighborhood(e.target.value)}
                  >
                    <option value="">All Neighborhoods</option>
                    {neighborhoods.map((hood) => (
                      <option key={hood.id} value={hood.id}>
                        {hood.name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>

                <div className="d-grid">
                  <Button
                    variant="outline-success"
                    onClick={() => {
                      setSearchTerm('');
                      setSpaceType('');
                      setNeighborhood('');
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={9}>
            <Tab.Content>
              <Tab.Pane eventKey="map">
                <Card className="border-0 shadow-sm mb-4">
                  <Card.Body className="p-4">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Green Space Map</h4>
                      <Button variant="outline-success">
                        <FaFilter className="me-2" />
                        Filter Map
                      </Button>
                    </div>

                    <div className="map-container mb-4">
                      <MapComponent
                        height="500px"
                        markers={filteredSpaces.map(space => ({
                          lat: space.lat || (space.id * 0.01 + 51.5), // Using mock coordinates based on ID
                          lng: space.lng || (space.id * 0.01 - 0.1),  // Using mock coordinates based on ID
                          popup: `
                            <strong>${space.name}</strong><br/>
                            ${space.description.substring(0, 50)}...<br/>
                            <button
                              class="btn btn-sm btn-outline-success mt-2"
                              onclick="window.viewSpace(${space.id}); return false;"
                            >
                              View Details
                            </button>
                          `
                        }))}
                      />
                    </div>

                    <p className="text-muted small">
                      Note: The map would show all green spaces with markers. Clicking on a marker would show details about that space.
                    </p>
                  </Card.Body>
                </Card>

                <h4 className="mb-3">Green Spaces</h4>

                {filteredSpaces.length > 0 ? (
                  <Row xs={1} md={2} className="g-4">
                    {filteredSpaces.map((space) => (
                      <Col key={space.id}>
                        <Card className="h-100 border-0 shadow-sm">
                          <Row className="g-0">
                            <Col md={5}>
                              <div style={{ height: '100%', overflow: 'hidden' }}>
                                <Card.Img
                                  src={space.image}
                                  alt={space.name}
                                  className="h-100"
                                  style={{ objectFit: 'cover' }}
                                />
                              </div>
                            </Col>
                            <Col md={7}>
                              <Card.Body>
                                <div className="d-flex justify-content-between align-items-start mb-2">
                                  <Badge bg="success" className="mb-2">
                                    {spaceTypes.find(t => t.id === space.type)?.name}
                                  </Badge>
                                </div>
                                <Card.Title>{space.name}</Card.Title>
                                <Card.Text className="text-muted small mb-3">
                                  {space.description.substring(0, 80)}...
                                </Card.Text>
                                <div className="d-flex justify-content-between align-items-center mb-2 small text-muted">
                                  <div>
                                    <FaMapMarkerAlt className="me-1" />
                                    {neighborhoods.find(n => n.id === space.neighborhood)?.name}
                                  </div>
                                </div>
                                <Button
                                  variant="outline-success"
                                  size="sm"
                                  onClick={() => handleViewSpace(space)}
                                >
                                  View Details
                                </Button>
                              </Card.Body>
                            </Col>
                          </Row>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                ) : (
                  <Card className="border-0 shadow-sm">
                    <Card.Body className="p-5 text-center">
                      <p className="mb-3 text-muted">No green spaces found matching your criteria.</p>
                      <Button
                        variant="outline-success"
                        onClick={() => {
                          setSearchTerm('');
                          setSpaceType('');
                          setNeighborhood('');
                        }}
                      >
                        Clear Filters
                      </Button>
                    </Card.Body>
                  </Card>
                )}
              </Tab.Pane>

              <Tab.Pane eventKey="events">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <h4 className="mb-0">Upcoming Community Events</h4>
                  <div className="d-flex">
                    <div className="input-group me-2">
                      <span className="input-group-text">
                        <FaSearch />
                      </span>
                      <Form.Control
                        type="text"
                        placeholder="Search events..."
                      />
                    </div>
                    <Button variant="outline-success">
                      <FaFilter />
                    </Button>
                  </div>
                </div>

                <Row className="mb-4">
                  <Col md={6} className="mb-4 mb-md-0">
                    <Card className="border-0 shadow-sm h-100">
                      <Card.Body>
                        <h5 className="mb-3">Event Calendar</h5>
                        <div className="calendar-container">
                          <Calendar
                            onChange={handleCalendarClick}
                            value={calendarDate}
                            tileContent={tileContent}
                            className="w-100"
                          />
                        </div>
                        <div className="mt-3 text-center">
                          <small className="text-muted">
                            Click on a date with a green dot to view events
                          </small>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>

                  <Col md={6}>
                    <Card className="border-0 shadow-sm h-100">
                      <Card.Body>
                        <h5 className="mb-3">Events on {calendarDate.toLocaleDateString()}</h5>
                        {communityEvents.filter(
                          event => new Date(event.date).toDateString() === calendarDate.toDateString()
                        ).length > 0 ? (
                          <div>
                            {communityEvents
                              .filter(event => new Date(event.date).toDateString() === calendarDate.toDateString())
                              .map(event => (
                                <div key={event.id} className="mb-3 border-bottom pb-3">
                                  <h6>{event.title}</h6>
                                  <p className="text-muted small mb-2">
                                    <FaMapMarkerAlt className="me-1" />
                                    {event.spaceName} • {event.time}
                                  </p>
                                  <Button
                                    variant="outline-success"
                                    size="sm"
                                    onClick={() => handleViewEvent(event)}
                                  >
                                    View Details
                                  </Button>
                                </div>
                              ))
                            }
                          </div>
                        ) : (
                          <p className="text-muted text-center">No events scheduled for this date</p>
                        )}
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>

                <h5 className="mb-3">All Upcoming Events</h5>
                <Row xs={1} md={2} className="g-4">
                  {communityEvents.map((event) => (
                    <Col key={event.id}>
                      <Card className="h-100 border-0 shadow-sm">
                        <div style={{ height: '160px', overflow: 'hidden' }}>
                          <Card.Img
                            variant="top"
                            src={event.image}
                            alt={event.title}
                            style={{ objectFit: 'cover', height: '100%', width: '100%' }}
                          />
                        </div>
                        <Card.Body>
                          <Card.Title>{event.title}</Card.Title>
                          <Card.Text className="text-muted small mb-3">
                            {event.description.substring(0, 100)}...
                          </Card.Text>
                          <div className="d-flex justify-content-between align-items-center mb-2 small text-muted">
                            <div>
                              <FaMapMarkerAlt className="me-1" />
                              {event.spaceName}
                            </div>
                            <div>
                              <FaCalendarAlt className="me-1" />
                              {event.date}
                            </div>
                          </div>
                          <div className="d-flex justify-content-between align-items-center">
                            <small className="text-muted">
                              <FaUsers className="me-1" />
                              {event.participants} participants
                            </small>
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleViewEvent(event)}
                            >
                              View Details
                            </Button>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Tab.Pane>

              <Tab.Pane eventKey="my-events">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">My Events</h4>

                    {userEvents.length > 0 ? (
                      <div className="table-responsive">
                        <table className="table table-hover">
                          <thead>
                            <tr>
                              <th>Event</th>
                              <th>Location</th>
                              <th>Date</th>
                              <th>Time</th>
                              <th>Status</th>
                              <th>Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {userEvents.map((event) => (
                              <tr key={event.id}>
                                <td>{event.title}</td>
                                <td>{event.spaceName}</td>
                                <td>{event.date}</td>
                                <td>{event.time}</td>
                                <td>
                                  <Badge bg={event.status === 'Upcoming' ? 'primary' : 'success'}>
                                    {event.status}
                                  </Badge>
                                </td>
                                <td>
                                  <Button
                                    variant="link"
                                    className="p-0 text-decoration-none"
                                    onClick={() => handleViewEvent(
                                      communityEvents.find(e => e.title === event.title)
                                    )}
                                  >
                                    View
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-muted text-center mb-0">You haven't joined any events yet.</p>
                    )}

                    <div className="text-center mt-4">
                      <p className="text-muted mb-3">Looking for more ways to get involved?</p>
                      <Button
                        variant="success"
                        onClick={() => document.querySelector('a[href="#events"]').click()}
                      >
                        Browse Community Events
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Green Space Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Green Space Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedSpace && (
            <Row>
              <Col md={6}>
                <img
                  src={selectedSpace.image}
                  alt={selectedSpace.name}
                  className="img-fluid rounded mb-3"
                />
                <div className="mb-3">
                  <Badge bg="success" className="me-2">
                    {spaceTypes.find(t => t.id === selectedSpace.type)?.name}
                  </Badge>
                  <Badge bg="secondary">
                    {neighborhoods.find(n => n.id === selectedSpace.neighborhood)?.name}
                  </Badge>
                </div>
                <h4>{selectedSpace.name}</h4>
                <p>{selectedSpace.description}</p>
              </Col>
              <Col md={6}>
                <h5 className="mb-3">Details</h5>
                <div className="mb-3">
                  <strong>Hours:</strong> {selectedSpace.hours}
                </div>
                <div className="mb-3">
                  <strong>Accessibility:</strong> {selectedSpace.accessibility}
                </div>

                <h5 className="mb-3">Amenities</h5>
                <ul className="list-unstyled">
                  {selectedSpace.amenities.map((amenity, index) => (
                    <li key={index} className="mb-2">
                      <FaCheck className="text-success me-2" />
                      {amenity}
                    </li>
                  ))}
                </ul>

                <h5 className="mb-3 mt-4">Upcoming Events</h5>
                {communityEvents.filter(event => event.spaceId === selectedSpace.id).length > 0 ? (
                  <ul className="list-unstyled">
                    {communityEvents
                      .filter(event => event.spaceId === selectedSpace.id)
                      .map(event => (
                        <li key={event.id} className="mb-3">
                          <div className="d-flex">
                            <div className="me-3">
                              <div className="bg-light rounded p-2 text-center" style={{ width: '60px' }}>
                                <div className="small">{event.date.split('-')[1]}/{event.date.split('-')[2]}</div>
                                <div className="fw-bold">{event.date.split('-')[0]}</div>
                              </div>
                            </div>
                            <div>
                              <h6 className="mb-1">{event.title}</h6>
                              <p className="text-muted small mb-1">{event.time}</p>
                              <Button
                                variant="link"
                                className="p-0 text-decoration-none"
                                onClick={() => {
                                  setShowModal(false);
                                  handleViewEvent(event);
                                }}
                              >
                                View Details
                              </Button>
                            </div>
                          </div>
                        </li>
                      ))
                    }
                  </ul>
                ) : (
                  <p className="text-muted">No upcoming events at this location.</p>
                )}
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-between">
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          <div>
            <Button
              variant="outline-primary"
              className="me-2"
              onClick={() => setShowChat(!showChat)}
            >
              <FaComments className="me-1" /> {showChat ? 'Hide Chat' : 'Show Chat'}
            </Button>
            <Button variant="success">
              Get Directions
            </Button>
          </div>
        </Modal.Footer>

        {/* Chat Room Section */}
        {showChat && selectedSpace && (
          <div className="px-3 pb-3 border-top mt-3">
            <h5 className="mb-3 pt-3">Community Chat</h5>
            <ChatRoom
              roomId={selectedSpace.id}
              roomType="green-space"
              currentUser={currentUser}
            />
          </div>
        )}
      </Modal>

      {/* Event Details Modal */}
      <Modal show={showEventModal} onHide={() => setShowEventModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Event Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedEvent && (
            <>
              <img
                src={selectedEvent.image}
                alt={selectedEvent.title}
                className="img-fluid rounded mb-3"
              />
              <h4>{selectedEvent.title}</h4>
              <p>{selectedEvent.description}</p>

              <div className="mb-3">
                <strong>Date:</strong> {selectedEvent.date}
              </div>
              <div className="mb-3">
                <strong>Time:</strong> {selectedEvent.time}
              </div>
              <div className="mb-3">
                <strong>Location:</strong> {selectedEvent.spaceName}
              </div>
              <div className="mb-3">
                <strong>Organizer:</strong> {selectedEvent.organizer}
              </div>
              <div className="mb-3">
                <strong>Participants:</strong> {selectedEvent.participants} registered
              </div>

              <div className="alert alert-info">
                <strong>What to Bring:</strong> Water bottle, comfortable shoes, and enthusiasm!
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEventModal(false)}>
            Close
          </Button>
          <Button
            variant="success"
            onClick={() => handleJoinEvent(selectedEvent?.id)}
          >
            <FaUserPlus className="me-2" />
            Join Event
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default GreenSpacePage;
