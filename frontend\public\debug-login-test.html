<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 800px;
            margin-top: 30px;
            margin-bottom: 50px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 200px;
        }
        .log-container {
            background-color: #212529;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
        }
        .log-time {
            color: #6c757d;
        }
        .log-info {
            color: #0dcaf0;
        }
        .log-success {
            color: #198754;
        }
        .log-error {
            color: #dc3545;
        }
        .log-warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Debug Login Test</h1>
        <p class="lead">This page helps diagnose login issues with detailed logging and network monitoring.</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Login Form</h5>
                    </div>
                    <div class="card-body">
                        <div id="alert-container"></div>
                        
                        <form id="login-form">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" placeholder="Enter your password" required>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use-fetch" checked>
                                    <label class="form-check-label" for="use-fetch">
                                        Use Fetch API (uncheck to use XMLHttpRequest)
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary" id="login-button">Login</button>
                        </form>
                        
                        <div class="mt-3">
                            <h6>Test Accounts</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-sm btn-outline-secondary" id="test-user-button">Test User (<EMAIL>)</button>
                                <button class="btn btn-sm btn-outline-secondary" id="demo-user-button">Demo User (<EMAIL>)</button>
                                <button class="btn btn-sm btn-outline-secondary" id="admin-button">Admin (<EMAIL>)</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Server Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="server-status">Checking server status...</div>
                        <button class="btn btn-sm btn-outline-primary mt-3" id="check-server-button">Check Server</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Debug Log</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-container" id="log-container"></div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-sm btn-outline-secondary" id="clear-log-button">Clear Log</button>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Response</h5>
                    </div>
                    <div class="card-body">
                        <pre id="response-container">No response yet</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Local Storage</h5>
            </div>
            <div class="card-body">
                <pre id="local-storage-container">No data yet</pre>
                <button class="btn btn-sm btn-outline-danger mt-2" id="clear-storage-button">Clear Storage</button>
            </div>
        </div>
        
        <div class="d-grid gap-2">
            <a href="/" class="btn btn-outline-primary">Go to Home Page</a>
            <a href="/dashboard" class="btn btn-outline-success">Go to Dashboard</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get DOM elements
            const loginForm = document.getElementById('login-form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const useFetchCheckbox = document.getElementById('use-fetch');
            const loginButton = document.getElementById('login-button');
            const testUserButton = document.getElementById('test-user-button');
            const demoUserButton = document.getElementById('demo-user-button');
            const adminButton = document.getElementById('admin-button');
            const alertContainer = document.getElementById('alert-container');
            const responseContainer = document.getElementById('response-container');
            const localStorageContainer = document.getElementById('local-storage-container');
            const clearStorageButton = document.getElementById('clear-storage-button');
            const logContainer = document.getElementById('log-container');
            const clearLogButton = document.getElementById('clear-log-button');
            const serverStatus = document.getElementById('server-status');
            const checkServerButton = document.getElementById('check-server-button');
            
            // Function to add log entry
            function log(message, type = 'info') {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                const time = new Date().toLocaleTimeString();
                logEntry.innerHTML = `<span class="log-time">[${time}]</span> <span class="log-${type}">${message}</span>`;
                
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            // Function to clear log
            function clearLog() {
                logContainer.innerHTML = '';
                log('Log cleared');
            }
            
            // Function to show alert
            function showAlert(message, type) {
                alertContainer.innerHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            }
            
            // Function to update local storage display
            function updateLocalStorageDisplay() {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                
                let html = '';
                
                if (token) {
                    html += `<strong>Token:</strong> ${token.substring(0, 20)}...<br>`;
                } else {
                    html += '<strong>Token:</strong> Not found<br>';
                }
                
                if (user) {
                    try {
                        const userData = JSON.parse(user);
                        html += `<strong>User:</strong> ${JSON.stringify(userData, null, 2)}`;
                    } catch (error) {
                        html += `<strong>User:</strong> Error parsing user data: ${error.message}`;
                    }
                } else {
                    html += '<strong>User:</strong> Not found';
                }
                
                localStorageContainer.innerHTML = html;
            }
            
            // Function to check server status
            async function checkServerStatus() {
                serverStatus.innerHTML = 'Checking server status...';
                log('Checking server status...');
                
                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'HEAD'
                    });
                    
                    serverStatus.innerHTML = `
                        <div class="alert alert-success mb-0">
                            Server is running. Status: ${response.status}
                        </div>
                    `;
                    log(`Server is running. Status: ${response.status}`, 'success');
                } catch (error) {
                    serverStatus.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            Server is not responding: ${error.message}
                        </div>
                    `;
                    log(`Server is not responding: ${error.message}`, 'error');
                }
            }
            
            // Function to handle login with Fetch API
            async function handleLoginWithFetch(email, password) {
                log(`Attempting login with Fetch API for ${email}`);
                
                try {
                    // Show loading state
                    loginButton.disabled = true;
                    loginButton.innerHTML = `
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing in...
                    `;
                    
                    log('Preparing request...');
                    
                    // Make API request
                    log(`Sending POST request to /api/auth/login`);
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });
                    
                    log(`Received response with status: ${response.status}`);
                    
                    // Parse response
                    log('Parsing response body...');
                    const data = await response.json();
                    log('Response parsed successfully');
                    
                    // Display response
                    responseContainer.textContent = JSON.stringify(data, null, 2);
                    
                    // Handle response
                    if (response.ok) {
                        log('Login successful!', 'success');
                        showAlert('Login successful!', 'success');
                        
                        // Store token and user data
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        log('Stored token and user data in localStorage');
                        
                        // Update local storage display
                        updateLocalStorageDisplay();
                    } else {
                        log(`Login failed: ${data.message}`, 'error');
                        showAlert(data.message || 'Login failed', 'danger');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    log(`Error during login: ${error.message}`, 'error');
                    responseContainer.textContent = `Error: ${error.message}`;
                    showAlert(`Login failed: ${error.message}`, 'danger');
                } finally {
                    // Reset button
                    loginButton.disabled = false;
                    loginButton.innerHTML = 'Login';
                }
            }
            
            // Function to handle login with XMLHttpRequest
            function handleLoginWithXHR(email, password) {
                log(`Attempting login with XMLHttpRequest for ${email}`);
                
                // Show loading state
                loginButton.disabled = true;
                loginButton.innerHTML = `
                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Signing in...
                `;
                
                // Create XHR
                const xhr = new XMLHttpRequest();
                
                // Setup request
                xhr.open('POST', '/api/auth/login', true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                
                log('Preparing request...');
                
                // Setup handlers
                xhr.onreadystatechange = function() {
                    log(`XHR state changed: ${xhr.readyState}`);
                    
                    if (xhr.readyState === 4) {
                        log(`Received response with status: ${xhr.status}`);
                        
                        // Reset button
                        loginButton.disabled = false;
                        loginButton.innerHTML = 'Login';
                        
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                log('Parsing response body...');
                                const data = JSON.parse(xhr.responseText);
                                log('Response parsed successfully');
                                
                                // Display response
                                responseContainer.textContent = JSON.stringify(data, null, 2);
                                
                                log('Login successful!', 'success');
                                showAlert('Login successful!', 'success');
                                
                                // Store token and user data
                                localStorage.setItem('token', data.token);
                                localStorage.setItem('user', JSON.stringify(data.user));
                                log('Stored token and user data in localStorage');
                                
                                // Update local storage display
                                updateLocalStorageDisplay();
                            } catch (error) {
                                log(`Error parsing response: ${error.message}`, 'error');
                                responseContainer.textContent = `Error parsing response: ${error.message}`;
                                showAlert(`Error parsing response: ${error.message}`, 'danger');
                            }
                        } else {
                            try {
                                const data = JSON.parse(xhr.responseText);
                                log(`Login failed: ${data.message}`, 'error');
                                responseContainer.textContent = JSON.stringify(data, null, 2);
                                showAlert(data.message || 'Login failed', 'danger');
                            } catch (error) {
                                log(`Login failed with status ${xhr.status}`, 'error');
                                responseContainer.textContent = `Error: Request failed with status ${xhr.status}`;
                                showAlert(`Login failed with status ${xhr.status}`, 'danger');
                            }
                        }
                    }
                };
                
                xhr.onerror = function() {
                    log('XHR network error occurred', 'error');
                    responseContainer.textContent = 'Network error occurred';
                    showAlert('Network error occurred', 'danger');
                    
                    // Reset button
                    loginButton.disabled = false;
                    loginButton.innerHTML = 'Login';
                };
                
                // Send request
                log(`Sending POST request to /api/auth/login`);
                xhr.send(JSON.stringify({ email, password }));
            }
            
            // Function to handle login
            function handleLogin(email, password) {
                const useFetch = useFetchCheckbox.checked;
                
                if (useFetch) {
                    handleLoginWithFetch(email, password);
                } else {
                    handleLoginWithXHR(email, password);
                }
            }
            
            // Handle form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const password = passwordInput.value;
                
                handleLogin(email, password);
            });
            
            // Handle test user login
            testUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle demo user login
            demoUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle admin login
            adminButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle clear storage button
            clearStorageButton.addEventListener('click', function() {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                updateLocalStorageDisplay();
                log('Local storage cleared');
                showAlert('Local storage cleared', 'info');
            });
            
            // Handle clear log button
            clearLogButton.addEventListener('click', clearLog);
            
            // Handle check server button
            checkServerButton.addEventListener('click', checkServerStatus);
            
            // Initial setup
            log('Debug login test page loaded', 'info');
            updateLocalStorageDisplay();
            checkServerStatus();
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
