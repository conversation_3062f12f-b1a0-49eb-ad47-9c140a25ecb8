import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Button, ListGroup } from 'react-bootstrap';
import { FaPaperPlane, FaUser } from 'react-icons/fa';
import socketService from '../../services/socketService';

const ChatRoom = ({ roomId, roomType, currentUser }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const messagesEndRef = useRef(null);

  // Join the room when component mounts
  useEffect(() => {
    if (!roomId) return;
    
    // Connect to Socket.io if not already connected
    if (!socketService.isConnected) {
      socketService.connect();
    }
    
    // Join the appropriate room
    if (roomType === 'neighborhood') {
      socketService.joinNeighborhood(roomId);
    } else if (roomType === 'green-space') {
      socketService.joinGreenSpace(roomId);
    }
    
    setIsConnected(true);
    
    // Listen for new messages
    socketService.on('new-message', handleNewMessage);
    
    // Clean up when component unmounts
    return () => {
      socketService.off('new-message', handleNewMessage);
    };
  }, [roomId, roomType]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  // Handle new messages
  const handleNewMessage = (data) => {
    setMessages(prev => [...prev, data]);
  };
  
  // Send a new message
  const sendMessage = (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !isConnected || !currentUser) return;
    
    const room = roomType === 'neighborhood' ? `neighborhood-${roomId}` : `green-space-${roomId}`;
    
    socketService.sendMessage(room, newMessage, {
      id: currentUser.id,
      name: currentUser.name,
      avatar: currentUser.avatar
    });
    
    setNewMessage('');
  };
  
  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  return (
    <Card className="chat-container">
      <Card.Header>
        <h5 className="mb-0">
          {roomType === 'neighborhood' ? 'Neighborhood Chat' : 'Green Space Chat'}
        </h5>
      </Card.Header>
      <Card.Body className="p-0">
        <ListGroup variant="flush" className="chat-messages" style={{ maxHeight: '300px', overflowY: 'auto' }}>
          {messages.length === 0 ? (
            <ListGroup.Item className="text-center text-muted py-4">
              No messages yet. Be the first to say hello!
            </ListGroup.Item>
          ) : (
            messages.map((msg, index) => (
              <ListGroup.Item 
                key={index}
                className={`border-0 py-2 ${msg.user.id === currentUser?.id ? 'text-end' : ''}`}
              >
                <div className={`d-inline-block p-2 rounded ${msg.user.id === currentUser?.id ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '80%' }}>
                  {msg.user.id !== currentUser?.id && (
                    <div className="fw-bold small mb-1">
                      {msg.user.avatar ? (
                        <img src={msg.user.avatar} alt={msg.user.name} className="rounded-circle me-1" width="20" height="20" />
                      ) : (
                        <FaUser className="me-1" />
                      )}
                      {msg.user.name}
                    </div>
                  )}
                  <div>{msg.message}</div>
                  <div className="text-end small opacity-75">
                    {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              </ListGroup.Item>
            ))
          )}
          <div ref={messagesEndRef} />
        </ListGroup>
      </Card.Body>
      <Card.Footer className="p-2">
        <Form onSubmit={sendMessage}>
          <div className="d-flex">
            <Form.Control
              type="text"
              placeholder="Type a message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={!isConnected || !currentUser}
            />
            <Button 
              variant="primary" 
              type="submit" 
              className="ms-2"
              disabled={!isConnected || !currentUser || !newMessage.trim()}
            >
              <FaPaperPlane />
            </Button>
          </div>
          {!currentUser && (
            <div className="text-center text-muted mt-2 small">
              Please log in to participate in the chat.
            </div>
          )}
        </Form>
      </Card.Footer>
    </Card>
  );
};

export default ChatRoom;
