import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, But<PERSON>, Alert, Spinner, Row, Col, Table } from 'react-bootstrap';
import { format } from 'date-fns';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const ActivityReport = ({ userId, token }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [reportType, setReportType] = useState('monthly');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [report, setReport] = useState(null);

  // Set default dates based on report type
  useEffect(() => {
    const now = new Date();
    const endDateStr = format(now, 'yyyy-MM-dd');
    setEndDate(endDateStr);
    
    let startDateStr;
    switch (reportType) {
      case 'daily':
        startDateStr = endDateStr;
        break;
      case 'weekly':
        const weekAgo = new Date(now);
        weekAgo.setDate(weekAgo.getDate() - 7);
        startDateStr = format(weekAgo, 'yyyy-MM-dd');
        break;
      case 'monthly':
      default:
        const monthAgo = new Date(now);
        monthAgo.setDate(monthAgo.getDate() - 30);
        startDateStr = format(monthAgo, 'yyyy-MM-dd');
        break;
    }
    
    setStartDate(startDateStr);
  }, [reportType]);

  const handleGenerateReport = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError('');
      
      if (!token) {
        throw new Error('Authentication token not found');
      }
      
      // Build query parameters
      const params = new URLSearchParams({
        reportType,
        startDate,
        endDate
      });
      
      // Fetch report
      const response = await fetch(`/api/reports/activity?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate report: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Received report data:', data);
      
      if (data.success) {
        setReport(data.data);
      } else {
        throw new Error(data.error || 'Failed to generate report');
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error generating report:', error);
      setError(error.message);
      setLoading(false);
    }
  };
  
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };
  
  // Prepare chart data if report is available
  const getChartData = () => {
    if (!report || !report.summary || !report.summary.activityByDay) {
      return null;
    }
    
    const activityByDay = report.summary.activityByDay;
    const labels = Object.keys(activityByDay).map(date => format(new Date(date), 'MMM d'));
    const data = Object.values(activityByDay);
    
    return {
      labels,
      datasets: [
        {
          label: 'Activities',
          data,
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
      ],
    };
  };
  
  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Activity by Day',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      }
    }
  };
  
  const chartData = getChartData();
  
  return (
    <Card className="border-0 shadow-sm">
      <Card.Body>
        <h4 className="mb-4">Activity Report</h4>
        
        <Form onSubmit={handleGenerateReport}>
          <Row className="mb-3">
            <Col md={4}>
              <Form.Group controlId="reportType">
                <Form.Label>Report Type</Form.Label>
                <Form.Select
                  value={reportType}
                  onChange={(e) => setReportType(e.target.value)}
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group controlId="startDate">
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  required
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group controlId="endDate">
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  required
                />
              </Form.Group>
            </Col>
          </Row>
          
          <Button
            variant="primary"
            type="submit"
            disabled={loading}
          >
            {loading ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                  className="me-2"
                />
                Generating...
              </>
            ) : (
              'Generate Report'
            )}
          </Button>
        </Form>
        
        {error && (
          <Alert variant="danger" className="mt-3">
            {error}
          </Alert>
        )}
        
        {report && (
          <div className="mt-4">
            <h5>Report Summary</h5>
            <p>
              <strong>Period:</strong> {formatDate(report.summary.startDate)} to {formatDate(report.summary.endDate)}
            </p>
            
            <Row className="mb-4">
              <Col md={6}>
                <Table striped bordered hover>
                  <thead>
                    <tr>
                      <th>Activity Type</th>
                      <th>Count</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Issues Reported</td>
                      <td>{report.summary.issuesReported}</td>
                    </tr>
                    <tr>
                      <td>Transport Feedbacks</td>
                      <td>{report.summary.transportFeedbacks}</td>
                    </tr>
                    <tr>
                      <td>Challenges Completed</td>
                      <td>{report.summary.challengesCompleted}</td>
                    </tr>
                    <tr>
                      <td>Reviews Submitted</td>
                      <td>{report.summary.reviewsSubmitted}</td>
                    </tr>
                    <tr>
                      <td>Resources Shared</td>
                      <td>{report.summary.resourcesShared}</td>
                    </tr>
                    <tr>
                      <td>Events Attended</td>
                      <td>{report.summary.eventsAttended}</td>
                    </tr>
                    <tr>
                      <td>Courses Completed</td>
                      <td>{report.summary.coursesCompleted}</td>
                    </tr>
                    <tr className="table-primary">
                      <td><strong>Total Activities</strong></td>
                      <td><strong>{report.summary.totalActivities}</strong></td>
                    </tr>
                  </tbody>
                </Table>
              </Col>
              <Col md={6}>
                <Card className="h-100">
                  <Card.Body>
                    <h6>Most Active Category</h6>
                    <p className="mb-0">
                      <strong>{report.summary.mostActiveCategory.category}</strong> with {report.summary.mostActiveCategory.count} activities
                    </p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
            
            {chartData && (
              <div className="mb-4">
                <h5>Activity Trend</h5>
                <Bar data={chartData} options={chartOptions} />
              </div>
            )}
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default ActivityReport;
