<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4 p-md-5">
                        <h2 class="text-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-right me-2" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0v-2z"/>
                                <path fill-rule="evenodd" d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                            </svg>
                            Log In to Urban Pulse
                        </h2>

                        <div id="alert-container"></div>

                        <form id="login-form">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input
                                    type="email"
                                    class="form-control"
                                    id="email"
                                    placeholder="Enter your email"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <input
                                    type="password"
                                    class="form-control"
                                    id="password"
                                    placeholder="Enter your password"
                                    required
                                >
                            </div>

                            <div class="d-grid mb-3">
                                <button
                                    type="submit"
                                    class="btn btn-primary py-2"
                                    id="login-button"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-right me-2" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0v-2z"/>
                                        <path fill-rule="evenodd" d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                                    </svg>
                                    Sign In
                                </button>
                            </div>

                            <div class="text-center mb-3">
                                <p class="text-muted">- or -</p>
                            </div>

                            <div class="row mb-3">
                                <div class="col">
                                    <div class="d-grid">
                                        <button
                                            type="button"
                                            class="btn btn-outline-primary"
                                            id="demo-user-button"
                                        >
                                            Demo User Login
                                        </button>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="d-grid">
                                        <button
                                            type="button"
                                            class="btn btn-outline-danger"
                                            id="demo-admin-button"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-shield-lock me-1" viewBox="0 0 16 16">
                                                <path d="M5.338 1.59a61.44 61.44 0 0 0-2.837.856.481.481 0 0 0-.328.39c-.554 4.157.726 7.19 2.253 9.188a10.725 10.725 0 0 0 2.287 2.233c.346.244.652.42.893.533.12.057.218.095.293.118a.55.55 0 0 0 .101.025.615.615 0 0 0 .1-.025c.076-.023.174-.061.294-.118.24-.113.547-.29.893-.533a10.726 10.726 0 0 0 2.287-2.233c1.527-1.997 2.807-5.031 2.253-9.188a.48.48 0 0 0-.328-.39c-.651-.213-1.75-.56-2.837-.855C9.552 1.29 8.531 1.067 8 1.067c-.53 0-1.552.223-2.662.524zM5.072.56C6.157.265 7.31 0 8 0s1.843.265 2.928.56c1.11.3 2.229.655 2.887.87a1.54 1.54 0 0 1 1.044 1.262c.596 4.477-.787 7.795-2.465 9.99a11.775 11.775 0 0 1-2.517 2.453 7.159 7.159 0 0 1-1.048.625c-.28.132-.581.24-.829.24s-.548-.108-.829-.24a7.158 7.158 0 0 1-1.048-.625 11.777 11.777 0 0 1-2.517-2.453C1.928 10.487.545 7.169 1.141 2.692A1.54 1.54 0 0 1 2.185 1.43 62.456 62.456 0 0 1 5.072.56z"/>
                                                <path d="M9.5 6.5a1.5 1.5 0 0 1-1 1.415l.385 1.99a.5.5 0 0 1-.491.595h-.788a.5.5 0 0 1-.49-.595l.384-1.99a1.5 1.5 0 1 1 2-1.415z"/>
                                            </svg>
                                            Demo Admin Login
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">
                                Don't have an account? <a href="/public/direct-register.html" class="text-decoration-none">Sign up</a>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <p class="text-muted small">
                        For demo purposes: Use the demo buttons above or create your own account.
                        <br>
                        All features are fully functional with real-time updates.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const loginButton = document.getElementById('login-button');
            const demoUserButton = document.getElementById('demo-user-button');
            const demoAdminButton = document.getElementById('demo-admin-button');
            const alertContainer = document.getElementById('alert-container');

            // Function to show alert
            function showAlert(message, type) {
                alertContainer.innerHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            }

            // Function to handle login
            async function handleLogin(email, password) {
                try {
                    // Show loading state
                    loginButton.disabled = true;
                    loginButton.innerHTML = `
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing in...
                    `;

                    // Make API request
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });

                    const data = await response.json();

                    if (response.ok && (data.success || data.token)) {
                        // Store token and user data
                        localStorage.setItem('token', data.token);

                        // Get user data if needed
                        let userData;
                        if (data.data) {
                            userData = data.data;
                        } else if (data.user) {
                            userData = data.user;
                        } else {
                            // If no user data in response, fetch it
                            const userResponse = await fetch('/api/auth/me', {
                                headers: {
                                    'Authorization': `Bearer ${data.token}`
                                }
                            });
                            const userData = await userResponse.json();
                            userData = userData.data;
                        }

                        // Store user data
                        localStorage.setItem('user', JSON.stringify(userData));

                        // Show success message
                        showAlert('Login successful! Redirecting to home page...', 'success');

                        // Create a custom event to notify other scripts about the login
                        const loginEvent = new Event('userLoggedIn');
                        window.dispatchEvent(loginEvent);

                        // Redirect directly to home page with a special parameter to trigger navbar update
                        setTimeout(() => {
                            // Create a timestamp to ensure the URL is unique
                            const timestamp = new Date().getTime();
                            // Go directly to home page with a parameter that will trigger the navbar update
                            window.location.href = `/?auth_update=${timestamp}`;
                        }, 1500);
                    } else {
                        // Show error message
                        showAlert(data.error || 'Login failed. Please check your credentials.', 'danger');

                        // Reset button state
                        loginButton.disabled = false;
                        loginButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-right me-2" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0v-2z"/>
                                <path fill-rule="evenodd" d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                            </svg>
                            Sign In
                        `;
                    }
                } catch (error) {
                    console.error('Login error:', error);

                    // Show error message
                    showAlert('An error occurred. Please try again later.', 'danger');

                    // Reset button state
                    loginButton.disabled = false;
                    loginButton.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-right me-2" viewBox="0 0 16 16">
                            <path fill-rule="evenodd" d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0v-2z"/>
                            <path fill-rule="evenodd" d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                        </svg>
                        Sign In
                    `;
                }
            }

            // Handle form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = emailInput.value.trim();
                const password = passwordInput.value;

                // Validate inputs
                if (!email) {
                    showAlert('Email is required', 'danger');
                    return;
                }

                if (!password) {
                    showAlert('Password is required', 'danger');
                    return;
                }

                // Attempt login
                handleLogin(email, password);
            });

            // Handle demo user login
            demoUserButton.addEventListener('click', function() {
                handleLogin('<EMAIL>', 'password123');
            });

            // Handle demo admin login
            demoAdminButton.addEventListener('click', function() {
                handleLogin('<EMAIL>', 'password123');
            });
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/public/auth-refresh.js"></script>

    <!-- Script to pre-load the navbar update script -->
    <script>
        // Pre-load the navbar update script to ensure it's cached
        function preloadNavbarScript() {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = '/public/force-navbar-update.js';
            document.head.appendChild(link);
            console.log('Preloaded navbar update script');
        }

        // Execute when the page loads
        window.addEventListener('load', preloadNavbarScript);
    </script>
</body>
</html>
