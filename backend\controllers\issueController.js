const Issue = require('../models/Issue');
const updateUserStats = require('../utils/updateUserStats');

// @desc    Get all issues
// @route   GET /api/issues
// @access  Public
exports.getIssues = async (req, res, next) => {
  try {
    let query;

    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = ['select', 'sort', 'page', 'limit'];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach(param => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

    // Finding resource
    query = Issue.find(JSON.parse(queryStr)).populate('user', 'name');

    // Select Fields
    if (req.query.select) {
      const fields = req.query.select.split(',').join(' ');
      query = query.select(fields);
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const total = await Issue.countDocuments();

    query = query.skip(startIndex).limit(limit);

    // Executing query
    const issues = await query;

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }

    res.status(200).json({
      success: true,
      count: issues.length,
      pagination,
      data: issues
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Get single issue
// @route   GET /api/issues/:id
// @access  Public
exports.getIssue = async (req, res, next) => {
  try {
    const issue = await Issue.findById(req.params.id).populate('user', 'name');

    if (!issue) {
      return res.status(404).json({
        success: false,
        error: 'Issue not found'
      });
    }

    res.status(200).json({
      success: true,
      data: issue
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Create new issue
// @route   POST /api/issues
// @access  Private
exports.createIssue = async (req, res, next) => {
  try {
    console.log('Creating issue with data:', req.body);
    console.log('User:', req.user);
    console.log('File:', req.file);

    // Add user to req.body
    req.body.user = req.user.id;

    // Add photo if uploaded
    if (req.file) {
      // Create URL for the uploaded file
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      req.body.photo = `${baseUrl}/uploads/issues/${req.file.filename}`;
      console.log('Photo URL:', req.body.photo);
    }

    // Add coordinates if provided
    if (req.body.latitude && req.body.longitude) {
      req.body.coordinates = {
        lat: parseFloat(req.body.latitude),
        lng: parseFloat(req.body.longitude)
      };
      console.log('Coordinates:', req.body.coordinates);
    }

    // Ensure category is valid
    if (!req.body.category) {
      req.body.category = 'Other';
    }

    console.log('Final issue data:', req.body);
    const issue = await Issue.create(req.body);
    console.log('Issue created:', issue);

    // Update user stats
    await updateUserStats(req.user.id, 'issue', issue);

    res.status(201).json({
      success: true,
      data: issue
    });
  } catch (err) {
    console.error('Error creating issue:', err);

    // Check for validation errors
    if (err.name === 'ValidationError') {
      const messages = Object.values(err.errors).map(val => val.message);
      console.error('Validation errors:', messages);

      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: messages
      });
    }

    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Update issue
// @route   PUT /api/issues/:id
// @access  Private
exports.updateIssue = async (req, res, next) => {
  try {
    let issue = await Issue.findById(req.params.id);

    if (!issue) {
      return res.status(404).json({
        success: false,
        error: 'Issue not found'
      });
    }

    // Make sure user is issue owner or admin
    if (issue.user.toString() !== req.user.id && !req.user.isAdmin) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update this issue'
      });
    }

    issue = await Issue.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: issue
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Delete issue
// @route   DELETE /api/issues/:id
// @access  Private
exports.deleteIssue = async (req, res, next) => {
  try {
    const issue = await Issue.findById(req.params.id);

    if (!issue) {
      return res.status(404).json({
        success: false,
        error: 'Issue not found'
      });
    }

    // Make sure user is issue owner or admin
    if (issue.user.toString() !== req.user.id && !req.user.isAdmin) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to delete this issue'
      });
    }

    await issue.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};

// @desc    Add status update to issue
// @route   POST /api/issues/:id/status
// @access  Private (Admin only)
exports.addStatusUpdate = async (req, res, next) => {
  try {
    const { status, comment } = req.body;

    const issue = await Issue.findById(req.params.id).populate('user', 'name');

    if (!issue) {
      return res.status(404).json({
        success: false,
        error: 'Issue not found'
      });
    }

    // Only admins can update status
    if (!req.user.isAdmin) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update issue status'
      });
    }

    // Add status update
    const statusUpdate = {
      status,
      comment,
      updatedBy: req.user.id,
      updatedAt: new Date()
    };

    issue.statusUpdates.push(statusUpdate);

    // Update main status
    issue.status = status;

    await issue.save();

    // Get the socket.io instance
    const socket = require('../config/socket');

    // Emit the issue update event
    socket.emitIssueUpdate(issue._id, {
      issueId: issue._id,
      status,
      comment,
      updatedBy: {
        id: req.user.id,
        name: req.user.name
      },
      userId: issue.user._id, // The user who reported the issue
      timestamp: statusUpdate.updatedAt
    });

    res.status(200).json({
      success: true,
      data: issue
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      error: err.message
    });
  }
};
