{"name": "urban-pulse-backend", "version": "0.1.0", "description": "Backend API for the UrbanPulse smart urban living platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "multer": "^1.4.5-lts.1", "socket.io": "^4.8.1", "validator": "^13.9.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}}