import api from '../utils/api';

// Get all events
export const getEvents = async (params) => {
  try {
    const res = await api.get('/events', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch events');
  }
};

// Get event by ID
export const getEventById = async (id) => {
  try {
    const res = await api.get(`/events/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch event');
  }
};

// Create new event
export const createEvent = async (eventData) => {
  try {
    const res = await api.post('/events', eventData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create event');
  }
};

// Update event
export const updateEvent = async (id, eventData) => {
  try {
    const res = await api.put(`/events/${id}`, eventData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update event');
  }
};

// Delete event
export const deleteEvent = async (id) => {
  try {
    const res = await api.delete(`/events/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete event');
  }
};

// Join event
export const joinEvent = async (id) => {
  try {
    const res = await api.post(`/events/${id}/join`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to join event');
  }
};

// Leave event
export const leaveEvent = async (id) => {
  try {
    const res = await api.delete(`/events/${id}/join`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to leave event');
  }
};

// Get events for a green space
export const getSpaceEvents = async (spaceId) => {
  try {
    const res = await api.get(`/spaces/${spaceId}/events`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch space events');
  }
};

// Get user's events
export const getMyEvents = async () => {
  try {
    const res = await api.get('/events/my-events');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch your events');
  }
};
