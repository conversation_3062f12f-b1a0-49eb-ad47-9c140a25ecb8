<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Debug Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 900px;
            margin-top: 30px;
            margin-bottom: 50px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
        }
        .card {
            margin-bottom: 20px;
        }
        .btn-debug {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Login Debug Tool</h1>
        <p class="lead">This tool helps diagnose login issues by testing various components of the authentication system.</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Server Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="server-status">Checking server status...</div>
                        <button class="btn btn-sm btn-outline-primary mt-3" id="check-server-button">Check Server</button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Database Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="db-status">Checking database status...</div>
                        <button class="btn btn-sm btn-outline-primary mt-3" id="check-db-button">Check Database</button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">User Check</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="check-email" class="form-label">Email to check</label>
                            <input type="email" class="form-control" id="check-email" placeholder="Enter email to check">
                        </div>
                        <button class="btn btn-sm btn-outline-primary" id="check-user-button">Check User</button>
                        <div id="user-status" class="mt-3">Enter an email and click "Check User"</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Debug Login</h5>
                    </div>
                    <div class="card-body">
                        <div id="alert-container"></div>
                        
                        <form id="debug-login-form">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" placeholder="Enter your password" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary" id="login-button">Debug Login</button>
                        </form>
                        
                        <div class="mt-3">
                            <h6>Test Accounts</h6>
                            <div class="d-flex flex-wrap">
                                <button class="btn btn-sm btn-outline-secondary btn-debug" id="test-user-button">Test User</button>
                                <button class="btn btn-sm btn-outline-secondary btn-debug" id="demo-user-button">Demo User</button>
                                <button class="btn btn-sm btn-outline-secondary btn-debug" id="admin-button">Admin</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Response</h5>
                    </div>
                    <div class="card-body">
                        <pre id="response-container">No response yet</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Local Storage</h5>
            </div>
            <div class="card-body">
                <pre id="local-storage-container">Checking local storage...</pre>
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-danger" id="clear-storage-button">Clear Local Storage</button>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap">
                    <a href="/public/test-login.html" class="btn btn-outline-primary me-2 mb-2">Regular Login Test</a>
                    <a href="/" class="btn btn-outline-primary me-2 mb-2">Go to Home Page</a>
                    <a href="/dashboard" class="btn btn-outline-primary me-2 mb-2">Go to Dashboard</a>
                    <button class="btn btn-outline-secondary me-2 mb-2" id="list-users-button">List All Users</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get DOM elements
            const debugLoginForm = document.getElementById('debug-login-form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const loginButton = document.getElementById('login-button');
            const testUserButton = document.getElementById('test-user-button');
            const demoUserButton = document.getElementById('demo-user-button');
            const adminButton = document.getElementById('admin-button');
            const alertContainer = document.getElementById('alert-container');
            const responseContainer = document.getElementById('response-container');
            const localStorageContainer = document.getElementById('local-storage-container');
            const serverStatus = document.getElementById('server-status');
            const dbStatus = document.getElementById('db-status');
            const userStatus = document.getElementById('user-status');
            const checkServerButton = document.getElementById('check-server-button');
            const checkDbButton = document.getElementById('check-db-button');
            const checkUserButton = document.getElementById('check-user-button');
            const checkEmailInput = document.getElementById('check-email');
            const clearStorageButton = document.getElementById('clear-storage-button');
            const listUsersButton = document.getElementById('list-users-button');
            
            // Function to show alert
            function showAlert(message, type) {
                alertContainer.innerHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            }
            
            // Function to update local storage display
            function updateLocalStorageDisplay() {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                
                let html = '';
                
                if (token) {
                    html += `<strong>Token:</strong> ${token.substring(0, 20)}...<br>`;
                } else {
                    html += '<strong>Token:</strong> Not found<br>';
                }
                
                if (user) {
                    try {
                        const userData = JSON.parse(user);
                        html += `<strong>User:</strong> ${JSON.stringify(userData, null, 2)}`;
                    } catch (error) {
                        html += `<strong>User:</strong> Error parsing user data: ${error.message}`;
                    }
                } else {
                    html += '<strong>User:</strong> Not found';
                }
                
                localStorageContainer.innerHTML = html;
            }
            
            // Function to check server status
            async function checkServerStatus() {
                serverStatus.innerHTML = 'Checking server status...';
                
                try {
                    const response = await fetch('/api/debug');
                    const data = await response.json();
                    
                    serverStatus.innerHTML = `
                        <div class="alert alert-success mb-0">
                            Server is running.<br>
                            Timestamp: ${data.timestamp}<br>
                            Environment: ${data.environment}
                        </div>
                    `;
                } catch (error) {
                    serverStatus.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            Server is not responding: ${error.message}
                        </div>
                    `;
                }
            }
            
            // Function to check database status
            async function checkDbStatus() {
                dbStatus.innerHTML = 'Checking database status...';
                
                try {
                    const response = await fetch('/api/debug/db');
                    const data = await response.json();
                    
                    if (data.status === 'success') {
                        dbStatus.innerHTML = `
                            <div class="alert alert-success mb-0">
                                Database is connected.<br>
                                State: ${data.connection.stateText} (${data.connection.state})<br>
                                Host: ${data.database.host}<br>
                                Name: ${data.database.name}<br>
                                Users: ${data.collections.users}
                            </div>
                        `;
                    } else {
                        dbStatus.innerHTML = `
                            <div class="alert alert-warning mb-0">
                                Database check returned an error: ${data.message}
                            </div>
                        `;
                    }
                } catch (error) {
                    dbStatus.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            Database check failed: ${error.message}
                        </div>
                    `;
                }
            }
            
            // Function to check user
            async function checkUser(email) {
                userStatus.innerHTML = 'Checking user...';
                
                try {
                    const response = await fetch('/api/debug/check-user', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email })
                    });
                    
                    const data = await response.json();
                    
                    if (data.status === 'success') {
                        userStatus.innerHTML = `
                            <div class="alert alert-success mb-0">
                                User found:<br>
                                Name: ${data.user.name}<br>
                                Email: ${data.user.email}<br>
                                Admin: ${data.user.isAdmin ? 'Yes' : 'No'}<br>
                                Password: ${data.user.passwordExists ? 'Exists' : 'Missing'}
                            </div>
                        `;
                    } else if (data.status === 'not_found') {
                        userStatus.innerHTML = `
                            <div class="alert alert-warning mb-0">
                                User not found with email: ${email}
                            </div>
                        `;
                    } else {
                        userStatus.innerHTML = `
                            <div class="alert alert-danger mb-0">
                                Error checking user: ${data.message}
                            </div>
                        `;
                    }
                } catch (error) {
                    userStatus.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            User check failed: ${error.message}
                        </div>
                    `;
                }
            }
            
            // Function to handle debug login
            async function handleDebugLogin(email, password) {
                try {
                    // Show loading state
                    loginButton.disabled = true;
                    loginButton.innerHTML = `
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Debugging...
                    `;
                    
                    // Make API request
                    const response = await fetch('/api/debug/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });
                    
                    // Parse response
                    const data = await response.json();
                    
                    // Display response
                    responseContainer.textContent = JSON.stringify(data, null, 2);
                    
                    // Handle response
                    if (response.ok && data.status === 'success') {
                        showAlert('Debug login successful!', 'success');
                        
                        // Store token and user data
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        
                        // Update local storage display
                        updateLocalStorageDisplay();
                    } else {
                        showAlert(data.message || 'Debug login failed', 'danger');
                    }
                } catch (error) {
                    console.error('Debug login error:', error);
                    responseContainer.textContent = `Error: ${error.message}`;
                    showAlert(`Debug login failed: ${error.message}`, 'danger');
                } finally {
                    // Reset button
                    loginButton.disabled = false;
                    loginButton.innerHTML = 'Debug Login';
                }
            }
            
            // Function to list all users
            async function listAllUsers() {
                try {
                    const response = await fetch('/api/debug/users');
                    const data = await response.json();
                    
                    responseContainer.textContent = JSON.stringify(data, null, 2);
                    
                    if (data.status === 'success') {
                        showAlert(`Found ${data.count} users`, 'info');
                    } else {
                        showAlert(data.message || 'Failed to list users', 'warning');
                    }
                } catch (error) {
                    console.error('List users error:', error);
                    responseContainer.textContent = `Error: ${error.message}`;
                    showAlert(`Failed to list users: ${error.message}`, 'danger');
                }
            }
            
            // Handle form submission
            debugLoginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const password = passwordInput.value;
                
                handleDebugLogin(email, password);
            });
            
            // Handle test user login
            testUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleDebugLogin('<EMAIL>', 'password123');
            });
            
            // Handle demo user login
            demoUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleDebugLogin('<EMAIL>', 'password123');
            });
            
            // Handle admin login
            adminButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleDebugLogin('<EMAIL>', 'password123');
            });
            
            // Handle check server button
            checkServerButton.addEventListener('click', checkServerStatus);
            
            // Handle check database button
            checkDbButton.addEventListener('click', checkDbStatus);
            
            // Handle check user button
            checkUserButton.addEventListener('click', function() {
                const email = checkEmailInput.value.trim();
                if (email) {
                    checkUser(email);
                } else {
                    userStatus.innerHTML = `
                        <div class="alert alert-warning mb-0">
                            Please enter an email to check
                        </div>
                    `;
                }
            });
            
            // Handle clear storage button
            clearStorageButton.addEventListener('click', function() {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                updateLocalStorageDisplay();
                showAlert('Local storage cleared', 'info');
            });
            
            // Handle list users button
            listUsersButton.addEventListener('click', listAllUsers);
            
            // Initial checks
            updateLocalStorageDisplay();
            checkServerStatus();
            checkDbStatus();
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
