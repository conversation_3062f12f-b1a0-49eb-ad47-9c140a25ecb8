const UserStats = require('../models/UserStats');
const User = require('../models/User');

/**
 * Generate a user activity report
 * @param {string} userId - The user ID
 * @param {string} reportType - Type of report (daily, weekly, monthly)
 * @param {Date} startDate - Start date for the report
 * @param {Date} endDate - End date for the report
 */
const generateUserActivityReport = async (userId, reportType = 'monthly', startDate = null, endDate = null) => {
  try {
    // Find user stats
    const userStats = await UserStats.findOne({ user: userId });
    
    if (!userStats) {
      return {
        success: false,
        error: 'No activity data found for this user'
      };
    }
    
    // Get user info
    const user = await User.findById(userId);
    
    if (!user) {
      return {
        success: false,
        error: 'User not found'
      };
    }
    
    // Set default date range if not provided
    const now = new Date();
    
    if (!endDate) {
      endDate = now;
    }
    
    if (!startDate) {
      switch (reportType) {
        case 'daily':
          // Start of today
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'weekly':
          // 7 days ago
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'monthly':
        default:
          // 30 days ago
          startDate = new Date(now);
          startDate.setDate(startDate.getDate() - 30);
          break;
      }
    }
    
    // Filter activity data by date range
    const filteredData = {
      issues: filterByDateRange(userStats.issueDetails, 'reportedAt', startDate, endDate),
      transport: filterByDateRange(userStats.transportDetails, 'submittedAt', startDate, endDate),
      challenges: filterByDateRange(userStats.challengeDetails, 'completedAt', startDate, endDate),
      reviews: filterByDateRange(userStats.reviewDetails, 'submittedAt', startDate, endDate),
      resources: filterByDateRange(userStats.resourceDetails, 'sharedAt', startDate, endDate),
      events: filterByDateRange(userStats.eventDetails, 'attendedAt', startDate, endDate),
      courses: filterByDateRange(userStats.courseDetails, 'completedAt', startDate, endDate)
    };
    
    // Calculate summary statistics
    const summary = {
      totalActivities: 0,
      issuesReported: filteredData.issues.length,
      transportFeedbacks: filteredData.transport.length,
      challengesCompleted: filteredData.challenges.length,
      reviewsSubmitted: filteredData.reviews.length,
      resourcesShared: filteredData.resources.length,
      eventsAttended: filteredData.events.length,
      coursesCompleted: filteredData.courses.length,
      mostActiveCategory: getMostActiveCategory(filteredData),
      activityByDay: getActivityByDay(filteredData, startDate, endDate),
      startDate,
      endDate
    };
    
    // Calculate total activities
    summary.totalActivities = Object.values(filteredData).reduce((sum, arr) => sum + arr.length, 0);
    
    return {
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email
        },
        reportType,
        summary,
        details: filteredData
      }
    };
  } catch (error) {
    console.error('Error generating user activity report:', error);
    return {
      success: false,
      error: 'Failed to generate report'
    };
  }
};

/**
 * Filter an array of objects by date range
 * @param {Array} data - Array of objects with date field
 * @param {string} dateField - Name of the date field
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 */
const filterByDateRange = (data, dateField, startDate, endDate) => {
  if (!data || !Array.isArray(data)) return [];
  
  return data.filter(item => {
    const itemDate = new Date(item[dateField]);
    return itemDate >= startDate && itemDate <= endDate;
  });
};

/**
 * Get the most active category across all activity types
 * @param {Object} data - Filtered activity data
 */
const getMostActiveCategory = (data) => {
  // Combine all categories from different activity types
  const categories = {};
  
  // Process issues
  data.issues.forEach(issue => {
    const category = issue.category || 'Unknown';
    categories[category] = (categories[category] || 0) + 1;
  });
  
  // Process challenges
  data.challenges.forEach(challenge => {
    const category = challenge.category || 'Unknown';
    categories[category] = (categories[category] || 0) + 1;
  });
  
  // Process resources
  data.resources.forEach(resource => {
    const category = resource.category || 'Unknown';
    categories[category] = (categories[category] || 0) + 1;
  });
  
  // Process courses
  data.courses.forEach(course => {
    const category = course.category || 'Unknown';
    categories[category] = (categories[category] || 0) + 1;
  });
  
  // Find the category with the highest count
  let mostActiveCategory = 'None';
  let highestCount = 0;
  
  Object.entries(categories).forEach(([category, count]) => {
    if (count > highestCount) {
      mostActiveCategory = category;
      highestCount = count;
    }
  });
  
  return {
    category: mostActiveCategory,
    count: highestCount
  };
};

/**
 * Get activity counts by day
 * @param {Object} data - Filtered activity data
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 */
const getActivityByDay = (data, startDate, endDate) => {
  const activityByDay = {};
  
  // Initialize all days in the range with zero counts
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dateString = currentDate.toISOString().split('T')[0];
    activityByDay[dateString] = 0;
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // Count activities by day
  const countByDay = (items, dateField) => {
    items.forEach(item => {
      const date = new Date(item[dateField]);
      const dateString = date.toISOString().split('T')[0];
      if (activityByDay[dateString] !== undefined) {
        activityByDay[dateString]++;
      }
    });
  };
  
  // Count all activity types
  countByDay(data.issues, 'reportedAt');
  countByDay(data.transport, 'submittedAt');
  countByDay(data.challenges, 'completedAt');
  countByDay(data.reviews, 'submittedAt');
  countByDay(data.resources, 'sharedAt');
  countByDay(data.events, 'attendedAt');
  countByDay(data.courses, 'completedAt');
  
  return activityByDay;
};

module.exports = {
  generateUserActivityReport
};
