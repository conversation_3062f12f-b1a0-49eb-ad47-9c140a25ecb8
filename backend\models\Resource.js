const mongoose = require('mongoose');

const ResourceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: ['tools', 'garden', 'kitchen', 'sports', 'books', 'parking', 'skills', 'other']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  neighborhood: {
    type: String,
    required: [true, 'Please add a neighborhood']
  },
  availability: {
    type: String,
    required: [true, 'Please add availability information']
  },
  condition: {
    type: String,
    enum: ['New', 'Like New', 'Good', 'Fair', 'N/A']
  },
  status: {
    type: String,
    enum: ['Available', 'Sold'],
    default: 'Available'
  },
  image: {
    type: String
  },
  availabilityCalendar: [
    {
      date: {
        type: Date,
        required: true
      },
      isAvailable: {
        type: Boolean,
        default: true
      }
    }
  ],
  requests: [
    {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
      },
      startDate: {
        type: Date,
        required: true
      },
      endDate: {
        type: Date,
        required: true
      },
      status: {
        type: String,
        enum: ['Pending', 'Approved', 'Rejected', 'Completed'],
        default: 'Pending'
      },
      message: {
        type: String
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  messages: [
    {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
      },
      userName: {
        type: String,
        required: true
      },
      content: {
        type: String,
        required: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      },
      isOwner: {
        type: Boolean,
        default: false
      }
    }
  ],
  owner: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Virtual for request count
ResourceSchema.virtual('requestCount').get(function() {
  return this.requests.length;
});

// Enable virtuals
ResourceSchema.set('toJSON', { virtuals: true });
ResourceSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Resource', ResourceSchema);
