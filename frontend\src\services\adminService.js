import api from '../utils/api';

// Get all issues
export const getAllIssues = async () => {
  try {
    const res = await api.get('/issues');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch issues');
  }
};

// Get all transport feedback
export const getAllTransportFeedback = async () => {
  try {
    const res = await api.get('/transport');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch transport feedback');
  }
};

// Get all reviews
export const getAllReviews = async () => {
  try {
    const res = await api.get('/reviews');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch reviews');
  }
};

// Get all users
export const getAllUsers = async () => {
  try {
    const res = await api.get('/auth/users');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch users');
  }
};

// Get dashboard stats
export const getDashboardStats = async () => {
  try {
    const res = await api.get('/admin/stats');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch dashboard stats');
  }
};

// Update issue status
export const updateIssueStatus = async (issueId, data) => {
  try {
    const res = await api.post(`/issues/${issueId}/status`, data);
    return res.data;
  } catch (error) {
    console.error('Error updating issue status:', error);
    throw error.response ? error.response.data : new Error('Failed to update issue status');
  }
};
