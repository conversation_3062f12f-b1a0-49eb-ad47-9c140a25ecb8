const mongoose = require('mongoose');

const GreenSpaceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  type: {
    type: String,
    required: [true, 'Please add a type'],
    enum: ['park', 'garden', 'trail', 'playground', 'nature', 'plaza']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  neighborhood: {
    type: String,
    required: [true, 'Please add a neighborhood']
  },
  amenities: {
    type: [String]
  },
  hours: {
    type: String
  },
  accessibility: {
    type: String
  },
  image: {
    type: String
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      index: '2dsphere'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Reverse populate with events
GreenSpaceSchema.virtual('events', {
  ref: 'Event',
  localField: '_id',
  foreignField: 'spaceId',
  justOne: false
});

module.exports = mongoose.model('GreenSpace', GreenSpaceSchema);
