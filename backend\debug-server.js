const express = require('express');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Create Express app
const app = express();
app.use(express.json());

// Import User model
const User = require('./models/User');

// Debug route to check if server is running
app.get('/api/debug', (req, res) => {
  res.json({
    message: 'Debug server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Debug route to check MongoDB connection
app.get('/api/debug/db', async (req, res) => {
  try {
    // Check MongoDB connection
    const dbState = mongoose.connection.readyState;
    const dbStateText = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    }[dbState];

    // Get database info
    const dbInfo = {
      host: mongoose.connection.host,
      name: mongoose.connection.name,
      port: mongoose.connection.port,
      models: Object.keys(mongoose.models)
    };

    // Count users in database
    const userCount = await User.countDocuments();

    res.json({
      status: 'success',
      connection: {
        state: dbState,
        stateText: dbStateText
      },
      database: dbInfo,
      collections: {
        users: userCount
      }
    });
  } catch (error) {
    console.error('Database debug error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Debug route to list all users (only in development)
app.get('/api/debug/users', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({ message: 'Not allowed in production' });
    }

    // Get all users (excluding password)
    const users = await User.find().select('-password');

    res.json({
      status: 'success',
      count: users.length,
      users: users.map(user => ({
        id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin,
        createdAt: user.createdAt
      }))
    });
  } catch (error) {
    console.error('Users debug error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

// Debug route to check a specific user
app.post('/api/debug/check-user', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Find user by email
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.json({
        status: 'not_found',
        message: 'User not found'
      });
    }

    // Return user info (excluding actual password)
    res.json({
      status: 'success',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin,
        createdAt: user.createdAt,
        passwordExists: !!user.password,
        passwordLength: user.password ? user.password.length : 0
      }
    });
  } catch (error) {
    console.error('Check user debug error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

// Debug login route with detailed logging
app.post('/api/debug/login', async (req, res) => {
  console.log('Debug login request received:', {
    body: { ...req.body, password: req.body.password ? '********' : undefined },
    headers: req.headers
  });

  try {
    const { email, password } = req.body;

    // Validate inputs
    if (!email || !password) {
      console.log('Debug login: Missing email or password');
      return res.status(400).json({
        status: 'error',
        message: 'Please provide both email and password'
      });
    }

    console.log(`Debug login: Looking for user with email: ${email}`);
    
    // Find user by email
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      console.log('Debug login: User not found');
      return res.status(401).json({
        status: 'error',
        message: 'Invalid credentials (user not found)'
      });
    }

    console.log('Debug login: User found, checking password');
    
    // Check if password matches
    try {
      const isMatch = await user.matchPassword(password);
      
      console.log(`Debug login: Password match result: ${isMatch}`);

      if (!isMatch) {
        return res.status(401).json({
          status: 'error',
          message: 'Invalid credentials (password mismatch)'
        });
      }
    } catch (passwordError) {
      console.error('Debug login: Error checking password:', passwordError);
      return res.status(500).json({
        status: 'error',
        message: 'Error validating password',
        error: passwordError.message
      });
    }

    console.log('Debug login: Authentication successful, generating token');
    
    // Generate JWT token
    const token = jwt.sign(
      { id: user._id, email: user.email, isAdmin: user.isAdmin || false },
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production',
      { expiresIn: '24h' }
    );

    console.log('Debug login: Token generated successfully');
    
    // Return success response
    res.json({
      status: 'success',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin || false
      }
    });
  } catch (error) {
    console.error('Debug login error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Server error during login',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Connect to MongoDB
console.log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
  
  // Start server
  const PORT = process.env.PORT || 5005;
  app.listen(PORT, () => {
    console.log(`Debug server running on port ${PORT}`);
    console.log(`MongoDB Connection State: ${mongoose.connection.readyState}`);
    console.log(`Database Name: ${mongoose.connection.name}`);
  });
})
.catch(error => {
  console.error('MongoDB connection error:', error);
  process.exit(1);
});
