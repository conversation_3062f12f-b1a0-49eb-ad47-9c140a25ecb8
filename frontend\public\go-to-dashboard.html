<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Going to Dashboard - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .icon {
            width: 80px;
            height: 80px;
            background-color: #0d6efd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }
        .icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        .spinner {
            width: 3rem;
            height: 3rem;
            margin: 1.5rem auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
        </div>
        <h2 class="mb-3">Going to Dashboard</h2>
        <p class="mb-4">Please wait while we redirect you to your personalized dashboard.</p>
        <div class="spinner-border text-primary spinner" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div id="status-message" class="mt-3"></div>
    </div>

    <script>
        // Function to check if user is logged in
        function checkAuthStatus() {
            const token = localStorage.getItem('token');
            const userDataString = localStorage.getItem('user');
            
            if (token && userDataString) {
                try {
                    // Parse user data
                    const userData = JSON.parse(userDataString);
                    document.getElementById('status-message').innerHTML = `
                        <div class="alert alert-success">
                            Logged in as: <strong>${userData.name || 'User'}</strong>
                        </div>
                    `;
                    return true;
                } catch (error) {
                    document.getElementById('status-message').innerHTML = `
                        <div class="alert alert-danger">
                            Error parsing user data: ${error.message}
                        </div>
                    `;
                    return false;
                }
            }
            
            document.getElementById('status-message').innerHTML = `
                <div class="alert alert-warning">
                    Not logged in. Please <a href="/public/direct-login.html">login</a> first.
                </div>
            `;
            return false;
        }
        
        // Function to redirect to dashboard
        function redirectToDashboard() {
            const isLoggedIn = checkAuthStatus();
            
            setTimeout(() => {
                if (isLoggedIn) {
                    // Redirect to dashboard
                    window.location.href = '/dashboard';
                } else {
                    // Show login option
                    document.querySelector('.spinner').style.display = 'none';
                    document.getElementById('status-message').innerHTML += `
                        <div class="mt-3">
                            <a href="/public/direct-login.html" class="btn btn-primary">Go to Login</a>
                        </div>
                    `;
                }
            }, 2000);
        }
        
        // Run the check and redirect
        redirectToDashboard();
    </script>
</body>
</html>
