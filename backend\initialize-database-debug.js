const mongoose = require('mongoose');
const dotenv = require('dotenv');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

console.log('Starting database initialization...');

// Check if models exist
const modelsDir = path.join(__dirname, 'models');
console.log(`Checking models directory: ${modelsDir}`);
if (fs.existsSync(modelsDir)) {
  console.log('Models directory found');
  const files = fs.readdirSync(modelsDir);
  console.log('Model files:', files);
} else {
  console.log('Models directory NOT found');
}

// Load environment variables
dotenv.config();
console.log(`MONGO_URI: ${process.env.MONGO_URI}`);

// Import models
console.log('Importing models...');
try {
  const User = require('./models/User');
  console.log('User model imported');
  
  // Connect to MongoDB
  console.log('Connecting to MongoDB...');
  mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(async () => {
    console.log('Connected to MongoDB');
    
    try {
      // Create admin user if it doesn't exist
      const adminExists = await User.findOne({ email: '<EMAIL>' });
      
      if (!adminExists) {
        console.log('Creating admin user...');
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash('admin123', salt);
        
        await User.create({
          name: 'Admin User',
          email: '<EMAIL>',
          password: hashedPassword,
          isAdmin: true
        });
        console.log('Admin user created');
      } else {
        console.log('Admin user already exists');
      }
      
      // Create test user if it doesn't exist
      const testUserExists = await User.findOne({ email: '<EMAIL>' });
      
      if (!testUserExists) {
        console.log('Creating test user...');
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash('password123', salt);
        
        await User.create({
          name: 'Test User',
          email: '<EMAIL>',
          password: hashedPassword,
          isAdmin: false
        });
        console.log('Test user created');
      } else {
        console.log('Test user already exists');
      }
      
      console.log('Checking collections...');
      const collections = await mongoose.connection.db.listCollections().toArray();
      console.log('Collections after user creation:');
      collections.forEach(collection => {
        console.log(`- ${collection.name}`);
      });
      
      console.log('\nDatabase initialization complete!');
      
    } catch (error) {
      console.error('Error initializing database:', error);
    }
    
    // Close the connection
    await mongoose.connection.close();
    console.log('Connection closed');
    process.exit(0);
  })
  .catch(error => {
    console.error('MongoDB connection error:', error.message);
    process.exit(1);
  });
} catch (error) {
  console.error('Error importing models:', error);
  process.exit(1);
}
