<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #0d6efd;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-outline {
            background-color: white;
            color: #0d6efd;
            border: 1px solid #0d6efd;
        }
    </style>
</head>
<body>
    <h1>Navigation Test Page</h1>
    <p>Click the buttons below to test navigation to different pages:</p>
    
    <div>
        <a href="/login" class="btn">Login Page</a>
        <a href="/register" class="btn">Register Page</a>
        <a href="/dashboard" class="btn">Dashboard</a>
        <a href="/" class="btn btn-outline">Home Page</a>
    </div>

    <h2>Direct JavaScript Navigation</h2>
    <div>
        <button onclick="window.location.href='/login'" class="btn">Login (JS)</button>
        <button onclick="window.location.href='/register'" class="btn">Register (JS)</button>
        <button onclick="window.location.href='/dashboard'" class="btn">Dashboard (JS)</button>
        <button onclick="window.location.href='/'" class="btn btn-outline">Home (JS)</button>
    </div>

    <h2>Full URL Navigation</h2>
    <div>
        <button onclick="window.location.href='http://localhost:3000/login'" class="btn">Login (Full URL)</button>
        <button onclick="window.location.href='http://localhost:3000/register'" class="btn">Register (Full URL)</button>
        <button onclick="window.location.href='http://localhost:3000/dashboard'" class="btn">Dashboard (Full URL)</button>
        <button onclick="window.location.href='http://localhost:3000/'" class="btn btn-outline">Home (Full URL)</button>
    </div>

    <script>
        // Log current URL for debugging
        console.log('Current URL:', window.location.href);
    </script>
</body>
</html>
