<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 800px;
            margin-top: 30px;
            margin-bottom: 50px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 1rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Dashboard Test</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">User Information</h5>
            </div>
            <div class="card-body">
                <div id="user-info">Loading user information...</div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">User Stats</h5>
            </div>
            <div class="card-body">
                <div id="stats-loading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading user stats...</p>
                </div>
                
                <div id="stats-error" class="alert alert-danger" style="display: none;">
                    Failed to load user stats. Please try again later.
                </div>
                
                <div id="stats-container" class="row" style="display: none;">
                    <!-- Stats will be inserted here -->
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Activity Details</h5>
            </div>
            <div class="card-body">
                <div id="activity-loading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading activity details...</p>
                </div>
                
                <div id="activity-error" class="alert alert-danger" style="display: none;">
                    Failed to load activity details. Please try again later.
                </div>
                
                <div id="activity-container" style="display: none;">
                    <pre id="activity-data">No activity data available</pre>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2">
            <button id="refresh-button" class="btn btn-primary">Refresh Data</button>
            <a href="/" class="btn btn-outline-secondary">Go to Home Page</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userInfoElement = document.getElementById('user-info');
            const statsLoadingElement = document.getElementById('stats-loading');
            const statsErrorElement = document.getElementById('stats-error');
            const statsContainerElement = document.getElementById('stats-container');
            const activityLoadingElement = document.getElementById('activity-loading');
            const activityErrorElement = document.getElementById('activity-error');
            const activityContainerElement = document.getElementById('activity-container');
            const activityDataElement = document.getElementById('activity-data');
            const refreshButton = document.getElementById('refresh-button');
            
            // Function to display user information
            function displayUserInfo() {
                const token = localStorage.getItem('token');
                const userDataString = localStorage.getItem('user');
                
                if (token && userDataString) {
                    try {
                        const userData = JSON.parse(userDataString);
                        userInfoElement.innerHTML = `
                            <div class="alert alert-success">
                                <h4>Welcome, ${userData.name || 'User'}!</h4>
                                <p>Email: ${userData.email}</p>
                                <p>User ID: ${userData.id}</p>
                                ${userData.isAdmin ? '<p><strong>Admin User</strong></p>' : ''}
                            </div>
                        `;
                    } catch (error) {
                        userInfoElement.innerHTML = `
                            <div class="alert alert-warning">
                                Error parsing user data: ${error.message}
                            </div>
                        `;
                    }
                } else {
                    userInfoElement.innerHTML = `
                        <div class="alert alert-warning">
                            Not logged in. Please <a href="/public/simple-login.html">login</a> first.
                        </div>
                    `;
                }
            }
            
            // Function to fetch user stats
            async function fetchUserStats() {
                const token = localStorage.getItem('token');
                
                if (!token) {
                    statsLoadingElement.style.display = 'none';
                    statsErrorElement.style.display = 'block';
                    statsErrorElement.textContent = 'Authentication token not found. Please login first.';
                    return;
                }
                
                try {
                    statsLoadingElement.style.display = 'block';
                    statsErrorElement.style.display = 'none';
                    statsContainerElement.style.display = 'none';
                    
                    const response = await fetch('/api/user/stats', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    
                    if (!response.ok) {
                        throw new Error(`Failed to fetch user stats: ${response.status}`);
                    }
                    
                    const statsData = await response.json();
                    console.log('Received stats data:', statsData);
                    
                    // Display stats
                    displayUserStats(statsData);
                    
                    statsLoadingElement.style.display = 'none';
                    statsContainerElement.style.display = 'block';
                } catch (error) {
                    console.error('Error fetching user stats:', error);
                    statsLoadingElement.style.display = 'none';
                    statsErrorElement.style.display = 'block';
                    statsErrorElement.textContent = `Failed to load user stats: ${error.message}`;
                }
            }
            
            // Function to display user stats
            function displayUserStats(stats) {
                const statItems = [
                    { name: 'Issues', value: stats.issuesReported, icon: '🚧', color: '#dc3545' },
                    { name: 'Transport', value: stats.transportFeedbacks, icon: '🚌', color: '#0d6efd' },
                    { name: 'Challenges', value: stats.challengesCompleted, icon: '🏆', color: '#198754' },
                    { name: 'Reviews', value: stats.reviewsSubmitted, icon: '⭐', color: '#ffc107' },
                    { name: 'Resources', value: stats.resourcesShared, icon: '🔄', color: '#6c757d' },
                    { name: 'Events', value: stats.eventsAttended, icon: '📅', color: '#6610f2' },
                    { name: 'Courses', value: stats.coursesCompleted, icon: '🎓', color: '#20c997' }
                ];
                
                statsContainerElement.innerHTML = '';
                
                statItems.forEach(item => {
                    const colElement = document.createElement('div');
                    colElement.className = 'col-md-3 col-sm-6';
                    
                    colElement.innerHTML = `
                        <div class="stat-card" style="border-top: 4px solid ${item.color}">
                            <div class="stat-icon">${item.icon}</div>
                            <div class="stat-value">${item.value}</div>
                            <div class="stat-label">${item.name}</div>
                        </div>
                    `;
                    
                    statsContainerElement.appendChild(colElement);
                });
                
                // Add total stat
                const totalValue = statItems.reduce((sum, item) => sum + item.value, 0);
                const colElement = document.createElement('div');
                colElement.className = 'col-md-3 col-sm-6';
                
                colElement.innerHTML = `
                    <div class="stat-card" style="border-top: 4px solid #000">
                        <div class="stat-icon">📊</div>
                        <div class="stat-value">${totalValue}</div>
                        <div class="stat-label">Total</div>
                    </div>
                `;
                
                statsContainerElement.appendChild(colElement);
            }
            
            // Function to fetch activity details
            async function fetchActivityDetails() {
                const token = localStorage.getItem('token');
                
                if (!token) {
                    activityLoadingElement.style.display = 'none';
                    activityErrorElement.style.display = 'block';
                    activityErrorElement.textContent = 'Authentication token not found. Please login first.';
                    return;
                }
                
                try {
                    activityLoadingElement.style.display = 'block';
                    activityErrorElement.style.display = 'none';
                    activityContainerElement.style.display = 'none';
                    
                    const response = await fetch('/api/user/activity/all', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    
                    if (!response.ok) {
                        throw new Error(`Failed to fetch activity details: ${response.status}`);
                    }
                    
                    const activityData = await response.json();
                    console.log('Received activity data:', activityData);
                    
                    // Display activity data
                    activityDataElement.textContent = JSON.stringify(activityData, null, 2);
                    
                    activityLoadingElement.style.display = 'none';
                    activityContainerElement.style.display = 'block';
                } catch (error) {
                    console.error('Error fetching activity details:', error);
                    activityLoadingElement.style.display = 'none';
                    activityErrorElement.style.display = 'block';
                    activityErrorElement.textContent = `Failed to load activity details: ${error.message}`;
                }
            }
            
            // Function to refresh all data
            function refreshData() {
                displayUserInfo();
                fetchUserStats();
                fetchActivityDetails();
            }
            
            // Add event listener to refresh button
            refreshButton.addEventListener('click', refreshData);
            
            // Initial data load
            refreshData();
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
