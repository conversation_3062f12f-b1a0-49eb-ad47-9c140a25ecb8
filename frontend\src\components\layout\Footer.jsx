import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-dark text-light py-4 mt-5">
      <Container>
        <Row className="mb-4">
          <Col md={4} className="mb-4 mb-md-0">
            <h5 className="mb-3">UrbanPulse</h5>
            <p className="text-muted">
              Empowering citizens to create smarter, more sustainable urban communities through collaboration and innovation.
            </p>
          </Col>
          <Col md={2} className="mb-4 mb-md-0">
            <h6 className="mb-3">Features</h6>
            <ul className="list-unstyled">
              <li className="mb-2"><Link to="/issue-reporting" className="text-decoration-none text-muted">Issue Reporting</Link></li>
              <li className="mb-2"><Link to="/transport-feedback" className="text-decoration-none text-muted">Transport Feedback</Link></li>
              <li className="mb-2"><Link to="/sustainable-living" className="text-decoration-none text-muted">Sustainable Living</Link></li>
              <li className="mb-2"><Link to="/public-services" className="text-decoration-none text-muted">Public Services</Link></li>
            </ul>
          </Col>
          <Col md={2} className="mb-4 mb-md-0">
            <h6 className="mb-3">More</h6>
            <ul className="list-unstyled">
              <li className="mb-2"><Link to="/resource-sharing" className="text-decoration-none text-muted">Resource Sharing</Link></li>
              <li className="mb-2"><Link to="/green-spaces" className="text-decoration-none text-muted">Green Spaces</Link></li>
              <li className="mb-2"><Link to="/learning-hub" className="text-decoration-none text-muted">Learning Hub</Link></li>
            </ul>
          </Col>
          <Col md={4}>
            <h6 className="mb-3">Connect With Us</h6>
            <p className="text-muted mb-2">
              Join our community and help make our city better.
            </p>
            <div className="d-flex gap-3 fs-5">
              <a href="#" className="text-muted"><i className="bi bi-facebook"></i></a>
              <a href="#" className="text-muted"><i className="bi bi-twitter"></i></a>
              <a href="#" className="text-muted"><i className="bi bi-instagram"></i></a>
              <a href="#" className="text-muted"><i className="bi bi-linkedin"></i></a>
            </div>
          </Col>
        </Row>
        <hr className="my-4 bg-secondary" />
        <Row>
          <Col className="text-center text-muted">
            <small>&copy; {currentYear} UrbanPulse. All rights reserved.</small>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
