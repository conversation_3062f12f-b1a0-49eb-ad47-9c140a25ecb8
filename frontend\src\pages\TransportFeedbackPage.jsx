import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Table, Badge, Modal, ProgressBar } from 'react-bootstrap';
import { FaBus, FaThumbsUp, FaThumbsDown, FaComments, FaVoteYea } from 'react-icons/fa';

const TransportFeedbackPage = () => {
  const [feedbackTitle, setFeedbackTitle] = useState('');
  const [feedbackCategory, setFeedbackCategory] = useState('');
  const [feedbackDescription, setFeedbackDescription] = useState('');
  const [routeNumber, setRouteNumber] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState(null);
  const [activeTab, setActiveTab] = useState('suggest');

  // Mock data for suggestions
  const suggestions = [
    { 
      id: 1, 
      title: 'Add Bus Stop at Central Park', 
      category: 'Routes', 
      votes: 45, 
      status: 'Under Review', 
      date: '2023-04-10',
      description: 'There should be a bus stop at the north entrance of Central Park to improve accessibility.',
      comments: 12
    },
    { 
      id: 2, 
      title: 'Extend Weekend Service Hours', 
      category: 'Schedule', 
      votes: 67, 
      status: 'Approved', 
      date: '2023-04-05',
      description: 'Weekend service should be extended until midnight to accommodate evening activities.',
      comments: 23
    },
    { 
      id: 3, 
      title: 'Improve Bus Cleanliness', 
      category: 'Maintenance', 
      votes: 32, 
      status: 'Under Review', 
      date: '2023-04-02',
      description: 'Buses on Route 7 need more frequent cleaning throughout the day.',
      comments: 8
    }
  ];

  // Mock data for polls
  const polls = [
    {
      id: 1,
      question: 'Which route needs more frequent service?',
      options: [
        { id: 1, text: 'Route 10 (Downtown - Airport)', votes: 156 },
        { id: 2, text: 'Route 15 (University - Shopping Center)', votes: 243 },
        { id: 3, text: 'Route 22 (Residential Area - Business District)', votes: 187 },
        { id: 4, text: 'Route 35 (Suburbs - Downtown)', votes: 92 }
      ],
      totalVotes: 678,
      endDate: '2023-05-15'
    },
    {
      id: 2,
      question: 'What improvement would you prioritize for public transport?',
      options: [
        { id: 1, text: 'More frequent service', votes: 312 },
        { id: 2, text: 'Extended hours', votes: 178 },
        { id: 3, text: 'Better bus shelters', votes: 143 },
        { id: 4, text: 'Real-time tracking app', votes: 267 }
      ],
      totalVotes: 900,
      endDate: '2023-05-10'
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!feedbackTitle || !feedbackCategory || !feedbackDescription) {
      return setError('Please fill in all required fields');
    }
    
    setError('');
    setSuccess('');
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setSuccess('Your suggestion has been submitted successfully! Others can now vote on it.');
      setLoading(false);
      setFeedbackTitle('');
      setFeedbackCategory('');
      setFeedbackDescription('');
      setRouteNumber('');
    }, 1500);
  };

  const handleViewFeedback = (feedback) => {
    setSelectedFeedback(feedback);
    setShowModal(true);
  };

  const handleVote = (pollId, optionId) => {
    // In a real app, this would make an API call to record the vote
    console.log(`Voted for option ${optionId} in poll ${pollId}`);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Under Review':
        return <Badge bg="info">Under Review</Badge>;
      case 'Approved':
        return <Badge bg="primary">Approved</Badge>;
      case 'Implemented':
        return <Badge bg="success">Implemented</Badge>;
      case 'Declined':
        return <Badge bg="danger">Declined</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Public Transport Feedback</h1>
          <p className="lead">
            Help improve our city's public transportation by suggesting improvements and participating in polls.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-primary bg-opacity-10 p-3 rounded">
            <FaBus className="text-primary fs-1" />
          </div>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col>
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white border-0 pt-4 pb-0 px-4">
              <ul className="nav nav-tabs">
                <li className="nav-item">
                  <button 
                    className={`nav-link ${activeTab === 'suggest' ? 'active' : ''}`}
                    onClick={() => setActiveTab('suggest')}
                  >
                    Suggest Improvements
                  </button>
                </li>
                <li className="nav-item">
                  <button 
                    className={`nav-link ${activeTab === 'vote' ? 'active' : ''}`}
                    onClick={() => setActiveTab('vote')}
                  >
                    Vote on Suggestions
                  </button>
                </li>
                <li className="nav-item">
                  <button 
                    className={`nav-link ${activeTab === 'polls' ? 'active' : ''}`}
                    onClick={() => setActiveTab('polls')}
                  >
                    Participate in Polls
                  </button>
                </li>
              </ul>
            </Card.Header>
            <Card.Body className="p-4">
              {activeTab === 'suggest' && (
                <>
                  <h4 className="mb-4">Suggest an Improvement</h4>
                  
                  {error && <Alert variant="danger">{error}</Alert>}
                  {success && <Alert variant="success">{success}</Alert>}
                  
                  <Form onSubmit={handleSubmit}>
                    <Row>
                      <Col md={8}>
                        <Form.Group className="mb-3" controlId="feedbackTitle">
                          <Form.Label>Suggestion Title*</Form.Label>
                          <Form.Control
                            type="text"
                            placeholder="E.g., Add bus stop at Central Park"
                            value={feedbackTitle}
                            onChange={(e) => setFeedbackTitle(e.target.value)}
                            required
                          />
                        </Form.Group>
                      </Col>
                      <Col md={4}>
                        <Form.Group className="mb-3" controlId="feedbackCategory">
                          <Form.Label>Category*</Form.Label>
                          <Form.Select
                            value={feedbackCategory}
                            onChange={(e) => setFeedbackCategory(e.target.value)}
                            required
                          >
                            <option value="">Select a category</option>
                            <option value="Routes">Routes</option>
                            <option value="Schedule">Schedule</option>
                            <option value="Frequency">Frequency</option>
                            <option value="Accessibility">Accessibility</option>
                            <option value="Maintenance">Maintenance</option>
                            <option value="Other">Other</option>
                          </Form.Select>
                        </Form.Group>
                      </Col>
                    </Row>
                    
                    <Row>
                      <Col md={4}>
                        <Form.Group className="mb-3" controlId="routeNumber">
                          <Form.Label>Route Number (if applicable)</Form.Label>
                          <Form.Control
                            type="text"
                            placeholder="E.g., Route 42"
                            value={routeNumber}
                            onChange={(e) => setRouteNumber(e.target.value)}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={8}>
                        <Form.Group className="mb-3" controlId="feedbackDescription">
                          <Form.Label>Description*</Form.Label>
                          <Form.Control
                            as="textarea"
                            rows={4}
                            placeholder="Please provide details about your suggestion..."
                            value={feedbackDescription}
                            onChange={(e) => setFeedbackDescription(e.target.value)}
                            required
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                    
                    <div className="d-flex justify-content-end">
                      <Button 
                        variant="primary" 
                        type="submit" 
                        disabled={loading}
                      >
                        {loading ? 'Submitting...' : 'Submit Suggestion'}
                      </Button>
                    </div>
                  </Form>
                </>
              )}
              
              {activeTab === 'vote' && (
                <>
                  <h4 className="mb-4">Vote on Suggestions</h4>
                  
                  <div className="table-responsive">
                    <Table hover>
                      <thead>
                        <tr>
                          <th>Suggestion</th>
                          <th>Category</th>
                          <th>Votes</th>
                          <th>Status</th>
                          <th>Date</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {suggestions.map((suggestion) => (
                          <tr key={suggestion.id}>
                            <td>{suggestion.title}</td>
                            <td>{suggestion.category}</td>
                            <td>
                              <div className="d-flex align-items-center">
                                <span className="me-2">{suggestion.votes}</span>
                                <Button variant="outline-success" size="sm" className="me-1 p-1">
                                  <FaThumbsUp size={14} />
                                </Button>
                                <Button variant="outline-danger" size="sm" className="p-1">
                                  <FaThumbsDown size={14} />
                                </Button>
                              </div>
                            </td>
                            <td>{getStatusBadge(suggestion.status)}</td>
                            <td>{suggestion.date}</td>
                            <td>
                              <Button 
                                variant="link" 
                                className="p-0 text-decoration-none"
                                onClick={() => handleViewFeedback(suggestion)}
                              >
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </div>
                </>
              )}
              
              {activeTab === 'polls' && (
                <>
                  <h4 className="mb-4">Participate in Polls</h4>
                  
                  {polls.map((poll) => (
                    <Card key={poll.id} className="mb-4 border-0 shadow-sm">
                      <Card.Body className="p-4">
                        <h5 className="mb-3">{poll.question}</h5>
                        <p className="text-muted mb-4">
                          <small>Total votes: {poll.totalVotes} • Poll ends: {poll.endDate}</small>
                        </p>
                        
                        {poll.options.map((option) => {
                          const percentage = Math.round((option.votes / poll.totalVotes) * 100);
                          
                          return (
                            <div key={option.id} className="mb-3">
                              <div className="d-flex justify-content-between align-items-center mb-1">
                                <span>{option.text}</span>
                                <span>{percentage}% ({option.votes} votes)</span>
                              </div>
                              <div className="d-flex align-items-center">
                                <ProgressBar 
                                  now={percentage} 
                                  className="flex-grow-1 me-2" 
                                  style={{ height: '10px' }}
                                />
                                <Button 
                                  variant="outline-primary" 
                                  size="sm"
                                  onClick={() => handleVote(poll.id, option.id)}
                                >
                                  <FaVoteYea className="me-1" />
                                  Vote
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </Card.Body>
                    </Card>
                  ))}
                </>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Feedback Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Suggestion Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedFeedback && (
            <>
              <h5>{selectedFeedback.title}</h5>
              <p className="mb-2">
                <strong>Category:</strong> {selectedFeedback.category}
              </p>
              <p className="mb-2">
                <strong>Status:</strong> {getStatusBadge(selectedFeedback.status)}
              </p>
              <p className="mb-2">
                <strong>Date Submitted:</strong> {selectedFeedback.date}
              </p>
              <p className="mb-2">
                <strong>Votes:</strong> {selectedFeedback.votes}
              </p>
              <p className="mb-4">
                <strong>Description:</strong> {selectedFeedback.description}
              </p>
              
              <div className="d-flex justify-content-between align-items-center mb-3">
                <h6 className="mb-0">Comments ({selectedFeedback.comments})</h6>
                <Button variant="outline-primary" size="sm">
                  <FaComments className="me-1" />
                  Add Comment
                </Button>
              </div>
              
              <div className="border-top pt-3">
                <div className="mb-3">
                  <div className="d-flex justify-content-between">
                    <strong>Jane Smith</strong>
                    <small className="text-muted">2023-04-12</small>
                  </div>
                  <p className="mb-0">This is a great idea! I've been wanting this for a long time.</p>
                </div>
                
                <div className="mb-3">
                  <div className="d-flex justify-content-between">
                    <strong>John Doe</strong>
                    <small className="text-muted">2023-04-11</small>
                  </div>
                  <p className="mb-0">I agree, this would make my commute much easier.</p>
                </div>
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          <Button variant="primary">
            <FaThumbsUp className="me-1" />
            Vote Up
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default TransportFeedbackPage;
