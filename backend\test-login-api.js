const axios = require('axios');

// Test user credentials
const testUsers = [
  { email: '<EMAIL>', password: 'password123', description: 'Test User' },
  { email: '<EMAIL>', password: 'password123', description: 'Demo User' },
  { email: '<EMAIL>', password: 'password123', description: 'Admin User' }
];

// Function to test login
async function testLogin(user) {
  console.log(`Testing login for ${user.description} (${user.email}):`);
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: user.email,
      password: user.password
    });
    
    console.log('✅ Login successful!');
    console.log('Status:', response.status);
    console.log('Token:', response.data.token ? `${response.data.token.substring(0, 20)}...` : 'None');
    console.log('User:', response.data.user ? JSON.stringify(response.data.user) : 'None');
    return true;
  } catch (error) {
    console.log('❌ Login failed!');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else if (error.request) {
      console.log('No response received. Is the server running?');
    } else {
      console.log('Error:', error.message);
    }
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('=== TESTING LOGIN API ===\n');
  
  let successCount = 0;
  
  for (const user of testUsers) {
    const success = await testLogin(user);
    if (success) successCount++;
    console.log(); // Add a blank line between tests
  }
  
  console.log(`=== TEST RESULTS: ${successCount}/${testUsers.length} successful logins ===`);
}

// Run the tests
runTests();
