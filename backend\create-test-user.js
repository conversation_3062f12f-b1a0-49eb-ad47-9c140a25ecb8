const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');

  // Create a new user directly
  const db = mongoose.connection.db;
  const usersCollection = db.collection('users');

  // Hash the password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash('testpass123', salt);

  // Create a new user with a unique email
  const timestamp = Date.now();
  const email = `newtest${timestamp}@example.com`;

  const newUser = {
    name: 'New Test User',
    email: email,
    password: hashedPassword,
    isAdmin: false,
    createdAt: new Date()
  };

  // Insert the user
  const result = await usersCollection.insertOne(newUser);
  console.log(`User created with ID: ${result.insertedId}`);

  // Verify the user
  const user = await usersCollection.findOne({ email: email });
  console.log('User found:', user ? 'Yes' : 'No');

  if (user) {
    // Test password
    const isMatch = await bcrypt.compare('testpass123', user.password);
    console.log('Password match:', isMatch ? 'Yes' : 'No');
  }

  // Close the connection
  await mongoose.connection.close();
  console.log('Connection closed');
  process.exit(0);
})
.catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
