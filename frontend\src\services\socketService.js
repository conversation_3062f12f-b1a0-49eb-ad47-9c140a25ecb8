// Socket service implementation
// This service handles real-time communication using socket.io

import { io } from 'socket.io-client';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = {};
    console.log('Socket service initialized');
  }

  connect() {
    if (this.socket) {
      return;
    }

    console.log('Connecting to socket server...');

    // In a real implementation, this would connect to the actual server
    // For now, we'll use a simulated connection
    try {
      // Simulate socket connection
      setTimeout(() => {
        this.isConnected = true;
        console.log('Socket connected (simulated)');

        // Trigger a connection event
        if (this.listeners['connect']) {
          this.listeners['connect'].forEach(callback => callback());
        }

        // Set up periodic interest notifications for demo purposes
        this.setupDemoInterestNotifications();
      }, 500);
    } catch (error) {
      console.error('Socket connection error:', error);
    }
  }

  // Set up demo interest notifications for testing
  setupDemoInterestNotifications() {
    // Only in development mode and if we have a current user
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
    if (!currentUser || !currentUser.id) return;

    // Check if the user has any resources
    const mockItems = JSON.parse(localStorage.getItem('mockItems') || '[]');
    const userItems = mockItems.filter(item => item.ownerId === currentUser.id);

    if (userItems.length > 0) {
      console.log('Setting up demo interest notifications for user resources');

      // Clear any existing interval
      if (this.demoInterestInterval) {
        clearInterval(this.demoInterestInterval);
      }

      // Set up a timer to occasionally send interest notifications
      this.demoInterestInterval = setInterval(() => {
        // 10% chance of triggering a notification
        if (Math.random() < 0.1) {
          const randomItem = userItems[Math.floor(Math.random() * userItems.length)];
          const demoUsers = [
            { id: 'demo1', name: 'Alex Johnson' },
            { id: 'demo2', name: 'Sam Taylor' },
            { id: 'demo3', name: 'Jordan Smith' }
          ];
          const randomUser = demoUsers[Math.floor(Math.random() * demoUsers.length)];

          console.log(`Generating demo interest notification for ${randomItem.title}`);

          // Emit interest notification
          this.emitResourceInterest(
            randomItem.id,
            randomUser.name,
            {
              userId: randomUser.id,
              userName: randomUser.name,
              resourceId: randomItem.id,
              resourceTitle: randomItem.title,
              ownerId: currentUser.id,
              timestamp: new Date(),
              message: `I'm interested in your ${randomItem.title}. Is it still available?`
            }
          );
        }
      }, 60000); // Check every minute
    }
  }

  // Join a neighborhood room
  joinNeighborhood(neighborhood) {
    console.log(`Join neighborhood: ${neighborhood} (placeholder)`);
  }

  // Join a green space room
  joinGreenSpace(spaceId) {
    console.log(`Join green space: ${spaceId} (placeholder)`);
  }

  // Join an issue room to receive updates about a specific issue
  joinIssueRoom(issueId) {
    console.log(`Join issue room: ${issueId} (placeholder)`);

    // In a real implementation, this would emit a 'join-issue' event to the server
    // this.socket.emit('join-issue', issueId);

    // For demo purposes, simulate receiving an issue update after a delay
    setTimeout(() => {
      if (this.listeners['issue-update']) {
        this.listeners['issue-update'].forEach(callback => callback({
          issueId,
          status: 'In Progress',
          comment: 'We have assigned a team to investigate this issue.',
          updatedBy: {
            id: 'admin-1',
            name: 'Admin User'
          },
          timestamp: new Date()
        }));
      }
    }, 10000); // Simulate an update after 10 seconds
  }

  // Join the user's personal room to receive updates about their issues
  joinUserRoom(userId) {
    console.log(`Join user room: ${userId} (placeholder)`);

    // In a real implementation, this would emit a 'join-user' event to the server
    // this.socket.emit('join-user', userId);
  }

  // Join a resource room to receive updates about a specific resource
  joinResourceRoom(resourceId) {
    console.log(`Join resource room: ${resourceId} (placeholder)`);

    // In a real implementation, this would emit a 'join-resource' event to the server
    // this.socket.emit('join-resource', resourceId);
  }

  // Send a chat message
  sendMessage(room, message, user) {
    console.log(`Send message to ${room}: ${message} (placeholder)`);

    // Simulate receiving the message back
    setTimeout(() => {
      if (this.listeners['new-message']) {
        this.listeners['new-message'].forEach(callback => callback({
          message,
          user,
          timestamp: new Date()
        }));
      }

      // Simulate a response from another user after a delay
      if (room.includes('green-space')) {
        const spaceId = room.split('-')[2];

        // Only respond to certain messages
        if (message.toLowerCase().includes('hello') ||
            message.toLowerCase().includes('hi') ||
            message.toLowerCase().includes('hey')) {
          setTimeout(() => {
            if (this.listeners['new-message']) {
              this.listeners['new-message'].forEach(callback => callback({
                message: `Hi there! Welcome to our green space community chat. How can I help you today?`,
                user: {
                  id: 'park-ranger',
                  name: 'Park Ranger',
                  isAdmin: true
                },
                timestamp: new Date()
              }));
            }
          }, 1500);
        } else if (message.toLowerCase().includes('event') ||
                  message.toLowerCase().includes('activity') ||
                  message.toLowerCase().includes('happening')) {
          setTimeout(() => {
            if (this.listeners['new-message']) {
              this.listeners['new-message'].forEach(callback => callback({
                message: `We have several events planned this month! Check the events tab for more details.`,
                user: {
                  id: 'community-manager',
                  name: 'Community Manager',
                  isAdmin: false
                },
                timestamp: new Date()
              }));
            }
          }, 2000);
        } else if (message.toLowerCase().includes('volunteer') ||
                  message.toLowerCase().includes('help') ||
                  message.toLowerCase().includes('join')) {
          setTimeout(() => {
            if (this.listeners['new-message']) {
              this.listeners['new-message'].forEach(callback => callback({
                message: `We're always looking for volunteers! Please sign up for our next community event.`,
                user: {
                  id: 'volunteer-coordinator',
                  name: 'Volunteer Coordinator',
                  isAdmin: false
                },
                timestamp: new Date()
              }));
            }
          }, 1800);
        }
      }
    }, 300);
  }

  // Emit an issue status update
  emitIssueStatusUpdate(issueId, status, comment) {
    console.log(`Emit issue status update for ${issueId}: ${status} (placeholder)`);

    // In a real implementation, this would emit an 'issue-status-update' event to the server
    // this.socket.emit('issue-status-update', { issueId, status, comment });

    // For demo purposes, simulate receiving the update back
    setTimeout(() => {
      if (this.listeners['issue-update']) {
        this.listeners['issue-update'].forEach(callback => callback({
          issueId,
          status,
          comment,
          updatedBy: {
            id: 'admin-1',
            name: 'Admin User'
          },
          timestamp: new Date()
        }));
      }
    }, 300);

    return true;
  }

  // Emit a resource status update
  emitResourceStatusUpdate(resourceId, status) {
    console.log(`Emit resource status update for ${resourceId}: ${status} (placeholder)`);

    // In a real implementation, this would emit a 'resource-status-update' event to the server
    // this.socket.emit('resource-status-update', { resourceId, status });

    // For demo purposes, simulate receiving the update back
    setTimeout(() => {
      if (this.listeners['resource-status-update']) {
        this.listeners['resource-status-update'].forEach(callback => callback({
          resourceId,
          status,
          updatedBy: {
            id: 'user-1',
            name: 'Current User'
          },
          timestamp: new Date()
        }));
      }
    }, 300);

    return true;
  }

  // Emit a resource message
  emitResourceMessage(resourceId, content, isOwner) {
    console.log(`Emit resource message for ${resourceId}: ${content} (placeholder)`);

    // In a real implementation, this would emit a 'resource-message' event to the server
    // this.socket.emit('resource-message', { resourceId, content });

    // For demo purposes, simulate receiving the message back
    setTimeout(() => {
      if (this.listeners['resource-message']) {
        this.listeners['resource-message'].forEach(callback => callback({
          resourceId,
          message: {
            id: Date.now().toString(),
            content,
            userName: 'Current User',
            createdAt: new Date(),
            isOwner: isOwner || false
          }
        }));
      }
    }, 300);

    return true;
  }

  // Emit a resource interest notification
  emitResourceInterest(resourceId, userName, interestData = null) {
    console.log(`Emit resource interest for ${resourceId} from ${userName} (placeholder)`);

    // In a real implementation, this would emit a 'resource-interest' event to the server
    // this.socket.emit('resource-interest', { resourceId, userName, ...interestData });

    // For demo purposes, simulate receiving the interest notification
    setTimeout(() => {
      if (this.listeners['resource-interest']) {
        this.listeners['resource-interest'].forEach(callback => callback({
          resourceId,
          userName,
          timestamp: new Date(),
          ...(interestData || {})
        }));
      }
    }, 300);

    return true;
  }

  // Simulate receiving an issue update from the server
  simulateIssueUpdate(issueId, status, comment) {
    if (this.listeners['issue-update']) {
      this.listeners['issue-update'].forEach(callback => callback({
        issueId,
        status,
        comment,
        updatedBy: {
          id: 'admin-1',
          name: 'Admin User'
        },
        timestamp: new Date()
      }));
    }
  }

  // Add event listener
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    return () => this.off(event, callback); // Return a cleanup function
  }

  // Remove event listener
  off(event, callback) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
  }

  // Disconnect from the Socket.io server
  disconnect() {
    this.isConnected = false;
    console.log('Socket disconnected (placeholder)');

    // Clear any demo intervals
    if (this.demoInterestInterval) {
      clearInterval(this.demoInterestInterval);
      this.demoInterestInterval = null;
    }

    // In a real implementation, this would disconnect the socket
    // if (this.socket) {
    //   this.socket.disconnect();
    //   this.socket = null;
    // }
  }
}

// Create a singleton instance
const socketService = new SocketService();

export default socketService;
