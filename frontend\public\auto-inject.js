// This script automatically injects the force-navbar-update.js script
(function() {
    console.log('Auto-inject script loaded');
    
    // Function to check if we should inject the script
    function shouldInjectScript() {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        const userDataString = localStorage.getItem('user');
        
        if (token && userDataString) {
            try {
                // Parse user data
                JSON.parse(userDataString);
                return true;
            } catch (error) {
                console.error('Error parsing user data:', error);
                return false;
            }
        }
        
        return false;
    }
    
    // Function to inject the script
    function injectScript() {
        // Check if the script is already injected
        if (document.querySelector('script[src="/public/force-navbar-update.js"]')) {
            console.log('Script already injected');
            return;
        }
        
        // Create a script element
        const script = document.createElement('script');
        script.src = '/public/force-navbar-update.js';
        
        // Add it to the document
        document.head.appendChild(script);
        
        console.log('Injected force-navbar-update.js');
    }
    
    // Function to initialize
    function init() {
        if (shouldInjectScript()) {
            console.log('User is logged in, injecting navbar update script');
            injectScript();
        } else {
            console.log('User is not logged in, no need to inject script');
        }
    }
    
    // Wait for the DOM to be fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
