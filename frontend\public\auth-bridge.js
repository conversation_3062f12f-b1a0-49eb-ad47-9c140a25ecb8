// This script acts as a bridge between the static HTML login and the React application
(function() {
    console.log('Auth bridge script loaded');
    
    // Function to create and dispatch a custom storage event
    function notifyAuth<PERSON>hange(key, newValue) {
        // Create a custom event that mimics a storage event
        const event = new CustomEvent('auth-change', {
            detail: {
                key: key,
                newValue: newValue
            }
        });
        
        // Dispatch the event
        window.dispatchEvent(event);
        console.log('Auth bridge: Dispatched auth-change event for', key);
    }
    
    // Listen for the userLoggedIn event from the login page
    window.addEventListener('userLoggedIn', function() {
        console.log('Auth bridge: Detected userLoggedIn event');
        
        // Get the current user data
        const userDataString = localStorage.getItem('user');
        if (userDataString) {
            try {
                const userData = JSON.parse(userDataString);
                console.log('Auth bridge: User logged in as', userData.name || 'User');
                
                // Notify about the auth change
                notifyAuthChange('user', userDataString);
            } catch (error) {
                console.error('Auth bridge: Error parsing user data', error);
            }
        }
    });
    
    // Function to check URL for auth_update parameter
    function checkUrlForAuthUpdate() {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('auth_update')) {
            console.log('Auth bridge: Detected auth_update parameter in URL');
            
            // Get the current user data
            const userDataString = localStorage.getItem('user');
            if (userDataString) {
                try {
                    const userData = JSON.parse(userDataString);
                    console.log('Auth bridge: User is logged in as', userData.name || 'User');
                    
                    // Notify about the auth change
                    notifyAuthChange('user', userDataString);
                } catch (error) {
                    console.error('Auth bridge: Error parsing user data', error);
                }
            }
            
            // Remove the parameter from URL to avoid repeated checks
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    }
    
    // Check URL when the script loads
    checkUrlForAuthUpdate();
    
    // Also check when the page loads
    window.addEventListener('load', checkUrlForAuthUpdate);
})();
