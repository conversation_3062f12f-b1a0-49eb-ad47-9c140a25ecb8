# UrbanPulse Frontend

This is the frontend application for the UrbanPulse smart urban living platform. It's built with <PERSON>act, Vite, and Bootstrap.

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn

### Installation

1. Install dependencies:
```
npm install
```

2. Start the development server:
```
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

## Available Scripts

- `npm run dev`: Start the development server
- `npm run build`: Build the app for production
- `npm run preview`: Preview the production build
- `npm run test`: Run tests
- `npm run test:watch`: Run tests in watch mode

## Project Structure

- `public/`: Static files
- `src/`: Source code
  - `assets/`: Images, styles, etc.
  - `components/`: Reusable components
  - `contexts/`: React contexts
  - `pages/`: Page components
  - `utils/`: Utility functions
  - `test/`: Test setup and utilities

## Features

The frontend implements all seven components of the UrbanPulse platform:

1. Urban Issue Reporting
2. Public Transport Feedback
3. Sustainable Living Challenges
4. Public Service Review System
5. Resource Sharing Platform
6. Green Space Mapping
7. Urban Learning Hub

## Dependencies

- React
- React Router
- React Bootstrap
- Axios
- Chart.js
- Leaflet
- React Icons
- Formik
- Yup

## Testing

Tests are written using Vitest and React Testing Library. Run tests with:

```
npm run test
```
