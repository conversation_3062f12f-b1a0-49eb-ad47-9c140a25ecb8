import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Al<PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { FaExclamationTriangle, FaBus, FaLeaf, FaStar, FaExchangeAlt, FaTree, FaGraduationCap, FaRecycle, FaCalendarAlt, FaUser, FaChartLine, FaBell } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import GeminiChatWidget from '../components/ai/GeminiChatWidget';
import ActivityDetails from '../components/dashboard/ActivityDetails';
import ActivityReport from '../components/dashboard/ActivityReport';

const UserDashboardPage = () => {
  const { currentUser } = useAuth();
  const [localUser, setLocalUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Check localStorage for user data
  useEffect(() => {
    try {
      const userString = localStorage.getItem('user');
      if (userString) {
        const userData = JSON.parse(userString);
        setLocalUser(userData);
      }
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }, []);

  // Use either context user or localStorage user
  const user = currentUser || localUser;
  const [userStats, setUserStats] = useState({
    issuesReported: 0,
    transportFeedbacks: 0,
    challengesCompleted: 0,
    reviewsSubmitted: 0,
    resourcesShared: 0,
    eventsAttended: 0,
    coursesCompleted: 0
  });
  const [notifications, setNotifications] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [upcomingEvents, setUpcomingEvents] = useState([]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);

        // Get the authentication token
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('Authentication token not found');
        }

        console.log('Fetching user stats with token:', token);

        // Fetch user stats from the backend
        const statsResponse = await fetch('/api/user/stats', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Stats response status:', statsResponse.status);

        if (!statsResponse.ok) {
          const errorText = await statsResponse.text();
          console.error('Stats response error:', errorText);
          throw new Error(`Failed to fetch user stats: ${statsResponse.status} ${errorText}`);
        }

        const statsData = await statsResponse.json();
        console.log('Received stats data:', statsData);

        // Set user stats with real data or default to zeros if not available
        setUserStats({
          issuesReported: statsData.issuesReported || 0,
          transportFeedbacks: statsData.transportFeedbacks || 0,
          challengesCompleted: statsData.challengesCompleted || 0,
          reviewsSubmitted: statsData.reviewsSubmitted || 0,
          resourcesShared: statsData.resourcesShared || 0,
          eventsAttended: statsData.eventsAttended || 0,
          coursesCompleted: statsData.coursesCompleted || 0
        });

        // Fetch notifications
        try {
          console.log('Fetching notifications');
          const notificationsResponse = await fetch('/api/user/notifications', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          console.log('Notifications response status:', notificationsResponse.status);

          if (notificationsResponse.ok) {
            const notificationsData = await notificationsResponse.json();
            console.log('Received notifications data:', notificationsData);
            setNotifications(notificationsData);
          } else {
            console.error('Failed to fetch notifications:', notificationsResponse.status);
          }
        } catch (error) {
          console.error('Error fetching notifications:', error);
        }

        // Fetch recommendations
        try {
          console.log('Fetching recommendations');
          const recommendationsResponse = await fetch('/api/user/recommendations', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          console.log('Recommendations response status:', recommendationsResponse.status);

          if (recommendationsResponse.ok) {
            const recommendationsData = await recommendationsResponse.json();
            console.log('Received recommendations data:', recommendationsData);
            setRecommendations(recommendationsData);
          } else {
            console.error('Failed to fetch recommendations:', recommendationsResponse.status);
          }
        } catch (error) {
          console.error('Error fetching recommendations:', error);
        }

        // Fetch upcoming events
        try {
          console.log('Fetching upcoming events');
          const eventsResponse = await fetch('/api/events/upcoming', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          console.log('Events response status:', eventsResponse.status);

          if (eventsResponse.ok) {
            const eventsData = await eventsResponse.json();
            console.log('Received events data:', eventsData);
            setUpcomingEvents(eventsData);
          } else {
            console.error('Failed to fetch upcoming events:', eventsResponse.status);
          }
        } catch (error) {
          console.error('Error fetching upcoming events:', error);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError('Failed to load your dashboard data. Please try again later.');
        setLoading(false);

        // Fallback to empty data if API calls fail
        setUserStats({
          issuesReported: 0,
          transportFeedbacks: 0,
          challengesCompleted: 0,
          reviewsSubmitted: 0,
          resourcesShared: 0,
          eventsAttended: 0,
          coursesCompleted: 0
        });
        setNotifications([]);
        setRecommendations([]);
        setUpcomingEvents([]);
      }
    };

    // Only fetch data if we have a user
    if (user) {
      fetchUserData();
    } else {
      setLoading(false);
    }
  }, [user]);

  // Calculate user level based on activity
  const calculateUserLevel = () => {
    const totalActivities = Object.values(userStats).reduce((sum, value) => sum + value, 0);

    if (totalActivities >= 30) return { level: 'Gold', color: '#FFD700' };
    if (totalActivities >= 15) return { level: 'Silver', color: '#C0C0C0' };
    return { level: 'Bronze', color: '#CD7F32' };
  };

  const userLevel = calculateUserLevel();

  // Features available in the app
  const features = [
    {
      id: 1,
      title: 'Urban Issue Reporting',
      description: 'Report potholes, streetlights, trash issues and more with simple forms and photos.',
      icon: <FaExclamationTriangle className="feature-icon" />,
      link: '/issue-reporting',
      color: '#e53935'
    },
    {
      id: 2,
      title: 'Public Transport Feedback',
      description: 'Suggest improvements to bus routes, schedules, and services through votes and comments.',
      icon: <FaBus className="feature-icon" />,
      link: '/transport-feedback',
      color: '#1565c0'
    },
    {
      id: 3,
      title: 'Challenge Calendars',
      description: 'Complete weekly sustainability and health challenges to earn badges and track your progress.',
      icon: <FaCalendarAlt className="feature-icon" />,
      link: '/sustainable-living',
      color: '#43a047'
    },
    {
      id: 4,
      title: 'Public Service Reviews',
      description: 'Rate and review public services like garbage collection, water supply, and more.',
      icon: <FaStar className="feature-icon" />,
      link: '/public-services',
      color: '#ffa000'
    }
  ];

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">Loading your personalized dashboard...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4" data-testid="user-dashboard">
      {error && <Alert variant="danger">{error}</Alert>}

      {/* Welcome Section */}
      <Row className="mb-4">
        <Col>
          <Card className="border-0 bg-primary text-white shadow">
            <Card.Body className="p-4">
              <Row className="align-items-center">
                <Col md={8}>
                  <h2 className="mb-1">Welcome back, {user?.name || 'User'}!</h2>
                  <p className="mb-0">Your personalized urban sustainability dashboard</p>
                </Col>
                <Col md={4} className="text-md-end mt-3 mt-md-0">
                  <Badge
                    bg="light"
                    text="dark"
                    className="p-2 fs-6"
                    style={{ border: `2px solid ${userLevel.color}` }}
                  >
                    <FaUser className="me-1" style={{ color: userLevel.color }} />
                    {userLevel.level} Level
                  </Badge>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Stats and Activity Section */}
      <Row className="mb-4">
        <Col lg={8}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body>
              <h4 className="mb-4">Your Activity</h4>
              <Row xs={2} md={4} className="g-3">
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaExclamationTriangle className="text-danger mb-2 fs-3" />
                    <h6>Issues</h6>
                    <h3>{userStats.issuesReported}</h3>
                  </div>
                </Col>
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaBus className="text-primary mb-2 fs-3" />
                    <h6>Feedback</h6>
                    <h3>{userStats.transportFeedbacks}</h3>
                  </div>
                </Col>
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaCalendarAlt className="text-success mb-2 fs-3" />
                    <h6>Challenges</h6>
                    <h3>{userStats.challengesCompleted}</h3>
                  </div>
                </Col>
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaStar className="text-warning mb-2 fs-3" />
                    <h6>Reviews</h6>
                    <h3>{userStats.reviewsSubmitted}</h3>
                  </div>
                </Col>
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaExchangeAlt className="text-secondary mb-2 fs-3" />
                    <h6>Resources</h6>
                    <h3>{userStats.resourcesShared}</h3>
                  </div>
                </Col>
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaTree className="text-success mb-2 fs-3" />
                    <h6>Events</h6>
                    <h3>{userStats.eventsAttended}</h3>
                  </div>
                </Col>
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaGraduationCap className="text-info mb-2 fs-3" />
                    <h6>Courses</h6>
                    <h3>{userStats.coursesCompleted}</h3>
                  </div>
                </Col>
                <Col>
                  <div className="text-center p-3 rounded bg-light">
                    <FaChartLine className="text-primary mb-2 fs-3" />
                    <h6>Impact</h6>
                    <h3>{Object.values(userStats).reduce((a, b) => a + b, 0)}</h3>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={4}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <h4 className="mb-0">Notifications</h4>
                <Badge bg="danger" pill>{notifications.length}</Badge>
              </div>
              {notifications.length > 0 ? (
                <div>
                  {notifications.map(notification => (
                    <div key={notification.id} className="p-3 border-bottom">
                      <div className="d-flex">
                        <div className="me-3">
                          <FaBell className="text-primary fs-5" />
                        </div>
                        <div>
                          <p className="mb-1">{notification.message}</p>
                          <small className="text-muted">{notification.date}</small>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted my-4">No new notifications</p>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recommendations Section */}
      <Row className="mb-4">
        <Col>
          <Card className="border-0 shadow-sm">
            <Card.Body>
              <h4 className="mb-4">Recommended for You</h4>
              <Row xs={1} md={3} className="g-4">
                {recommendations.map(item => (
                  <Col key={item.id}>
                    <Card className="h-100 border-0 bg-light">
                      <Card.Body>
                        <h5>{item.title}</h5>
                        <p className="text-muted">{item.description}</p>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          as={Link}
                          to={item.type === 'challenge' ? '/sustainable-living' :
                              item.type === 'course' ? '/learning-hub' : '/resource-sharing'}
                        >
                          Learn More
                        </Button>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Quick Access Section */}
      <Row className="mb-4">
        <Col>
          <Card className="border-0 shadow-sm">
            <Card.Body>
              <h4 className="mb-4">Quick Access</h4>
              <Row xs={2} md={4} className="g-3">
                {features.map(feature => (
                  <Col key={feature.id}>
                    <Card
                      as={Link}
                      to={feature.link}
                      className="h-100 text-decoration-none border-0 shadow-sm"
                    >
                      <Card.Body className="text-center">
                        <div className="mb-3" style={{ color: feature.color }}>
                          {feature.icon}
                        </div>
                        <h6>{feature.title}</h6>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Activity Details Section */}
      <Row className="mb-4">
        <Col>
          <ActivityDetails userId={user?._id} token={localStorage.getItem('token')} />
        </Col>
      </Row>

      {/* Activity Report Section */}
      <Row className="mb-4">
        <Col>
          <ActivityReport userId={user?._id} token={localStorage.getItem('token')} />
        </Col>
      </Row>

      {/* Upcoming Events Section */}
      <Row className="mb-4">
        <Col md={8}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body>
              <h4 className="mb-4">Upcoming Events</h4>
              {upcomingEvents.length > 0 ? (
                <div>
                  {upcomingEvents.map(event => (
                    <div key={event.id} className="p-3 mb-3 bg-light rounded">
                      <h5>{event.title}</h5>
                      <p className="mb-1">
                        <FaCalendarAlt className="me-2" />
                        {event.date}
                      </p>
                      <p className="mb-2">
                        <FaTree className="me-2" />
                        {event.location}
                      </p>
                      <Button variant="outline-primary" size="sm">View Details</Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted my-4">No upcoming events</p>
              )}
            </Card.Body>
          </Card>
        </Col>
        <Col md={4}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body>
              <h4 className="mb-3">Need Help?</h4>
              <GeminiChatWidget title="Ask about Urban Pulse" />
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default UserDashboardPage;
