<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 500px;
            margin-top: 50px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Simple Login Test</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Login Form</h5>
            </div>
            <div class="card-body">
                <div id="alert-container"></div>
                
                <form id="login-form">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" placeholder="Enter your password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" id="login-button">Login</button>
                </form>
                
                <div class="mt-3">
                    <h6>Test Accounts</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-sm btn-outline-secondary" id="test-user-button">Test User (<EMAIL>)</button>
                        <button class="btn btn-sm btn-outline-secondary" id="demo-user-button">Demo User (<EMAIL>)</button>
                        <button class="btn btn-sm btn-outline-secondary" id="admin-button">Admin (<EMAIL>)</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Response</h5>
            </div>
            <div class="card-body">
                <pre id="response-container">No response yet</pre>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Local Storage</h5>
            </div>
            <div class="card-body">
                <pre id="local-storage-container">No data yet</pre>
                <button class="btn btn-sm btn-outline-danger mt-2" id="clear-storage-button">Clear Storage</button>
            </div>
        </div>
        
        <div class="d-grid gap-2">
            <a href="/" class="btn btn-outline-primary">Go to Home Page</a>
            <a href="/dashboard" class="btn btn-outline-success">Go to Dashboard</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const loginButton = document.getElementById('login-button');
            const testUserButton = document.getElementById('test-user-button');
            const demoUserButton = document.getElementById('demo-user-button');
            const adminButton = document.getElementById('admin-button');
            const alertContainer = document.getElementById('alert-container');
            const responseContainer = document.getElementById('response-container');
            const localStorageContainer = document.getElementById('local-storage-container');
            const clearStorageButton = document.getElementById('clear-storage-button');
            
            // Function to show alert
            function showAlert(message, type) {
                alertContainer.innerHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            }
            
            // Function to update local storage display
            function updateLocalStorageDisplay() {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                
                let html = '';
                
                if (token) {
                    html += `<strong>Token:</strong> ${token.substring(0, 20)}...<br>`;
                } else {
                    html += '<strong>Token:</strong> Not found<br>';
                }
                
                if (user) {
                    try {
                        const userData = JSON.parse(user);
                        html += `<strong>User:</strong> ${JSON.stringify(userData, null, 2)}`;
                    } catch (error) {
                        html += `<strong>User:</strong> Error parsing user data: ${error.message}`;
                    }
                } else {
                    html += '<strong>User:</strong> Not found';
                }
                
                localStorageContainer.innerHTML = html;
            }
            
            // Function to handle login
            async function handleLogin(email, password) {
                try {
                    // Show loading state
                    loginButton.disabled = true;
                    loginButton.innerHTML = `
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing in...
                    `;
                    
                    // Make API request
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });
                    
                    // Parse response
                    const data = await response.json();
                    
                    // Display response
                    responseContainer.textContent = JSON.stringify(data, null, 2);
                    
                    // Handle response
                    if (response.ok) {
                        showAlert('Login successful!', 'success');
                        
                        // Store token and user data
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        
                        // Update local storage display
                        updateLocalStorageDisplay();
                    } else {
                        showAlert(data.message || 'Login failed', 'danger');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    responseContainer.textContent = `Error: ${error.message}`;
                    showAlert(`Login failed: ${error.message}`, 'danger');
                } finally {
                    // Reset button
                    loginButton.disabled = false;
                    loginButton.innerHTML = 'Login';
                }
            }
            
            // Handle form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const password = passwordInput.value;
                
                handleLogin(email, password);
            });
            
            // Handle test user login
            testUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle demo user login
            demoUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle admin login
            adminButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle clear storage button
            clearStorageButton.addEventListener('click', function() {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                updateLocalStorageDisplay();
                showAlert('Local storage cleared', 'info');
            });
            
            // Initial update
            updateLocalStorageDisplay();
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
