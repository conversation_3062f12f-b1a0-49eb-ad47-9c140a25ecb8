import api from '../utils/api';

// Get all transport feedback
export const getTransportFeedback = async (params) => {
  try {
    const res = await api.get('/transport', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch transport feedback');
  }
};

// Get transport feedback by ID
export const getTransportFeedbackById = async (id) => {
  try {
    const res = await api.get(`/transport/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch transport feedback');
  }
};

// Create new transport feedback
export const createTransportFeedback = async (feedbackData) => {
  try {
    const res = await api.post('/transport', feedbackData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create transport feedback');
  }
};

// Update transport feedback
export const updateTransportFeedback = async (id, feedbackData) => {
  try {
    const res = await api.put(`/transport/${id}`, feedbackData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update transport feedback');
  }
};

// Delete transport feedback
export const deleteTransportFeedback = async (id) => {
  try {
    const res = await api.delete(`/transport/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete transport feedback');
  }
};

// Vote on transport feedback
export const voteOnFeedback = async (id, voteType) => {
  try {
    const res = await api.post(`/transport/${id}/vote`, { voteType });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to vote on feedback');
  }
};

// Add comment to transport feedback
export const addFeedbackComment = async (feedbackId, comment) => {
  try {
    const res = await api.post(`/transport/${feedbackId}/comments`, { text: comment });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to add comment');
  }
};

// Get transport polls
export const getTransportPolls = async () => {
  try {
    const res = await api.get('/transport/polls');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch transport polls');
  }
};

// Vote on transport poll
export const voteOnPoll = async (pollId, optionId) => {
  try {
    const res = await api.post(`/transport/polls/${pollId}/vote`, { optionId });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to vote on poll');
  }
};

// Get user's transport feedback
export const getMyFeedback = async () => {
  try {
    const res = await api.get('/transport/my-feedback');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch your feedback');
  }
};
