const mongoose = require('mongoose');

const TransportSchema = new mongoose.Schema({
  routeName: {
    type: String,
    required: [true, 'Please add a route name'],
    trim: true,
    maxlength: [100, 'Route name cannot be more than 100 characters']
  },
  routeType: {
    type: String,
    required: [true, 'Please add a route type'],
    enum: ['bus', 'train', 'subway', 'tram', 'ferry', 'other']
  },
  rating: {
    type: Number,
    required: [true, 'Please add a rating'],
    min: 1,
    max: 5
  },
  feedback: {
    type: String,
    required: [true, 'Please add feedback'],
    maxlength: [500, 'Feedback cannot be more than 500 characters']
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Transport', TransportSchema);
