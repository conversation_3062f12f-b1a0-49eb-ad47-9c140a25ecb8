const mongoose = require('mongoose');

const UserStatsSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  issuesReported: {
    type: Number,
    default: 0
  },
  transportFeedbacks: {
    type: Number,
    default: 0
  },
  challengesCompleted: {
    type: Number,
    default: 0
  },
  reviewsSubmitted: {
    type: Number,
    default: 0
  },
  resourcesShared: {
    type: Number,
    default: 0
  },
  eventsAttended: {
    type: Number,
    default: 0
  },
  coursesCompleted: {
    type: Number,
    default: 0
  },
  // Detailed activity tracking
  issueDetails: [{
    issueId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Issue'
    },
    title: String,
    category: String,
    status: String,
    reportedAt: {
      type: Date,
      default: Date.now
    }
  }],
  transportDetails: [{
    feedbackId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Transport'
    },
    title: String,
    category: String,
    submittedAt: {
      type: Date,
      default: Date.now
    }
  }],
  challengeDetails: [{
    challengeId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Challenge'
    },
    title: String,
    category: String,
    points: Number,
    completedAt: {
      type: Date,
      default: Date.now
    }
  }],
  reviewDetails: [{
    reviewId: {
      type: mongoose.Schema.ObjectId,
      ref: 'ServiceReview'
    },
    serviceId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Service'
    },
    serviceName: String,
    rating: Number,
    submittedAt: {
      type: Date,
      default: Date.now
    }
  }],
  resourceDetails: [{
    resourceId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Resource'
    },
    title: String,
    category: String,
    sharedAt: {
      type: Date,
      default: Date.now
    }
  }],
  eventDetails: [{
    eventId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Event'
    },
    title: String,
    organizer: String,
    attendedAt: {
      type: Date,
      default: Date.now
    }
  }],
  courseDetails: [{
    courseId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Course'
    },
    title: String,
    category: String,
    completedAt: {
      type: Date,
      default: Date.now
    }
  }],
  lastUpdated: {
    type: Date,
    default: Date.now
  }
});

// Virtual for total activity count
UserStatsSchema.virtual('totalActivity').get(function() {
  return this.issuesReported + 
         this.transportFeedbacks + 
         this.challengesCompleted + 
         this.reviewsSubmitted + 
         this.resourcesShared + 
         this.eventsAttended + 
         this.coursesCompleted;
});

// Enable virtuals
UserStatsSchema.set('toJSON', { virtuals: true });
UserStatsSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('UserStats', UserStatsSchema);
