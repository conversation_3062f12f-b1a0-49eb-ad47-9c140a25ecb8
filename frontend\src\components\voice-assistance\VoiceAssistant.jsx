import React, { useState, useEffect, useRef } from 'react';
import { FaMicrophone, FaRobot, FaTimes, FaVolumeUp, FaSearch, FaQuestion, FaStop } from 'react-icons/fa';
import './VoiceAssistant.css';

const VoiceAssistant = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');
  const [showStatus, setShowStatus] = useState(false);
  const [voices, setVoices] = useState([]);
  const [activeInterface, setActiveInterface] = useState(null); // 'search' or 'question'
  const [searchQuery, setSearchQuery] = useState('');
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState({ text: '', source: '' });
  const [searchResults, setSearchResults] = useState([]);

  const dropdownRef = useRef(null);
  const recognitionRef = useRef(null);
  const synthRef = useRef(window.speechSynthesis);

  // Initialize speech recognition
  useEffect(() => {
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onstart = () => {
        setIsListening(true);
        displayStatus('Listening...');
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
        displayStatus('Stopped listening');
        setTimeout(() => setShowStatus(false), 2000);
      };

      recognitionRef.current.onerror = (event) => {
        setIsListening(false);
        displayStatus(`Error: ${event.error}`);
        setTimeout(() => setShowStatus(false), 2000);
      };

      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        
        if (activeInterface === 'search') {
          setSearchQuery(transcript);
          performSearch(transcript);
        } else if (activeInterface === 'question') {
          setQuestion(transcript);
          answerQuestion(transcript);
        }
      };
    } else {
      displayStatus('Speech recognition not supported in this browser');
    }

    // Load available voices
    const loadVoices = () => {
      const availableVoices = synthRef.current.getVoices();
      setVoices(availableVoices);
    };

    if (synthRef.current.onvoiceschanged !== undefined) {
      synthRef.current.onvoiceschanged = loadVoices;
    }
    
    loadVoices();

    // Cleanup
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.onend = null;
        recognitionRef.current.onstart = null;
        recognitionRef.current.onresult = null;
        recognitionRef.current.onerror = null;
      }
      stopSpeaking();
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const displayStatus = (message) => {
    setStatusMessage(message);
    setShowStatus(true);
  };

  const speak = (text) => {
    if (synthRef.current.speaking) {
      synthRef.current.cancel();
    }

    const utterance = new SpeechSynthesisUtterance(text);
    
    // Select a voice (preferably a female voice)
    if (voices.length > 0) {
      const femaleVoice = voices.find(voice =>
        voice.name.includes('female') ||
        voice.name.includes('Samantha') ||
        voice.name.includes('Google UK English Female')
      );
      utterance.voice = femaleVoice || voices[0];
    }

    utterance.pitch = 1;
    utterance.rate = 1;
    utterance.volume = 1;

    setIsSpeaking(true);
    displayStatus('Speaking...');

    utterance.onend = () => {
      setIsSpeaking(false);
      setShowStatus(false);
    };

    synthRef.current.speak(utterance);
  };

  const stopSpeaking = () => {
    if (synthRef.current.speaking) {
      synthRef.current.cancel();
      setIsSpeaking(false);
      displayStatus('Speech stopped');
      setTimeout(() => setShowStatus(false), 2000);
    }
  };

  const startListeningForSearch = () => {
    setActiveInterface('search');
    if (recognitionRef.current) {
      recognitionRef.current.start();
    }
  };

  const startListeningForQuestion = () => {
    setActiveInterface('question');
    if (recognitionRef.current) {
      recognitionRef.current.start();
    }
  };

  const performSearch = (query) => {
    if (!query) return;

    displayStatus(`Searching for: "${query}"`);

    // Mock search results for demonstration
    const mockResults = [
      {
        title: 'Urban Pulse - Sustainable Living',
        url: '/sustainable-living',
        description: 'Learn about sustainable living practices in urban environments.'
      },
      {
        title: 'Report Urban Issues',
        url: '/issue-reporting',
        description: 'Report potholes, streetlights, trash issues and more with simple forms and photos.'
      },
      {
        title: 'Public Transport Feedback',
        url: '/transport-feedback',
        description: 'Suggest improvements to bus routes, schedules, and services through votes and comments.'
      }
    ];

    setSearchResults(mockResults);
    speak(`Here are the search results for ${query}`);
  };

  const answerQuestion = (question) => {
    if (!question) return;

    displayStatus(`Finding answer to: "${question}"`);

    // Mock answer for demonstration
    const mockAnswer = {
      text: `Based on my knowledge, ${question} is related to urban sustainability. Urban Pulse helps citizens create smarter, more sustainable urban communities through collaboration and innovation. You can explore features like issue reporting, resource sharing, and sustainable living challenges.`,
      source: "This is a simulated answer from Urban Pulse assistant."
    };

    setAnswer(mockAnswer);
    speak(`Here's what I found about ${question}: ${mockAnswer.text}`);
  };

  const readSelectedText = () => {
    const selectedText = window.getSelection().toString();
    if (selectedText) {
      speak(selectedText);
    } else {
      displayStatus('No text selected');
      setTimeout(() => setShowStatus(false), 2000);
    }
  };

  const closeInterface = () => {
    setActiveInterface(null);
  };

  return (
    <div className="voice-assistant-container" ref={dropdownRef}>
      <div 
        className={`voice-assistant-icon ${isSpeaking ? 'speaking' : ''}`} 
        onClick={toggleDropdown}
      >
        <FaRobot />
      </div>

      {isOpen && (
        <div className="voice-assistant-dropdown">
          <div className="dropdown-option" onClick={() => {
            setActiveInterface('search');
            setIsOpen(false);
          }}>
            <FaSearch /> Voice Search
          </div>
          <div className="dropdown-option" onClick={() => {
            setActiveInterface('question');
            setIsOpen(false);
          }}>
            <FaQuestion /> Ask Questions
          </div>
          <div className="dropdown-option" onClick={() => {
            readSelectedText();
            setIsOpen(false);
          }}>
            <FaVolumeUp /> Read Selected Text
          </div>
          <div className="dropdown-option" onClick={() => {
            stopSpeaking();
            setIsOpen(false);
          }}>
            <FaStop /> Stop Speaking
          </div>
        </div>
      )}

      {showStatus && (
        <div className="status-indicator">
          <span>{statusMessage}</span>
        </div>
      )}

      {activeInterface === 'search' && (
        <div className="interface-overlay">
          <div className="interface-container search-interface">
            <div className="interface-header">
              <h2>Voice Search</h2>
              <button className="close-btn" onClick={closeInterface}>
                <FaTimes />
              </button>
            </div>
            <div className="interface-content">
              <div className="search-input-container">
                <input 
                  type="text" 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search..." 
                />
                <button onClick={() => performSearch(searchQuery)}>
                  <FaSearch />
                </button>
              </div>
              <div className="action-btn-container">
                <button 
                  className={`action-btn ${isListening ? 'listening' : ''}`}
                  onClick={startListeningForSearch}
                >
                  <FaMicrophone />
                  <span>Start Voice Search</span>
                </button>
              </div>
              {searchResults.length > 0 && (
                <div className="search-results">
                  {searchResults.map((result, index) => (
                    <div key={index} className="search-result-item">
                      <h3><a href={result.url}>{result.title}</a></h3>
                      <p>{result.description}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {activeInterface === 'question' && (
        <div className="interface-overlay">
          <div className="interface-container question-interface">
            <div className="interface-header">
              <h2>Ask Questions</h2>
              <button className="close-btn" onClick={closeInterface}>
                <FaTimes />
              </button>
            </div>
            <div className="interface-content">
              <div className="question-input-container">
                <input 
                  type="text" 
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  placeholder="Ask a question..." 
                />
                <button onClick={() => answerQuestion(question)}>
                  <FaQuestion />
                </button>
              </div>
              <div className="action-btn-container">
                <button 
                  className={`action-btn ${isListening ? 'listening' : ''}`}
                  onClick={startListeningForQuestion}
                >
                  <FaMicrophone />
                  <span>Start Voice Question</span>
                </button>
              </div>
              {answer.text && (
                <div className="answer-container">
                  <div className="answer-text">
                    <p>{answer.text}</p>
                    <p className="answer-source">{answer.source}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceAssistant;
