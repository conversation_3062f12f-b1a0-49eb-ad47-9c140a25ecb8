class NotificationService {
  constructor() {
    this.supported = typeof window !== 'undefined' && 'Notification' in window;
    this.permission = this.supported ? Notification.permission : 'default';
    this.listeners = [];
    this.notifications = [];
  }

  // Check if notifications are supported and permission is granted
  async isEnabled() {
    if (!this.supported) return false;
    return this.permission === 'granted';
  }

  // Get all notifications
  getNotifications() {
    return this.notifications;
  }

  // Add a notification to the list
  addNotification(notification) {
    const newNotification = {
      id: Date.now(),
      timestamp: new Date(),
      read: false,
      ...notification
    };

    this.notifications.unshift(newNotification);

    // Keep only the last 50 notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }

    // Notify listeners
    this.notifyListeners(newNotification);

    return newNotification;
  }

  // Mark a notification as read
  markAsRead(id) {
    const notification = this.notifications.find(n => n.id === id);
    if (notification) {
      notification.read = true;
      this.notifyListeners(notification);
    }
  }

  // Mark all notifications as read
  markAllAsRead() {
    this.notifications.forEach(n => n.read = true);
    this.notifyListeners();
  }

  // Add a listener for notifications
  addListener(callback) {
    this.listeners.push(callback);
    return () => this.removeListener(callback);
  }

  // Remove a listener
  removeListener(callback) {
    this.listeners = this.listeners.filter(l => l !== callback);
  }

  // Notify all listeners
  notifyListeners(notification) {
    this.listeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });
  }

  // Request permission to show notifications
  async requestPermission() {
    if (!this.supported) {
      console.warn('Notifications are not supported in this browser');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      this.permission = permission;
      return permission === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  // Show a notification
  async showNotification(title, options = {}) {
    // First, try to request permission if not already granted
    if (this.permission !== 'granted') {
      await this.requestPermission();
    }

    if (!await this.isEnabled()) {
      console.warn('Notifications are not enabled');

      // Still add to in-app notifications even if web notifications are disabled
      return this.addNotification({
        title,
        message: options.body || '',
        type: options.type || 'info',
        url: options.url || null
      });
    }

    try {
      const notification = new Notification(title, {
        icon: '/favicon.svg',
        badge: '/favicon.svg',
        silent: options.silent || false,
        requireInteraction: options.requireInteraction || false,
        ...options
      });

      // Handle notification click
      notification.onclick = (event) => {
        event.preventDefault();
        window.focus();

        if (options.url) {
          window.location.href = options.url;
        }

        notification.close();
      };

      // Also add to in-app notifications
      this.addNotification({
        title,
        message: options.body || '',
        type: options.type || 'info',
        url: options.url || null
      });

      return true;
    } catch (error) {
      console.error('Error showing notification:', error);
      return false;
    }
  }

  // Show an issue update notification
  async showIssueUpdate(issue) {
    return this.showNotification(
      `Issue Update: ${issue.title}`,
      {
        body: `Status changed to: ${issue.status}`,
        url: `/issue-reporting?id=${issue.id}`,
        tag: `issue-${issue.id}`
      }
    );
  }

  // Show an event reminder notification
  async showEventReminder(event) {
    return this.showNotification(
      `Event Reminder: ${event.title}`,
      {
        body: `${event.date} at ${event.time} - ${event.spaceName}`,
        url: `/green-spaces?event=${event.id}`,
        tag: `event-${event.id}`
      }
    );
  }

  // Show a community announcement notification
  async showCommunityAnnouncement(announcement) {
    return this.showNotification(
      `Community Announcement`,
      {
        body: announcement.title,
        url: announcement.url || '/',
        tag: `announcement-${announcement.id}`
      }
    );
  }

  // Show a new message notification
  async showNewMessage(message) {
    return this.showNotification(
      `New Message from ${message.user.name}`,
      {
        body: message.message.substring(0, 100),
        url: message.url,
        tag: `message-${message.room}`
      }
    );
  }

  // Show a resource interest notification (only to owner)
  async showResourceInterest(interest) {
    // Check if the current user is the owner of the resource
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

    // Only show notification if the current user is the owner
    if (currentUser && currentUser.id === interest.ownerId) {
      // Add to in-app notifications with all details
      this.addNotification({
        title: `New Interest in Your Resource`,
        message: `${interest.userName} is interested in your ${interest.resourceTitle}`,
        type: 'interest',
        url: `/resource-sharing/items/${interest.resourceId}?tab=chat`,
        resourceId: interest.resourceId,
        resourceTitle: interest.resourceTitle,
        userId: interest.userId,
        userName: interest.userName,
        timestamp: interest.timestamp || new Date()
      });

      // Show browser notification
      return this.showNotification(
        `New Interest in Your Resource`,
        {
          body: `${interest.userName} is interested in your ${interest.resourceTitle}`,
          url: `/resource-sharing/items/${interest.resourceId}?tab=chat`,
          type: 'interest',
          tag: `interest-${interest.resourceId}-${interest.userId}`,
          requireInteraction: true, // Keep notification visible until user interacts with it
          icon: '/assets/interest-icon.svg' // Custom icon for interest notifications
        }
      );
    }

    return false;
  }

  // Show a resource status update notification
  async showResourceStatusUpdate(data) {
    return this.showNotification(
      `Resource Status Updated`,
      {
        body: `${data.title} is now ${data.status}`,
        url: `/resource-sharing/items/${data.resourceId}`,
        tag: `resource-status-${data.resourceId}`,
        type: data.status === 'Sold' ? 'warning' : 'info'
      }
    );
  }

  // Show a resource message notification
  async showResourceMessage(data) {
    return this.showNotification(
      `New Message About Resource`,
      {
        body: `${data.senderName}: ${data.content.substring(0, 50)}${data.content.length > 50 ? '...' : ''}`,
        url: `/resource-sharing/items/${data.resourceId}?tab=chat`,
        tag: `resource-message-${data.resourceId}`,
        type: 'info'
      }
    );
  }
}

// Create a singleton instance
const notificationService = new NotificationService();

export default notificationService;
