import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, Card, Button, Badge, ProgressBar, Tab, Nav, Modal, Form } from 'react-bootstrap';
import { FaLeaf, FaTrophy, FaMedal, FaCheck, FaCalendarAlt, FaUsers, FaUserPlus } from 'react-icons/fa';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import api from '../utils/api';

const SustainableLivingPage = () => {
  const [showModal, setShowModal] = useState(false);
  const [selectedChallenge, setSelectedChallenge] = useState(null);
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [taskDates, setTaskDates] = useState({});

  // Realistic data for active challenges
  const activeChallenges = [
    {
      id: 1,
      title: 'Urban Waste Reduction Challenge',
      description: 'Reduce your household waste by implementing practical zero-waste strategies in your daily urban lifestyle.',
      category: 'Waste Reduction',
      participants: 287,
      daysLeft: 6,
      points: 120,
      tasks: [
        { id: 1, title: 'Conduct a waste audit of your household for 3 days', completed: true },
        { id: 2, title: 'Switch to reusable shopping bags, water bottles, and coffee cups', completed: true },
        { id: 3, title: 'Start a small compost bin suitable for apartment living', completed: false },
        { id: 4, title: 'Visit a bulk store and purchase 3 items without packaging', completed: false },
        { id: 5, title: 'Document your waste reduction journey with before/after photos', completed: false }
      ],
      progress: 40,
      image: 'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 2,
      title: 'Sustainable Urban Mobility',
      description: 'Transform your daily commute by exploring and utilizing sustainable transportation options available in our city.',
      category: 'Transportation',
      participants: 203,
      daysLeft: 12,
      points: 150,
      tasks: [
        { id: 1, title: 'Map out public transit routes from your home to frequent destinations', completed: true },
        { id: 2, title: 'Use public transportation for at least 5 commute days', completed: false },
        { id: 3, title: 'Try a bike-sharing service or use your own bike for 3 trips', completed: false },
        { id: 4, title: 'Walk to a destination you would normally drive to', completed: false },
        { id: 5, title: 'Calculate and share your carbon savings from sustainable commuting', completed: false }
      ],
      progress: 20,
      image: 'https://images.unsplash.com/photo-1519583272095-6433daf26b6e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 3,
      title: 'Local Food & Urban Agriculture',
      description: 'Connect with local food systems by sourcing ingredients from nearby producers and exploring urban farming opportunities.',
      category: 'Food',
      participants: 156,
      daysLeft: 18,
      points: 200,
      tasks: [
        { id: 1, title: 'Visit and purchase from 2 different farmers markets in the city', completed: true },
        { id: 2, title: 'Tour an urban farm or community garden in your neighborhood', completed: false },
        { id: 3, title: 'Prepare 5 meals using primarily ingredients sourced within 50 miles', completed: false },
        { id: 4, title: 'Grow an edible plant on your windowsill or balcony', completed: true },
        { id: 5, title: 'Create and share a seasonal recipe featuring local ingredients', completed: false }
      ],
      progress: 40,
      image: 'https://images.unsplash.com/photo-1488459716781-31db52582fe9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    }
  ];

  // Realistic data for upcoming challenges
  const upcomingChallenges = [
    {
      id: 4,
      title: 'Urban Energy Efficiency',
      description: 'Implement practical energy-saving measures in your home to reduce consumption and lower your utility bills.',
      category: 'Energy',
      startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      duration: '2 weeks',
      points: 150,
      image: 'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 5,
      title: 'Water Conservation in the City',
      description: 'Learn and apply water-saving techniques appropriate for urban apartments and homes to reduce water waste.',
      category: 'Water',
      startDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      duration: '10 days',
      points: 120,
      image: 'https://images.unsplash.com/photo-1527100673774-cce25eafaf7f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    }
  ];

  // Realistic data for completed challenges
  const completedChallenges = [
    {
      id: 6,
      title: 'Plastic-Free Urban Living',
      description: 'Successfully reduced single-use plastics by finding sustainable alternatives available in our city.',
      category: 'Waste Reduction',
      completedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      pointsEarned: 110,
      image: 'https://images.unsplash.com/photo-1621451537084-482c73073a0f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    {
      id: 7,
      title: 'Plant-Based City Diet',
      description: 'Explored plant-based eating options available in our city's restaurants, markets, and food services.',
      category: 'Food',
      completedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      pointsEarned: 180,
      image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    }
  ];

  // Realistic data for leaderboard
  const leaderboard = [
    { id: 1, name: 'Priya Sharma', points: 1320, badges: 9, rank: 1 },
    { id: 2, name: 'Marcus Johnson', points: 1175, badges: 8, rank: 2 },
    { id: 3, name: 'Sophia Chen', points: 1040, badges: 7, rank: 3 },
    { id: 4, name: 'Jamal Washington', points: 895, badges: 6, rank: 4 },
    { id: 5, name: 'Elena Rodriguez', points: 760, badges: 5, rank: 5 }
  ];

  // Realistic data for user's badges
  const userBadges = [
    { id: 1, name: 'Urban Waste Reducer', description: 'Completed 3 waste reduction challenges in an urban setting', icon: '🗑️', date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
    { id: 2, name: 'City Commuter', description: 'Used sustainable transportation options for 2 weeks in the city', icon: '🚲', date: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
    { id: 3, name: 'Local Food Explorer', description: 'Sourced 80% of food from local urban producers for one month', icon: '🥕', date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
    { id: 4, name: 'Urban Energy Optimizer', description: 'Reduced apartment energy consumption by 22% during challenge', icon: '💡', date: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] }
  ];

  const handleViewChallenge = (challenge) => {
    setSelectedChallenge(challenge);
    setShowModal(true);
  };

  const handleJoinChallenge = (challenge) => {
    // In a real app, this would make an API call to join the challenge
    console.log(`Joined challenge: ${challenge.title}`);
    setShowModal(false);
  };

  const handleCompleteTask = (challengeId, taskId) => {
    // In a real app, this would make an API call to mark the task as completed
    console.log(`Completed task ${taskId} for challenge ${challengeId}`);
    setShowCompletionModal(true);
  };

  // Function to schedule a task
  const handleScheduleTask = (challengeId, taskId, date) => {
    // In a real app, this would make an API call to schedule the task
    console.log(`Scheduled task ${taskId} for challenge ${challengeId} on ${date}`);

    // Update local state
    setTaskDates(prev => ({
      ...prev,
      [`${challengeId}-${taskId}`]: date
    }));
  };

  // Function to get scheduled date for a task
  const getTaskScheduledDate = (challengeId, taskId) => {
    return taskDates[`${challengeId}-${taskId}`] || null;
  };

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Sustainable Living Challenges</h1>
          <p className="lead">
            Join challenges that promote sustainable actions, earn badges, and make a positive impact on our city.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-success bg-opacity-10 p-3 rounded">
            <FaLeaf className="text-success fs-1" />
          </div>
        </Col>
      </Row>

      <Tab.Container defaultActiveKey="challenges">
        <Row>
          <Col md={3} className="mb-4">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-0">
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="challenges" className="rounded-0 px-4 py-3">
                      <FaLeaf className="me-2" />
                      Challenges
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="leaderboard" className="rounded-0 px-4 py-3">
                      <FaTrophy className="me-2" />
                      Leaderboard
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="badges" className="rounded-0 px-4 py-3">
                      <FaMedal className="me-2" />
                      Your Badges
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </Card.Body>
            </Card>

            <Card className="border-0 shadow-sm mt-4">
              <Card.Body className="p-4">
                <h5 className="mb-3">Your Progress</h5>
                <div className="d-flex justify-content-between mb-2">
                  <span>Total Points</span>
                  <span className="fw-bold">720</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Badges Earned</span>
                  <span className="fw-bold">4</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Challenges Completed</span>
                  <span className="fw-bold">7</span>
                </div>
                <div className="d-flex justify-content-between mb-3">
                  <span>Current Rank</span>
                  <span className="fw-bold">5th</span>
                </div>
                <div className="text-center">
                  <Badge bg="success" className="px-3 py-2">Level 3: Eco Enthusiast</Badge>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col md={9}>
            <Tab.Content>
              <Tab.Pane eventKey="challenges">
                <Card className="border-0 shadow-sm mb-4">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">Active Challenges</h4>

                    <Row xs={1} md={2} lg={3} className="g-4">
                      {activeChallenges.map((challenge) => (
                        <Col key={challenge.id}>
                          <Card className="h-100 border-0 shadow-sm">
                            <div style={{ height: '140px', overflow: 'hidden' }}>
                              <Card.Img
                                variant="top"
                                src={challenge.image}
                                alt={challenge.title}
                                style={{ objectFit: 'cover', height: '100%', width: '100%' }}
                              />
                            </div>
                            <Card.Body>
                              <div className="d-flex justify-content-between align-items-start mb-2">
                                <Badge bg="success" className="mb-2">{challenge.category}</Badge>
                                <Badge bg="warning" text="dark">{challenge.daysLeft} days left</Badge>
                              </div>
                              <Card.Title>{challenge.title}</Card.Title>
                              <Card.Text className="text-muted small mb-3">
                                {challenge.description.substring(0, 80)}...
                              </Card.Text>
                              <div className="mb-3">
                                <div className="d-flex justify-content-between mb-1 small">
                                  <span>Progress</span>
                                  <span>{challenge.progress}%</span>
                                </div>
                                <ProgressBar now={challenge.progress} variant="success" />
                              </div>
                              <div className="d-flex justify-content-between align-items-center">
                                <small className="text-muted">
                                  <FaUsers className="me-1" />
                                  {challenge.participants} participants
                                </small>
                                <Button
                                  variant="outline-success"
                                  size="sm"
                                  onClick={() => handleViewChallenge(challenge)}
                                >
                                  View Details
                                </Button>
                              </div>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>

                    <h4 className="mt-5 mb-4">Upcoming Challenges</h4>

                    <Row xs={1} md={2} className="g-4">
                      {upcomingChallenges.map((challenge) => (
                        <Col key={challenge.id}>
                          <Card className="border-0 shadow-sm">
                            <Row className="g-0">
                              <Col md={4}>
                                <div style={{ height: '100%', overflow: 'hidden' }}>
                                  <Card.Img
                                    src={challenge.image}
                                    alt={challenge.title}
                                    className="h-100"
                                    style={{ objectFit: 'cover' }}
                                  />
                                </div>
                              </Col>
                              <Col md={8}>
                                <Card.Body>
                                  <Badge bg="primary" className="mb-2">{challenge.category}</Badge>
                                  <Card.Title>{challenge.title}</Card.Title>
                                  <Card.Text className="text-muted small mb-3">
                                    {challenge.description.substring(0, 60)}...
                                  </Card.Text>
                                  <div className="d-flex justify-content-between align-items-center">
                                    <small className="text-muted">
                                      <FaCalendarAlt className="me-1" />
                                      Starts {challenge.startDate}
                                    </small>
                                    <Button
                                      variant="outline-primary"
                                      size="sm"
                                      onClick={() => handleViewChallenge(challenge)}
                                    >
                                      <FaUserPlus className="me-1" />
                                      Pre-register
                                    </Button>
                                  </div>
                                </Card.Body>
                              </Col>
                            </Row>
                          </Card>
                        </Col>
                      ))}
                    </Row>

                    <h4 className="mt-5 mb-4">Completed Challenges</h4>

                    <Row xs={1} md={2} className="g-4">
                      {completedChallenges.map((challenge) => (
                        <Col key={challenge.id}>
                          <Card className="border-0 shadow-sm">
                            <Row className="g-0">
                              <Col md={4}>
                                <div style={{ height: '100%', overflow: 'hidden' }}>
                                  <Card.Img
                                    src={challenge.image}
                                    alt={challenge.title}
                                    className="h-100"
                                    style={{ objectFit: 'cover', filter: 'grayscale(0.5)' }}
                                  />
                                </div>
                              </Col>
                              <Col md={8}>
                                <Card.Body>
                                  <Badge bg="secondary" className="mb-2">{challenge.category}</Badge>
                                  <Card.Title>{challenge.title}</Card.Title>
                                  <Card.Text className="text-muted small mb-3">
                                    {challenge.description.substring(0, 60)}...
                                  </Card.Text>
                                  <div className="d-flex justify-content-between align-items-center">
                                    <small className="text-muted">
                                      Completed on {challenge.completedDate}
                                    </small>
                                    <Badge bg="success">
                                      +{challenge.pointsEarned} points
                                    </Badge>
                                  </div>
                                </Card.Body>
                              </Col>
                            </Row>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </Card.Body>
                </Card>
              </Tab.Pane>

              <Tab.Pane eventKey="leaderboard">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">Sustainability Leaderboard</h4>

                    <div className="table-responsive">
                      <table className="table table-hover">
                        <thead>
                          <tr>
                            <th>Rank</th>
                            <th>Name</th>
                            <th>Points</th>
                            <th>Badges</th>
                          </tr>
                        </thead>
                        <tbody>
                          {leaderboard.map((user) => (
                            <tr key={user.id} className={user.rank <= 3 ? 'table-success' : ''}>
                              <td>
                                {user.rank === 1 && <FaTrophy className="text-warning me-1" />}
                                {user.rank === 2 && <FaTrophy className="text-secondary me-1" />}
                                {user.rank === 3 && <FaTrophy className="text-danger me-1" />}
                                {user.rank}
                              </td>
                              <td>{user.name}</td>
                              <td>{user.points}</td>
                              <td>{user.badges}</td>
                            </tr>
                          ))}
                          <tr className="table-active">
                            <td>5</td>
                            <td>You</td>
                            <td>720</td>
                            <td>4</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <div className="text-center mt-4">
                      <p className="text-muted">
                        You need 130 more points to reach the 4th position!
                      </p>
                      <Button variant="success">
                        View Full Leaderboard
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Tab.Pane>

              <Tab.Pane eventKey="badges">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">Your Earned Badges</h4>

                    <Row xs={1} md={2} className="g-4">
                      {userBadges.map((badge) => (
                        <Col key={badge.id}>
                          <Card className="border-0 shadow-sm h-100">
                            <Card.Body className="d-flex">
                              <div className="me-3 fs-1">
                                {badge.icon}
                              </div>
                              <div>
                                <Card.Title>{badge.name}</Card.Title>
                                <Card.Text className="text-muted small">
                                  {badge.description}
                                </Card.Text>
                                <small className="text-muted">
                                  Earned on {badge.date}
                                </small>
                              </div>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>

                    <h5 className="mt-5 mb-3">Badges to Earn</h5>

                    <Row xs={1} md={2} className="g-4">
                      <Col>
                        <Card className="border-0 shadow-sm h-100 bg-light">
                          <Card.Body className="d-flex">
                            <div className="me-3 fs-1 text-muted">
                              🌱
                            </div>
                            <div>
                              <Card.Title className="text-muted">Sustainability Champion</Card.Title>
                              <Card.Text className="text-muted small">
                                Complete 10 sustainability challenges
                              </Card.Text>
                              <small className="text-muted">
                                Progress: 7/10 challenges
                              </small>
                            </div>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col>
                        <Card className="border-0 shadow-sm h-100 bg-light">
                          <Card.Body className="d-flex">
                            <div className="me-3 fs-1 text-muted">
                              💧
                            </div>
                            <div>
                              <Card.Title className="text-muted">Water Saver</Card.Title>
                              <Card.Text className="text-muted small">
                                Complete the Water Conservation Week challenge
                              </Card.Text>
                              <small className="text-muted">
                                Available in upcoming challenges
                              </small>
                            </div>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Challenge Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Challenge Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedChallenge && (
            <Row>
              <Col md={5}>
                <img
                  src={selectedChallenge.image}
                  alt={selectedChallenge.title}
                  className="img-fluid rounded mb-3"
                />
                <Badge bg={selectedChallenge.category ? 'success' : 'primary'} className="mb-2">
                  {selectedChallenge.category}
                </Badge>
                <h4>{selectedChallenge.title}</h4>
                <p className="text-muted">{selectedChallenge.description}</p>

                {selectedChallenge.daysLeft && (
                  <div className="mb-3">
                    <div className="d-flex justify-content-between mb-1">
                      <span>Time Remaining</span>
                      <span>{selectedChallenge.daysLeft} days</span>
                    </div>
                    <ProgressBar
                      now={100 - (selectedChallenge.daysLeft / 30 * 100)}
                      variant="warning"
                    />
                  </div>
                )}

                {selectedChallenge.startDate && (
                  <p className="mb-2">
                    <FaCalendarAlt className="me-2" />
                    Starts on {selectedChallenge.startDate}
                  </p>
                )}

                {selectedChallenge.participants && (
                  <p className="mb-2">
                    <FaUsers className="me-2" />
                    {selectedChallenge.participants} participants
                  </p>
                )}

                {selectedChallenge.points && (
                  <p className="mb-3">
                    <FaTrophy className="me-2" />
                    {selectedChallenge.points} points upon completion
                  </p>
                )}
              </Col>
              <Col md={7}>
                {selectedChallenge.tasks ? (
                  <>
                    <h5 className="mb-3">Challenge Tasks</h5>
                    <div className="mb-4">
                      {selectedChallenge.tasks.map((task) => (
                        <div key={task.id} className="mb-3">
                          <div className="d-flex align-items-center mb-2">
                            <div className="me-3">
                              {task.completed ? (
                                <span className="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '30px', height: '30px' }}>
                                  <FaCheck />
                                </span>
                              ) : (
                                <Button
                                  variant="outline-success"
                                  className="rounded-circle p-0 d-inline-flex align-items-center justify-content-center"
                                  style={{ width: '30px', height: '30px' }}
                                  onClick={() => handleCompleteTask(selectedChallenge.id, task.id)}
                                >
                                  <FaCheck />
                                </Button>
                              )}
                            </div>
                            <div>
                              <p className={`mb-0 ${task.completed ? 'text-decoration-line-through' : ''}`}>
                                {task.title}
                              </p>
                            </div>
                          </div>

                          {!task.completed && (
                            <div className="ms-5 mt-2">
                              <div className="d-flex align-items-center">
                                <FaCalendarAlt className="text-muted me-2" />
                                <small className="text-muted me-2">Schedule:</small>
                                <DatePicker
                                  selected={getTaskScheduledDate(selectedChallenge.id, task.id)}
                                  onChange={(date) => handleScheduleTask(selectedChallenge.id, task.id, date)}
                                  minDate={new Date()}
                                  placeholderText="Select a date"
                                  className="form-control form-control-sm"
                                  dateFormat="MMM d, yyyy"
                                />
                              </div>
                              {getTaskScheduledDate(selectedChallenge.id, task.id) && (
                                <div className="ms-4 mt-1">
                                  <small className="text-success">
                                    Scheduled for: {getTaskScheduledDate(selectedChallenge.id, task.id).toLocaleDateString()}
                                  </small>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    <div className="mb-3">
                      <div className="d-flex justify-content-between mb-1">
                        <span>Overall Progress</span>
                        <span>{selectedChallenge.progress}%</span>
                      </div>
                      <ProgressBar now={selectedChallenge.progress} variant="success" />
                    </div>
                  </>
                ) : (
                  <>
                    <h5 className="mb-3">Challenge Details</h5>
                    <p>
                      Join this challenge to make a positive impact on our environment and community.
                      You'll receive detailed tasks and instructions once the challenge begins.
                    </p>

                    <h5 className="mb-3 mt-4">What You'll Need</h5>
                    <ul>
                      <li>Commitment to sustainable practices</li>
                      <li>Willingness to track and share your progress</li>
                      <li>A positive attitude and desire to make a difference</li>
                    </ul>

                    <h5 className="mb-3 mt-4">Benefits</h5>
                    <ul>
                      <li>Reduce your environmental footprint</li>
                      <li>Learn new sustainable habits</li>
                      <li>Connect with like-minded community members</li>
                      <li>Earn points and badges for your profile</li>
                    </ul>
                  </>
                )}
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          {selectedChallenge && !selectedChallenge.completedDate && (
            <Button
              variant={selectedChallenge.daysLeft ? 'success' : 'primary'}
              onClick={() => handleJoinChallenge(selectedChallenge)}
            >
              {selectedChallenge.daysLeft ? 'View My Progress' : 'Join Challenge'}
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Task Completion Modal */}
      <Modal show={showCompletionModal} onHide={() => setShowCompletionModal(false)} centered>
        <Modal.Body className="text-center p-5">
          <div className="mb-4">
            <div className="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '80px', height: '80px' }}>
              <FaCheck className="fs-1" />
            </div>
            <h4>Task Completed!</h4>
            <p className="text-muted">
              Great job! You've earned 20 points for completing this task.
            </p>
          </div>
          <div className="d-flex justify-content-center">
            <Button variant="success" onClick={() => setShowCompletionModal(false)}>
              Continue
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    </Container>
  );
};

export default SustainableLivingPage;
