import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Container } from 'react-bootstrap';

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Pages
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ProfilePage from './pages/ProfilePage';
import UserDashboardPage from './pages/UserDashboardPage';
import CarbonFootprintPage from './pages/CarbonFootprintPage';
import SimpleGreenSpacePage from './pages/SimpleGreenSpacePage';
import IssueReportingPage from './pages/IssueReportingPage';
import IssuesMapPage from './pages/IssuesMapPage';
import TransportFeedbackPage from './pages/TransportFeedbackPage';
import StandaloneDashboardPage from './pages/StandaloneDashboardPage';
import PublicServiceReviewPage from './pages/PublicServiceReviewPage';
import ResourceSharingPage from './pages/ResourceSharingPage';
import ItemDetailPage from './pages/ItemDetailPage';
import ShareItemPage from './pages/ShareItemPage';
import LearningHubPage from './pages/LearningHubPage';
import AIAssistantPage from './pages/AIAssistantPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import AdminLoginPage from './pages/AdminLoginPage';
import TestPage from './pages/TestPage';
import NotFoundPage from './pages/NotFoundPage';

function App() {
  // Simplified App component without socket.io and notifications
  return (
    <div className="app-container d-flex flex-column min-vh-100">
      <Header />
      <main className="flex-grow-1 py-4">
        <Container>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/admin-login" element={<AdminLoginPage />} />
            <Route path="/test" element={<TestPage />} />

            {/* Feature Routes */}
            <Route path="/green-spaces" element={<CarbonFootprintPage />} />
            <Route path="/simple-green-spaces" element={<SimpleGreenSpacePage />} />
            <Route path="/issue-reporting" element={<IssueReportingPage />} />
            <Route path="/issues-map" element={<IssuesMapPage />} />
            <Route path="/transport-feedback" element={<TransportFeedbackPage />} />
            <Route path="/sustainable-living" element={<StandaloneDashboardPage />} />
            <Route path="/public-services" element={<PublicServiceReviewPage />} />
            <Route path="/resource-sharing" element={<ResourceSharingPage />} />
            <Route path="/resource-sharing/items/:id" element={<ItemDetailPage />} />
            <Route path="/resource-sharing/share-item" element={<ShareItemPage />} />
            <Route path="/learning-hub" element={<LearningHubPage />} />
            <Route path="/ai-assistant" element={<AIAssistantPage />} />

            {/* Protected Routes */}
            <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
            <Route path="/dashboard" element={<ProtectedRoute><UserDashboardPage /></ProtectedRoute>} />

            {/* Admin Routes */}
            <Route path="/admin/*" element={<ProtectedRoute adminOnly={true}><AdminDashboardPage /></ProtectedRoute>} />

            {/* 404 Route */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Container>
      </main>
      <Footer />
    </div>
  );
}

export default App;
