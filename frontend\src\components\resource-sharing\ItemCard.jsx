import React from 'react';
import { Card, Badge, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { FaHeart } from 'react-icons/fa';

/**
 * ItemCard component displays a single item in the resource sharing platform
 * @param {Object} props
 * @param {import('../../types/item').Item} props.item - The item to display
 */
const ItemCard = ({ item }) => {
  const { id, title, category, imageUrl, availability, ownerName, location } = item;

  return (
    <Card className="h-100 border-0 shadow-sm resource-card">
      <Link to={`/resource-sharing/items/${id}`} className="text-decoration-none">
        <div style={{ height: '160px', overflow: 'hidden' }}>
          <Card.Img
            variant="top"
            src={imageUrl}
            alt={title}
            style={{ objectFit: 'cover', height: '100%', width: '100%' }}
          />
        </div>
        <Card.Body>
          <div className="d-flex justify-content-between align-items-start mb-2">
            <Badge bg="primary" className="mb-2">
              {category}
            </Badge>
            <Button
              variant="link"
              className="p-0 text-danger"
            >
              <FaHeart />
            </Button>
          </div>
          <Card.Title>{title}</Card.Title>
          <div className="d-flex justify-content-between mt-2 text-muted small">
            <span>{ownerName}</span>
            <span>{location}</span>
          </div>
          <div className="mt-2">
            <Badge bg={availability === 'Available' ? 'success' : 'secondary'}>
              {availability}
            </Badge>
          </div>
        </Card.Body>
      </Link>
    </Card>
  );
};

export default ItemCard;
