<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Urban Pulse - Smart Urban Living</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .loading-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .spinner {
            width: 3rem;
            height: 3rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div>
            <div class="spinner-border text-primary spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h2>Loading Urban Pulse</h2>
            <p>Please wait while we prepare your experience...</p>
            <div id="auth-status" class="mt-3"></div>
        </div>
    </div>

    <script>
        // Check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('token');
            const userDataString = localStorage.getItem('user');
            
            if (token && userDataString) {
                try {
                    const userData = JSON.parse(userDataString);
                    document.getElementById('auth-status').innerHTML = `
                        <div class="alert alert-success">
                            Logged in as: <strong>${userData.name || 'User'}</strong>
                        </div>
                    `;
                    return true;
                } catch (error) {
                    document.getElementById('auth-status').innerHTML = `
                        <div class="alert alert-warning">
                            Authentication issue detected. Attempting to fix...
                        </div>
                    `;
                    return false;
                }
            } else {
                document.getElementById('auth-status').innerHTML = `
                    <div class="alert alert-info">
                        Not logged in
                    </div>
                `;
                return false;
            }
        }
        
        // Redirect to main app with auth script
        function redirectToApp() {
            const isLoggedIn = checkAuthStatus();
            
            // Add a small delay to show the status
            setTimeout(() => {
                // Redirect to home page with special parameter
                window.location.href = '/?auth=' + new Date().getTime();
            }, 2000);
        }
        
        // Run the redirect
        redirectToApp();
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
