import React, { useState, useMemo } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, Form, Button, Alert, Badge, Modal, Tab, Nav } from 'react-bootstrap';
import { FaExchangeAlt, FaSearch, FaMapMarkerAlt, FaUser, FaCalendarAlt, FaPlus, FaHeart } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import ItemGrid from '../components/resource-sharing/ItemGrid';
import Filters from '../components/resource-sharing/Filters';
import { mockItems, allCategories, allLocations } from '../data/mockItems';
import { useGeolocation, calculateDistance } from '../hooks/useGeolocation';

// Distance threshold in kilometers for "near me" filtering
const NEARBY_THRESHOLD_KM = 10;

const ResourceSharingPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [showAvailableOnly, setShowAvailableOnly] = useState(false);
  const [showNearMe, setShowNearMe] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  const { latitude, longitude, error: geoError, loading: geoLoading } = useGeolocation();

  // Check if we can use location-based filtering
  const canUseLocation = Boolean(latitude && longitude);

  // If user selects "Near Me" but we don't have their location, disable it
  if (showNearMe && !canUseLocation && !geoLoading) {
    setShowNearMe(false);
  }

  // Mock data for user's shared resources
  const userResources = [
    {
      id: '101',
      title: 'Pressure Washer',
      category: 'Tools',
      description: 'Electric pressure washer, good for cleaning patios and driveways.',
      location: 'Eastside',
      postedDate: '2023-03-20',
      requests: 3,
      status: 'Active'
    },
    {
      id: '102',
      title: 'Camping Tent (4-Person)',
      category: 'Sports',
      description: 'Four-person camping tent, waterproof and easy to set up.',
      location: 'Eastside',
      postedDate: '2023-04-01',
      requests: 1,
      status: 'Active'
    }
  ];

  // Mock data for user's borrowed resources
  const borrowedResources = [
    {
      id: '201',
      title: 'Food Processor',
      category: 'Kitchen',
      owner: 'Jane S.',
      borrowDate: '2023-04-05',
      returnDate: '2023-04-12',
      status: 'Returned'
    },
    {
      id: '202',
      title: 'Ladder',
      category: 'Tools',
      owner: 'Mark L.',
      borrowDate: '2023-04-15',
      returnDate: '2023-04-22',
      status: 'Active'
    }
  ];

  const handleAddResource = () => {
    navigate('/resource-sharing/share-item');
  };

  // Apply filters and search to items
  const filteredItems = useMemo(() => {
    if (!mockItems) return [];

    return mockItems.filter(item => {
      // Filter by search query
      const matchesSearch = searchTerm === '' ||
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase());

      // Filter by category
      const matchesCategory = selectedCategory === null || item.category === selectedCategory;

      // Filter by location
      const matchesLocation = selectedLocation === null || item.location === selectedLocation;

      // Filter by availability
      const matchesAvailability = !showAvailableOnly || item.availability === 'Available';

      // Filter by proximity to user's location
      let matchesNearby = true;
      if (showNearMe && canUseLocation) {
        // If the item has coordinates, calculate distance
        if (item.coordinates) {
          const distance = calculateDistance(
            latitude,
            longitude,
            item.coordinates.latitude,
            item.coordinates.longitude
          );
          matchesNearby = distance <= NEARBY_THRESHOLD_KM;
        } else {
          // If the item doesn't have coordinates, it doesn't match the "near me" filter
          matchesNearby = false;
        }
      }

      return matchesSearch && matchesCategory && matchesLocation && matchesAvailability && matchesNearby;
    });
  }, [searchTerm, selectedCategory, selectedLocation, showAvailableOnly, showNearMe, canUseLocation, latitude, longitude]);

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Resource Sharing Platform</h1>
          <p className="lead">
            Share unused resources with neighbors - tools, equipment, books, and more.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-primary bg-opacity-10 p-3 rounded">
            <FaExchangeAlt className="text-primary fs-1" />
          </div>
        </Col>
      </Row>

      {geoLoading && (
        <div className="mb-4 text-sm text-muted d-flex align-items-center gap-2">
          <div className="spinner-border spinner-border-sm text-primary me-2" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          Getting your location...
        </div>
      )}

      {geoError && (
        <Alert variant="warning" className="mb-4">
          {geoError}. "Near Me" functionality will be disabled.
        </Alert>
      )}

      {showNearMe && canUseLocation && (
        <div className="mb-4 d-flex align-items-center gap-2 text-sm text-secondary">
          <FaMapMarkerAlt />
          Showing items within {NEARBY_THRESHOLD_KM} km of your location
        </div>
      )}

      <Tab.Container defaultActiveKey="browse">
        <Row>
          <Col lg={3} className="mb-4">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-0">
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="browse" className="rounded-0 px-4 py-3">
                      <FaSearch className="me-2" />
                      Browse Resources
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="my-resources" className="rounded-0 px-4 py-3">
                      <FaUser className="me-2" />
                      My Resources
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </Card.Body>
            </Card>

            <Filters
              categories={allCategories}
              locations={allLocations}
              selectedCategory={selectedCategory}
              selectedLocation={selectedLocation}
              onSelectCategory={setSelectedCategory}
              onSelectLocation={setSelectedLocation}
              onShowAvailableOnly={setShowAvailableOnly}
              showAvailableOnly={showAvailableOnly}
              onShowNearMe={setShowNearMe}
              showNearMe={showNearMe}
              canUseLocation={canUseLocation}
            />
          </Col>

          <Col lg={9}>
            <Tab.Content>
              <Tab.Pane eventKey="browse">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <h4 className="mb-0">Available Resources</h4>
                  <Button
                    variant="primary"
                    onClick={handleAddResource}
                  >
                    <FaPlus className="me-2" />
                    Share a Resource
                  </Button>
                </div>

                <ItemGrid items={filteredItems} />
              </Tab.Pane>

              <Tab.Pane eventKey="my-resources">
                <Card className="border-0 shadow-sm mb-4">
                  <Card.Body className="p-4">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">My Shared Resources</h4>
                      <Button
                        variant="primary"
                        onClick={handleAddResource}
                      >
                        <FaPlus className="me-2" />
                        Share a Resource
                      </Button>
                    </div>

                    {userResources.length > 0 ? (
                      <div className="table-responsive">
                        <table className="table table-hover">
                          <thead>
                            <tr>
                              <th>Resource</th>
                              <th>Category</th>
                              <th>Posted Date</th>
                              <th>Requests</th>
                              <th>Status</th>
                              <th>Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {userResources.map((resource) => (
                              <tr key={resource.id}>
                                <td>{resource.title}</td>
                                <td>{resource.category}</td>
                                <td>{resource.postedDate}</td>
                                <td>{resource.requests}</td>
                                <td>
                                  <Badge bg={resource.status === 'Active' ? 'success' : 'secondary'}>
                                    {resource.status}
                                  </Badge>
                                </td>
                                <td>
                                  <Button variant="link" className="p-0 text-decoration-none me-2">
                                    Edit
                                  </Button>
                                  <Button variant="link" className="p-0 text-decoration-none text-danger">
                                    Remove
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-muted text-center mb-0">You haven't shared any resources yet.</p>
                    )}
                  </Card.Body>
                </Card>

                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">Borrowed Resources</h4>

                    {borrowedResources.length > 0 ? (
                      <div className="table-responsive">
                        <table className="table table-hover">
                          <thead>
                            <tr>
                              <th>Resource</th>
                              <th>Category</th>
                              <th>Owner</th>
                              <th>Borrow Date</th>
                              <th>Return Date</th>
                              <th>Status</th>
                            </tr>
                          </thead>
                          <tbody>
                            {borrowedResources.map((resource) => (
                              <tr key={resource.id}>
                                <td>{resource.title}</td>
                                <td>{resource.category}</td>
                                <td>{resource.owner}</td>
                                <td>{resource.borrowDate}</td>
                                <td>{resource.returnDate}</td>
                                <td>
                                  <Badge bg={resource.status === 'Active' ? 'primary' : 'success'}>
                                    {resource.status}
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-muted text-center mb-0">You haven't borrowed any resources yet.</p>
                    )}
                  </Card.Body>
                </Card>
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Add Resource Modal */}
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Share a Resource</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}

          <p>To share a new resource, please use our dedicated form.</p>
          <div className="d-grid">
            <Button
              variant="primary"
              onClick={handleAddResource}
              disabled={loading}
            >
              Continue to Share Item Form
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    </Container>
  );
};

export default ResourceSharingPage;
