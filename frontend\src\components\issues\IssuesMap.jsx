import React, { useState, useEffect } from 'react';
import { <PERSON>, Spinner, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { FaMapMarkerAlt, FaExclamationTriangle } from 'react-icons/fa';
import MapComponent from '../map/MapComponent';
import issueService from '../../utils/issueService';

const IssuesMap = ({ height = '500px', width = '100%' }) => {
  const [issues, setIssues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [mapCenter, setMapCenter] = useState([51.505, -0.09]); // Default to London
  const [selectedIssue, setSelectedIssue] = useState(null);

  // Fetch all issues
  useEffect(() => {
    const fetchIssues = async () => {
      try {
        setLoading(true);
        // Use the issueService to fetch all issues
        const result = await issueService.getIssues();
        setIssues(result.data || []);

        // If we have issues with coordinates, center the map on the first one
        if (result.data && result.data.length > 0) {
          const issuesWithCoords = result.data.filter(issue =>
            issue.coordinates && issue.coordinates.lat && issue.coordinates.lng
          );

          if (issuesWithCoords.length > 0) {
            setMapCenter([
              issuesWithCoords[0].coordinates.lat,
              issuesWithCoords[0].coordinates.lng
            ]);
          }
        }
      } catch (err) {
        console.error('Error fetching issues:', err);
        setError('Failed to load issues. Please try again later.');

        // Use mock data if API fails
        const mockIssues = [
          {
            id: 1,
            title: 'Pothole on Main Street',
            category: 'Roads',
            status: 'Open',
            description: 'Large pothole causing traffic hazards',
            location: '123 Main St',
            coordinates: { lat: 51.505, lng: -0.09 },
            photo: 'https://images.unsplash.com/photo-1515162816999-a0c47dc192f7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          },
          {
            id: 2,
            title: 'Broken Streetlight',
            category: 'Lighting',
            status: 'In Progress',
            description: 'Streetlight not working for 3 days',
            location: 'Corner of Oak and Pine',
            coordinates: { lat: 51.51, lng: -0.1 },
            photo: 'https://images.unsplash.com/photo-1551462147-ff29053bfc14?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          },
          {
            id: 3,
            title: 'Overflowing Trash Bin',
            category: 'Waste',
            status: 'Resolved',
            description: 'Trash bin overflowing and causing odor',
            location: 'City Park',
            coordinates: { lat: 51.515, lng: -0.08 },
            photo: 'https://images.unsplash.com/photo-1605600659873-d808a13e4d2a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          }
        ];

        setIssues(mockIssues);
        setMapCenter([51.505, -0.09]);
      } finally {
        setLoading(false);
      }
    };

    fetchIssues();
  }, []);

  // Get status badge
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Open':
        return <Badge bg="danger">Open</Badge>;
      case 'In Progress':
        return <Badge bg="warning">In Progress</Badge>;
      case 'Resolved':
        return <Badge bg="success">Resolved</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  // Create map markers from issues
  const getMarkers = () => {
    return issues
      .filter(issue => issue.coordinates && issue.coordinates.lat && issue.coordinates.lng)
      .map(issue => ({
        lat: issue.coordinates.lat,
        lng: issue.coordinates.lng,
        popup: `
          <div class="issue-popup">
            <h6>${issue.title}</h6>
            <div class="mb-1">${getStatusBadgeHTML(issue.status)}</div>
            <p class="small mb-1">${issue.description?.substring(0, 100) || 'No description'}${issue.description?.length > 100 ? '...' : ''}</p>
            <button
              class="btn btn-sm btn-outline-primary mt-1"
              onclick="window.viewIssueDetails(${issue.id || issue._id}); return false;"
            >
              View Details
            </button>
          </div>
        `,
        icon: getMarkerIcon(issue.status)
      }));
  };

  // Get HTML for status badge in popup
  const getStatusBadgeHTML = (status) => {
    switch (status) {
      case 'Open':
        return '<span class="badge bg-danger">Open</span>';
      case 'In Progress':
        return '<span class="badge bg-warning">In Progress</span>';
      case 'Resolved':
        return '<span class="badge bg-success">Resolved</span>';
      default:
        return `<span class="badge bg-secondary">${status}</span>`;
    }
  };

  // Get marker icon based on status
  const getMarkerIcon = (status) => {
    switch (status) {
      case 'Open':
        return 'danger';
      case 'In Progress':
        return 'warning';
      case 'Resolved':
        return 'success';
      default:
        return 'info';
    }
  };

  // Make the function available globally for map marker clicks
  useEffect(() => {
    window.viewIssueDetails = (issueId) => {
      const issue = issues.find(i => (i.id === issueId || i._id === issueId));
      if (issue) {
        setSelectedIssue(issue);
      }
    };

    // Cleanup function to remove the global function when component unmounts
    return () => {
      delete window.viewIssueDetails;
    };
  }, [issues]);

  return (
    <Card className="border-0 shadow-sm">
      <Card.Body className="p-0">
        {loading ? (
          <div className="text-center py-5">
            <Spinner animation="border" variant="primary" />
            <p className="mt-3 text-muted">Loading issues map...</p>
          </div>
        ) : error ? (
          <Alert variant="danger" className="m-3">
            <FaExclamationTriangle className="me-2" />
            {error}
          </Alert>
        ) : (
          <div style={{ height, width }}>
            <MapComponent
              center={mapCenter}
              zoom={13}
              height={height}
              width={width}
              markers={getMarkers()}
            />
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default IssuesMap;
