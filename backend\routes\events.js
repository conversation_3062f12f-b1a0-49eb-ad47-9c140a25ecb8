const express = require('express');
const {
  getEvents,
  getEvent,
  createEvent,
  updateEvent,
  deleteEvent,
  joinEvent,
  leaveEvent,
  getSpaceEvents,
  getMyEvents,
  getUpcomingEvents
} = require('../controllers/eventController');

const router = express.Router({ mergeParams: true });

const { protect } = require('../middleware/auth');

router
  .route('/')
  .get(getEvents)
  .post(protect, createEvent);

router
  .route('/my-events')
  .get(protect, getMyEvents);

router
  .route('/upcoming')
  .get(protect, getUpcomingEvents);

router
  .route('/:id')
  .get(getEvent)
  .put(protect, updateEvent)
  .delete(protect, deleteEvent);

router
  .route('/:id/join')
  .post(protect, joinEvent)
  .delete(protect, leaveEvent);

module.exports = router;
