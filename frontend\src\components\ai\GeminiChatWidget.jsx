import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Button, ListGroup, Spinner } from 'react-bootstrap';
import { FaPaperPlane, FaRobot, FaExpand } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import geminiService from '../../services/geminiService';

const GeminiChatWidget = ({ title = "Ask Reuse Assistant", maxHeight = "300px" }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef(null);

  // Initialize chat with a welcome message
  useEffect(() => {
    const initialMessage = {
      role: 'model',
      parts: [{ text: "Hello! How can I help you today?" }],
      timestamp: new Date()
    };
    setMessages([initialMessage]);
    geminiService.initChat();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a new message
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!newMessage.trim() || loading) return;

    // Add user message to UI
    const userMessage = {
      role: 'user',
      parts: [{ text: newMessage }],
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setLoading(true);

    try {
      // Get response from Gemini
      const response = await geminiService.getResponse(newMessage);

      // Add AI response to UI
      const aiMessage = {
        role: 'model',
        parts: [{ text: response }],
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error getting response:', error);

      // Add error message
      const errorMessage = {
        role: 'model',
        parts: [{ text: "I'm sorry, I encountered an error. Please try again." }],
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="gemini-chat-widget border-0 shadow-sm">
      <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
        <div className="d-flex align-items-center">
          <FaRobot className="me-2" />
          <h6 className="mb-0">{title}</h6>
        </div>
        <Link to="/ai-assistant" className="text-white" title="Open Reuse Ideas Generator">
          <FaExpand />
        </Link>
      </Card.Header>

      <ListGroup variant="flush" className="chat-messages" style={{ maxHeight, overflowY: 'auto' }}>
        {messages.map((msg, index) => (
          <ListGroup.Item
            key={index}
            className={`border-0 py-2 ${msg.role === 'user' ? 'bg-light' : ''} ${msg.isError ? 'text-danger' : ''}`}
          >
            <div className="d-flex">
              {msg.role === 'model' && (
                <div className="me-2">
                  <div className="bg-success bg-opacity-10 rounded-circle p-1">
                    <FaRobot className="text-success" size={14} />
                  </div>
                </div>
              )}
              <div className="flex-grow-1">
                <div className="message-content small">
                  {msg.parts[0].text.split('\n').map((line, i) => (
                    <p key={i} className="mb-1">{line}</p>
                  ))}
                </div>
              </div>
            </div>
          </ListGroup.Item>
        ))}
        {loading && (
          <ListGroup.Item className="border-0 py-2">
            <div className="d-flex align-items-center">
              <Spinner animation="border" size="sm" variant="success" className="me-2" />
              <span className="text-muted small">AI is thinking...</span>
            </div>
          </ListGroup.Item>
        )}
        <div ref={messagesEndRef} />
      </ListGroup>

      <Card.Footer className="bg-white border-top p-2">
        <Form onSubmit={handleSubmit}>
          <div className="d-flex">
            <Form.Control
              type="text"
              placeholder="Ask a question..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={loading}
              className="border-end-0 rounded-end-0 py-1"
              size="sm"
            />
            <Button
              variant="primary"
              type="submit"
              className="rounded-start-0"
              disabled={loading || !newMessage.trim()}
              size="sm"
            >
              <FaPaperPlane />
            </Button>
          </div>
        </Form>
      </Card.Footer>
    </Card>
  );
};

export default GeminiChatWidget;
