import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, Row, Col, Card, <PERSON>, But<PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { FaSignInAlt, FaUserShield } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');

  const { login, isAuthenticated, error: authError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/';

  // Check if user is already logged in
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  // Set error from auth context
  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate inputs
    if (!email.trim()) {
      setError('Email is required');
      return;
    }

    if (!password) {
      setError('Password is required');
      return;
    }

    try {
      setError('');
      setSuccess('');
      setLoading(true);

      // Attempt to login
      const userData = await login(email, password);

      if (userData) {
        setSuccess('Login successful! Redirecting...');

        // Manual navigation after a short delay to show the success message
        setTimeout(() => {
          // Force a refresh to ensure the navbar updates correctly
          if (from === '/') {
            // If going to home page, force a full refresh
            window.location.href = '/';
          } else {
            // Otherwise use normal navigation
            navigate(from, { replace: true });
          }
        }, 1500);
      }
    } catch (err) {
      setError(err.error || 'Failed to sign in. Please check your credentials.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle demo login
  const handleDemoLogin = async (type) => {
    try {
      setError('');
      setSuccess('');
      setLoading(true);

      let userData;
      if (type === 'user') {
        userData = await login('<EMAIL>', 'password123');
      } else if (type === 'admin') {
        userData = await login('<EMAIL>', 'password123');
      }

      if (userData) {
        setSuccess('Demo login successful! Redirecting...');

        // Manual navigation after a short delay to show the success message
        setTimeout(() => {
          // Force a refresh to ensure the navbar updates correctly
          if (from === '/') {
            // If going to home page, force a full refresh
            window.location.href = '/';
          } else {
            // Otherwise use normal navigation
            navigate(from, { replace: true });
          }
        }, 1500);
      }
    } catch (err) {
      setError('Demo login failed. Please try again or use regular login.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      <Row className="justify-content-center">
        <Col md={8} lg={6}>
          <Card className="shadow-sm border-0">
            <Card.Body className="p-4 p-md-5">
              <h2 className="text-center mb-4">
                <FaSignInAlt className="me-2" />
                Log In to Urban Pulse
              </h2>

              {error && <Alert variant="danger">{error}</Alert>}
              {success && <Alert variant="success">{success}</Alert>}

              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3" controlId="email">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    disabled={loading}
                    required
                  />
                </Form.Group>

                <Form.Group className="mb-4" controlId="password">
                  <Form.Label>Password</Form.Label>
                  <Form.Control
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    disabled={loading}
                    required
                  />
                </Form.Group>

                <div className="d-grid mb-3">
                  <Button
                    variant="primary"
                    type="submit"
                    disabled={loading}
                    className="py-2"
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Signing in...
                      </>
                    ) : (
                      <>
                        <FaSignInAlt className="me-2" />
                        Sign In
                      </>
                    )}
                  </Button>
                </div>

                <div className="text-center mb-3">
                  <p className="text-muted">- or -</p>
                </div>

                <Row className="mb-3">
                  <Col>
                    <div className="d-grid">
                      <Button
                        variant="outline-primary"
                        onClick={() => handleDemoLogin('user')}
                        disabled={loading}
                      >
                        Demo User Login
                      </Button>
                    </div>
                  </Col>
                  <Col>
                    <div className="d-grid">
                      <Button
                        variant="outline-danger"
                        onClick={() => handleDemoLogin('admin')}
                        disabled={loading}
                      >
                        <FaUserShield className="me-1" />
                        Demo Admin Login
                      </Button>
                    </div>
                  </Col>
                </Row>
              </Form>

              <div className="text-center mt-4">
                <p className="mb-0">
                  Don't have an account? <Link to="/register" className="text-decoration-none">Sign up</Link>
                </p>
              </div>
            </Card.Body>
          </Card>

          <div className="text-center mt-3">
            <p className="text-muted small">
              For demo purposes: Use the demo buttons above or create your own account.
              <br />
              All features are fully functional with real-time updates.
            </p>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default LoginPage;
