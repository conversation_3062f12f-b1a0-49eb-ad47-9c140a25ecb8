// This script checks if the user is logged in and updates the navbar accordingly
(function() {
    console.log('Navbar auth check script loaded');
    
    // Function to check if user is logged in
    function isUserLoggedIn() {
        const token = localStorage.getItem('token');
        const userDataString = localStorage.getItem('user');
        
        if (token && userDataString) {
            try {
                // Parse user data
                const userData = JSON.parse(userDataString);
                console.log('Navbar auth check: User is logged in as', userData.name || 'User');
                return true;
            } catch (error) {
                console.error('Navbar auth check: Error parsing user data', error);
                return false;
            }
        }
        
        console.log('Navbar auth check: User is not logged in');
        return false;
    }
    
    // Function to inject the navbar update script
    function injectNavbarUpdateScript() {
        // Check if the script is already injected
        if (document.querySelector('script[src="/public/force-navbar-update.js"]')) {
            console.log('Navbar update script already injected');
            return;
        }
        
        console.log('Injecting navbar update script');
        const script = document.createElement('script');
        script.src = '/public/force-navbar-update.js';
        document.head.appendChild(script);
    }
    
    // Function to check if the navbar needs updating
    function checkNavbar() {
        const isLoggedIn = isUserLoggedIn();
        
        if (isLoggedIn) {
            // Check if the navbar shows login/signup buttons when it should show profile
            const loginLinks = document.querySelectorAll('a[href="/public/direct-login.html"], a[href="/login"], a[href*="login"]');
            const profileDropdown = document.getElementById('profile-dropdown');
            
            if (loginLinks.length > 0 && !profileDropdown) {
                console.log('Navbar needs updating, injecting script...');
                injectNavbarUpdateScript();
            } else {
                console.log('Navbar appears to be correct');
            }
        }
    }
    
    // Function to initialize the script
    function init() {
        // Check if URL contains auth_update parameter
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('auth_update')) {
            console.log('Auth update parameter detected, forcing navbar update');
            injectNavbarUpdateScript();
            return;
        }
        
        // Otherwise, check if navbar needs updating
        checkNavbar();
    }
    
    // Wait for the DOM to be fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // If DOM is already loaded, wait a short time to ensure React has rendered
        setTimeout(init, 500);
    }
})();
