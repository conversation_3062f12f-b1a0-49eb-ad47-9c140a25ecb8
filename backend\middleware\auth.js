const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Protect routes
exports.protect = async (req, res, next) => {
  let token;

  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // Set token from Bearer token in header
    token = req.headers.authorization.split(' ')[1];
  }

  // Make sure token exists
  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_secret_key_change_this_in_production');

    // Get user from MongoDB
    // Check if decoded has userId (from server.js) or id (from other parts)
    const userId = decoded.userId || decoded.id;
    req.user = await User.findById(userId);

    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }

    next();
  } catch (err) {
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }
};

// Grant access to specific roles
exports.authorize = (...roles) => {
  return (req, res, next) => {
    // If roles includes 'admin', check if the user is an admin
    if (roles.includes('admin') && !req.user.isAdmin) {
      return res.status(403).json({
        success: false,
        error: `Admin role required to access this route`
      });
    }

    // For other roles (if implemented in the future)
    if (!req.user.isAdmin && roles.length > 0 && !roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: `User role not authorized to access this route`
      });
    }

    next();
  };
};
