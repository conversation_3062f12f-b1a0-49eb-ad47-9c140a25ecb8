<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 800px;
            margin-top: 50px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Login Test</h1>
        <p>This page tests the login functionality directly.</p>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Login Form</h5>
            </div>
            <div class="card-body">
                <div id="alert-container"></div>
                
                <form id="login-form">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" placeholder="Enter your password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" id="login-button">Login</button>
                </form>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Test Accounts</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" id="test-user-button">Test User (<EMAIL>)</button>
                    <button class="btn btn-outline-primary" id="demo-user-button">Demo User (<EMAIL>)</button>
                    <button class="btn btn-outline-primary" id="admin-button">Admin (<EMAIL>)</button>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Response</h5>
            </div>
            <div class="card-body">
                <pre id="response-container">No response yet</pre>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Debug Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Local Storage</h6>
                    <pre id="local-storage-container">No data yet</pre>
                </div>
                
                <div class="mb-3">
                    <h6>Server Status</h6>
                    <div id="server-status">Checking...</div>
                    <button class="btn btn-sm btn-outline-secondary mt-2" id="check-server-button">Check Server</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const loginButton = document.getElementById('login-button');
            const testUserButton = document.getElementById('test-user-button');
            const demoUserButton = document.getElementById('demo-user-button');
            const adminButton = document.getElementById('admin-button');
            const alertContainer = document.getElementById('alert-container');
            const responseContainer = document.getElementById('response-container');
            const localStorageContainer = document.getElementById('local-storage-container');
            const serverStatus = document.getElementById('server-status');
            const checkServerButton = document.getElementById('check-server-button');
            
            // Function to show alert
            function showAlert(message, type) {
                alertContainer.innerHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            }
            
            // Function to update local storage display
            function updateLocalStorageDisplay() {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                
                let html = '';
                
                if (token) {
                    html += `<strong>Token:</strong> ${token.substring(0, 20)}...<br>`;
                } else {
                    html += '<strong>Token:</strong> Not found<br>';
                }
                
                if (user) {
                    try {
                        const userData = JSON.parse(user);
                        html += `<strong>User:</strong> ${JSON.stringify(userData, null, 2)}`;
                    } catch (error) {
                        html += `<strong>User:</strong> Error parsing user data: ${error.message}`;
                    }
                } else {
                    html += '<strong>User:</strong> Not found';
                }
                
                localStorageContainer.innerHTML = html;
            }
            
            // Function to check server status
            async function checkServerStatus() {
                serverStatus.innerHTML = 'Checking server status...';
                
                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'HEAD'
                    });
                    
                    serverStatus.innerHTML = `
                        <div class="alert alert-success mb-0">
                            Server is running. Status: ${response.status}
                        </div>
                    `;
                } catch (error) {
                    serverStatus.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            Server is not responding: ${error.message}
                        </div>
                    `;
                }
            }
            
            // Function to handle login
            async function handleLogin(email, password) {
                try {
                    // Show loading state
                    loginButton.disabled = true;
                    loginButton.innerHTML = `
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing in...
                    `;
                    
                    // Make API request
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });
                    
                    // Parse response
                    const data = await response.json();
                    
                    // Display response
                    responseContainer.textContent = JSON.stringify(data, null, 2);
                    
                    // Handle response
                    if (response.ok) {
                        showAlert('Login successful!', 'success');
                        
                        // Store token and user data
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        
                        // Update local storage display
                        updateLocalStorageDisplay();
                    } else {
                        showAlert(data.message || 'Login failed', 'danger');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    responseContainer.textContent = `Error: ${error.message}`;
                    showAlert(`Login failed: ${error.message}`, 'danger');
                } finally {
                    // Reset button
                    loginButton.disabled = false;
                    loginButton.innerHTML = 'Login';
                }
            }
            
            // Handle form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const password = passwordInput.value;
                
                handleLogin(email, password);
            });
            
            // Handle test user login
            testUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle demo user login
            demoUserButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle admin login
            adminButton.addEventListener('click', function() {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'password123';
                handleLogin('<EMAIL>', 'password123');
            });
            
            // Handle check server button
            checkServerButton.addEventListener('click', checkServerStatus);
            
            // Initial checks
            updateLocalStorageDisplay();
            checkServerStatus();
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
