<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Check</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 800px;
            margin-top: 50px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .btn-fix {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication Status Check</h1>
        <p>This page checks your current authentication status and helps diagnose navbar issues.</p>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Authentication Data</h5>
            </div>
            <div class="card-body">
                <div id="auth-status">Checking...</div>
                <pre id="auth-data"></pre>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Navbar Status</h5>
            </div>
            <div class="card-body">
                <div id="navbar-status">Checking...</div>
            </div>
        </div>
        
        <div class="d-flex gap-2">
            <button id="fix-navbar" class="btn btn-primary">Fix Navbar</button>
            <button id="go-home" class="btn btn-outline-primary">Go to Home Page</button>
            <button id="go-dashboard" class="btn btn-outline-success">Go to Dashboard</button>
            <button id="clear-auth" class="btn btn-outline-danger">Clear Auth Data</button>
        </div>
    </div>

    <script>
        // Function to check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('token');
            const userDataString = localStorage.getItem('user');
            const authStatus = document.getElementById('auth-status');
            const authData = document.getElementById('auth-data');
            
            if (token && userDataString) {
                try {
                    // Parse user data
                    const userData = JSON.parse(userDataString);
                    
                    // Display success message
                    authStatus.innerHTML = `
                        <div class="alert alert-success">
                            <strong>Logged in as:</strong> ${userData.name || 'User'} (${userData.email})
                        </div>
                    `;
                    
                    // Display auth data
                    authData.textContent = JSON.stringify({
                        token: token.substring(0, 20) + '...',
                        user: userData
                    }, null, 2);
                    
                    return { isLoggedIn: true, userData };
                } catch (error) {
                    // Display error message
                    authStatus.innerHTML = `
                        <div class="alert alert-warning">
                            <strong>Error parsing user data:</strong> ${error.message}
                        </div>
                    `;
                    authData.textContent = 'Error parsing user data';
                    return { isLoggedIn: false, error };
                }
            } else {
                // Display not logged in message
                authStatus.innerHTML = `
                    <div class="alert alert-info">
                        Not logged in. No authentication data found in localStorage.
                    </div>
                `;
                authData.textContent = 'No authentication data found';
                return { isLoggedIn: false };
            }
        }
        
        // Function to check navbar status
        function checkNavbarStatus() {
            const navbarStatus = document.getElementById('navbar-status');
            
            // We'll check this by opening the home page in a new window and checking its navbar
            navbarStatus.innerHTML = `
                <div class="alert alert-info">
                    To check the navbar status, go to the home page using the button below.
                </div>
            `;
        }
        
        // Function to fix navbar
        function fixNavbar() {
            // Create a script element to inject the force-navbar-update.js script
            const script = document.createElement('script');
            script.src = '/public/force-navbar-update.js';
            document.head.appendChild(script);
            
            // Show message
            alert('Navbar fix script has been injected. Now go to the home page to see if it worked.');
        }
        
        // Function to clear auth data
        function clearAuthData() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            // Refresh the page
            window.location.reload();
        }
        
        // Initialize the page
        function init() {
            // Check auth status
            checkAuthStatus();
            
            // Check navbar status
            checkNavbarStatus();
            
            // Add event listeners
            document.getElementById('fix-navbar').addEventListener('click', fixNavbar);
            document.getElementById('go-home').addEventListener('click', () => {
                window.location.href = '/?auth_update=' + new Date().getTime();
            });
            document.getElementById('go-dashboard').addEventListener('click', () => {
                window.location.href = '/dashboard';
            });
            document.getElementById('clear-auth').addEventListener('click', clearAuthData);
        }
        
        // Run initialization
        document.addEventListener('DOMContentLoaded', init);
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
