import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, Form, Button, Modal, Spinner } from 'react-bootstrap';
import { FaExclamationTriangle, FaMapMarkerAlt, FaFilter, FaSearch, Fa<PERSON>ist, FaMap } from 'react-icons/fa';
import IssuesMap from '../components/issues/IssuesMap';
import IssueCard from '../components/issues/IssueCard';
import issueService from '../utils/issueService';

const IssuesMapPage = () => {
  const [issues, setIssues] = useState([]);
  const [filteredIssues, setFilteredIssues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [viewMode, setViewMode] = useState('map'); // 'map' or 'list'
  const [showModal, setShowModal] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState(null);

  // Fetch all issues
  useEffect(() => {
    const fetchIssues = async () => {
      try {
        setLoading(true);
        // Use the issueService to fetch all issues
        const result = await issueService.getIssues({
          category: categoryFilter,
          status: statusFilter
        });
        setIssues(result.data || []);
        setFilteredIssues(result.data || []);
      } catch (err) {
        console.error('Error fetching issues:', err);
        setError('Failed to load issues. Please try again later.');

        // Use realistic data if API fails
        const mockIssues = [
          {
            id: 1,
            title: 'Severe Pothole on Riverside Drive',
            category: 'Roads',
            status: 'Open',
            description: 'Deep pothole approximately 2 feet wide and 6 inches deep causing traffic hazards and vehicle damage. Located in the right lane heading north, just before the intersection with Maple Avenue.',
            location: '1420 Riverside Drive',
            coordinates: { lat: 51.505, lng: -0.09 },
            date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            photo: 'https://images.unsplash.com/photo-1515162816999-a0c47dc192f7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          },
          {
            id: 2,
            title: 'Non-functioning Streetlight at Oak & Pine',
            category: 'Lighting',
            status: 'In Progress',
            description: 'Streetlight ID #SL-2467 has been completely out for 5 nights. This is creating a safety hazard for pedestrians in the evening as this is a busy intersection with a crosswalk. Maintenance crew has been dispatched.',
            location: 'Intersection of Oak Avenue and Pine Street',
            coordinates: { lat: 51.51, lng: -0.1 },
            date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
            photo: 'https://images.unsplash.com/photo-1551462147-ff29053bfc14?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          },
          {
            id: 3,
            title: 'Overflowing Public Waste Bin',
            category: 'Waste',
            status: 'Resolved',
            description: 'Public waste bin #WB-328 near the east entrance of Central City Park was overflowing for 2 days, causing litter to spread to surrounding areas and creating unpleasant odors. Waste management team has now emptied the bin and cleaned the surrounding area.',
            location: 'Central City Park - East Entrance',
            coordinates: { lat: 51.515, lng: -0.08 },
            date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            photo: 'https://images.unsplash.com/photo-1605600659873-d808a13e4d2a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          },
          {
            id: 4,
            title: 'Damaged Playground Equipment',
            category: 'Parks',
            status: 'In Progress',
            description: 'The main slide in the Westside Community Park children\'s playground has a large crack in the middle section, creating a safety hazard for children. The area has been temporarily cordoned off with caution tape. Parks department has ordered replacement parts.',
            location: 'Westside Community Park',
            coordinates: { lat: 51.508, lng: -0.11 },
            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            photo: 'https://images.unsplash.com/photo-1579704025097-9c5e1a7c3326?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          },
          {
            id: 5,
            title: 'Graffiti on Public Library Wall',
            category: 'Graffiti',
            status: 'Open',
            description: 'Extensive graffiti has appeared on the north-facing exterior wall of the Downtown Public Library. The affected area is approximately 8 feet wide by 5 feet high. The graffiti contains inappropriate language and imagery that should be removed promptly.',
            location: '350 Library Avenue',
            coordinates: { lat: 51.502, lng: -0.095 },
            date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            photo: 'https://images.unsplash.com/photo-1572446184214-84a7c4a1ac70?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&q=80'
          }
        ];

        setIssues(mockIssues);
        setFilteredIssues(mockIssues);
      } finally {
        setLoading(false);
      }
    };

    fetchIssues();
  }, [categoryFilter, statusFilter]);

  // Filter issues when search term or filters change
  useEffect(() => {
    let result = [...issues];

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(issue =>
        issue.title.toLowerCase().includes(term) ||
        issue.description?.toLowerCase().includes(term) ||
        issue.location.toLowerCase().includes(term)
      );
    }

    // Apply category filter
    if (categoryFilter) {
      result = result.filter(issue => issue.category === categoryFilter);
    }

    // Apply status filter
    if (statusFilter) {
      result = result.filter(issue => issue.status === statusFilter);
    }

    setFilteredIssues(result);
  }, [issues, searchTerm, categoryFilter, statusFilter]);

  // Handle issue card click
  const handleIssueClick = (issue) => {
    setSelectedIssue(issue);
    setShowModal(true);
  };

  // Get unique categories from issues
  const getCategories = () => {
    const categories = new Set(issues.map(issue => issue.category));
    return ['', ...Array.from(categories)];
  };

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Urban Issues Map</h1>
          <p className="lead">
            View and track reported issues across the city.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-danger bg-opacity-10 p-3 rounded">
            <FaExclamationTriangle className="text-danger fs-1" />
          </div>
        </Col>
      </Row>

      {/* Filters and Search */}
      <Card className="border-0 shadow-sm mb-4">
        <Card.Body className="p-3">
          <Row className="g-3">
            <Col md={4}>
              <Form.Group>
                <div className="input-group">
                  <span className="input-group-text bg-white">
                    <FaSearch className="text-muted" />
                  </span>
                  <Form.Control
                    type="text"
                    placeholder="Search issues..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group>
                <div className="input-group">
                  <span className="input-group-text bg-white">
                    <FaFilter className="text-muted" />
                  </span>
                  <Form.Select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                  >
                    <option value="">All Categories</option>
                    {getCategories().filter(c => c).map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </Form.Select>
                </div>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group>
                <div className="input-group">
                  <span className="input-group-text bg-white">
                    <FaFilter className="text-muted" />
                  </span>
                  <Form.Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="">All Statuses</option>
                    <option value="Open">Open</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Resolved">Resolved</option>
                  </Form.Select>
                </div>
              </Form.Group>
            </Col>
            <Col md={2}>
              <div className="btn-group w-100">
                <Button
                  variant={viewMode === 'map' ? 'primary' : 'outline-primary'}
                  onClick={() => setViewMode('map')}
                >
                  <FaMap className="me-1" /> Map
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'primary' : 'outline-primary'}
                  onClick={() => setViewMode('list')}
                >
                  <FaList className="me-1" /> List
                </Button>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Results Count */}
      <div className="mb-3 text-muted">
        Showing {filteredIssues.length} of {issues.length} issues
      </div>

      {/* Map View */}
      {viewMode === 'map' && (
        <div className="mb-4" style={{ height: '600px' }}>
          <IssuesMap height="600px" />
        </div>
      )}

      {/* List View */}
      {viewMode === 'list' && (
        <Row xs={1} md={2} lg={3} className="g-4 mb-4">
          {loading ? (
            <Col className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Loading issues...</p>
            </Col>
          ) : filteredIssues.length === 0 ? (
            <Col className="text-center py-5">
              <FaExclamationTriangle className="text-muted mb-3" size={32} />
              <p className="text-muted">No issues found matching your filters.</p>
            </Col>
          ) : (
            filteredIssues.map(issue => (
              <Col key={issue.id || issue._id}>
                <IssueCard issue={issue} onClick={handleIssueClick} />
              </Col>
            ))
          )}
        </Row>
      )}

      {/* Issue Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Issue Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedIssue && (
            <Row>
              <Col md={6}>
                {selectedIssue.photo && (
                  <img
                    src={selectedIssue.photo}
                    alt={selectedIssue.title}
                    className="img-fluid rounded mb-3"
                    style={{ maxHeight: '300px', width: '100%', objectFit: 'cover' }}
                  />
                )}
              </Col>
              <Col md={6}>
                <h4>{selectedIssue.title}</h4>
                <div className="mb-3">
                  {(() => {
                    switch (selectedIssue.status) {
                      case 'Open':
                        return <span className="badge bg-danger">Open</span>;
                      case 'In Progress':
                        return <span className="badge bg-warning">In Progress</span>;
                      case 'Resolved':
                        return <span className="badge bg-success">Resolved</span>;
                      default:
                        return <span className="badge bg-secondary">{selectedIssue.status}</span>;
                    }
                  })()}
                </div>
                <p className="mb-3">
                  <strong>Category:</strong> {selectedIssue.category}
                </p>
                <p className="mb-3">
                  <strong>Location:</strong> {selectedIssue.location}
                </p>
                <p className="mb-3">
                  <strong>Reported on:</strong> {new Date(selectedIssue.createdAt || selectedIssue.date).toLocaleDateString()}
                </p>
                <p className="mb-3">
                  <strong>Description:</strong><br />
                  {selectedIssue.description || 'No description provided.'}
                </p>
                {selectedIssue.coordinates && (
                  <div style={{ height: '200px', marginBottom: '10px' }}>
                    <IssuesMap
                      height="200px"
                      center={[selectedIssue.coordinates.lat, selectedIssue.coordinates.lng]}
                      zoom={15}
                    />
                  </div>
                )}
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default IssuesMapPage;
