# Voice Assistant Component

This component implements a minimalist voice assistant for the Urban Pulse application with a dropdown menu interface and the following features:
- Single voice assistant icon that expands to show options
- Voice search functionality
- Question answering with spoken responses
- Text-to-speech for selected text
- Clean and responsive user interface

## Features

### Dropdown Menu Interface
- Initially, only a voice assistant icon is visible on the page
- Clicking the icon reveals a dropdown menu with options
- Options include: Voice Search, Ask Questions, Read Selected Text, and Stop Speaking

### Web Search Integration
- Select "Voice Search" from the dropdown menu
- Use voice commands to search the web
- Say "Search for..." followed by your query
- Mock search results are displayed (can be replaced with a real search API)

### Question Answering
- Select "Ask Questions" from the dropdown menu
- Ask questions using voice commands
- Say "Tell me about...", "Answer...", "What is...", or "How to..." followed by your question
- Receive spoken answers to your questions
- Mock answers are provided (can be replaced with a real AI API)

### Text-to-Speech (TTS)
- Select text on the webpage
- Choose "Read Selected Text" from the dropdown menu
- The assistant will read the selected text aloud
- Choose "Stop Speaking" from the dropdown to cancel any ongoing speech

## Usage

Import and use the component in any page:

```jsx
import VoiceAssistant from '../components/voice-assistance';

const YourPage = () => {
  return (
    <>
      <VoiceAssistant />
      {/* Your page content */}
    </>
  );
};
```

## Browser Compatibility

This voice assistant uses the Web Speech API, which has varying levels of support across browsers:
- Chrome: Full support
- Edge: Good support
- Firefox: Partial support
- Safari: Limited support

For the best experience, use Google Chrome.

## Customization

### Voice Selection
- The assistant attempts to use a female voice by default
- You can modify the voice selection logic in the `speak` function in `VoiceAssistant.jsx`

### Styling
- All styling is contained in `VoiceAssistant.css`
- The voice assistant icon color, size, and position can be customized
- Dropdown menu appearance and animations can be modified
- Interface colors and layouts can be adjusted as needed

### Additional Commands
- You can add more voice commands by extending the logic in the `recognition.onresult` event handler
- You can add more options to the dropdown menu by adding new items to the JSX and corresponding event handlers

## Integration with AI APIs

The current implementation uses mock answers and search results. To integrate with real APIs:

1. For search functionality, replace the `performSearch` function with a call to a real search API
2. For question answering, replace the `answerQuestion` function with a call to an AI API like OpenAI or Google Gemini

## Troubleshooting

- **Voice Recognition Not Working**: Ensure your browser has permission to use the microphone
- **Text-to-Speech Not Working**: Some browsers have limitations with the Speech Synthesis API
- **HTTPS Requirement**: Some browsers require HTTPS for the Web Speech API to work properly
