# UrbanPulse Backend

This is the backend API for the UrbanPulse smart urban living platform. It's built with Node.js, Express, and MongoDB.

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- MongoDB (local or Atlas)

### Installation

1. Install dependencies:
```
npm install
```

2. Create a `.env` file based on `.env.example`:
```
cp .env.example .env
```

3. Edit the `.env` file with your configuration

4. Start the development server:
```
npm run dev
```

5. The API will be available at `http://localhost:5000`

## Available Scripts

- `npm run dev`: Start the development server with hot reloading
- `npm start`: Start the server
- `npm test`: Run tests

## Project Structure

- `controllers/`: Request handlers
- `models/`: Database models
- `routes/`: API routes
- `middleware/`: Custom middleware
- `utils/`: Utility functions
- `config/`: Configuration files

## API Endpoints

The API will provide endpoints for all seven components of the UrbanPulse platform:

1. `/api/users`: User authentication and profiles
2. `/api/issues`: Urban issue reporting
3. `/api/transport`: Public transport feedback
4. `/api/challenges`: Sustainable living challenges
5. `/api/reviews`: Public service reviews
6. `/api/resources`: Resource sharing
7. `/api/spaces`: Green space mapping
8. `/api/courses`: Urban learning hub

## Dependencies

- Express
- Mongoose
- JWT
- bcrypt
- Multer
- Validator
- Cors
- Dotenv

## Testing

Tests are written using Jest and Supertest. Run tests with:

```
npm test
```
