import api from '../utils/api';

// Get all courses
export const getCourses = async (params) => {
  try {
    const res = await api.get('/courses', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch courses');
  }
};

// Get course by ID
export const getCourseById = async (id) => {
  try {
    const res = await api.get(`/courses/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch course');
  }
};

// Create new course
export const createCourse = async (courseData) => {
  try {
    const res = await api.post('/courses', courseData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create course');
  }
};

// Update course
export const updateCourse = async (id, courseData) => {
  try {
    const res = await api.put(`/courses/${id}`, courseData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update course');
  }
};

// Delete course
export const deleteCourse = async (id) => {
  try {
    const res = await api.delete(`/courses/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete course');
  }
};

// Enroll in course
export const enrollInCourse = async (id) => {
  try {
    const res = await api.post(`/courses/${id}/enroll`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to enroll in course');
  }
};

// Unenroll from course
export const unenrollFromCourse = async (id) => {
  try {
    const res = await api.delete(`/courses/${id}/enroll`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to unenroll from course');
  }
};

// Complete lesson
export const completeLesson = async (courseId, lessonId) => {
  try {
    const res = await api.post(`/courses/${courseId}/lessons/${lessonId}/complete`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to complete lesson');
  }
};

// Rate course
export const rateCourse = async (courseId, rating, review) => {
  try {
    const res = await api.post(`/courses/${courseId}/ratings`, { rating, review });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to rate course');
  }
};

// Get user's enrolled courses
export const getEnrolledCourses = async () => {
  try {
    const res = await api.get('/courses/enrolled');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch enrolled courses');
  }
};

// Get course progress
export const getCourseProgress = async (courseId) => {
  try {
    const res = await api.get(`/courses/${courseId}/progress`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch course progress');
  }
};
