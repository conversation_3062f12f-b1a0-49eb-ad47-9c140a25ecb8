const mongoose = require('mongoose');

const ServiceReviewSchema = new mongoose.Schema({
  serviceId: {
    type: mongoose.Schema.ObjectId,
    ref: 'Service',
    required: true
  },
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  text: {
    type: String,
    required: [true, 'Please add review text'],
    maxlength: [500, 'Review cannot be more than 500 characters']
  },
  rating: {
    type: Number,
    required: [true, 'Please add a rating'],
    min: 1,
    max: 5
  },
  helpful: [
    {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  ],
  comments: [
    {
      text: {
        type: String,
        required: true
      },
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Virtual for helpful count
ServiceReviewSchema.virtual('helpfulCount').get(function() {
  return this.helpful.length;
});

// Virtual for comment count
ServiceReviewSchema.virtual('commentCount').get(function() {
  return this.comments.length;
});

// Enable virtuals
ServiceReviewSchema.set('toJSON', { virtuals: true });
ServiceReviewSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('ServiceReview', ServiceReviewSchema);
