import React, { useState, useEffect } from 'react';
import { Card, Nav, Row, Col } from 'react-bootstrap';
import ChallengeCard from './ChallengeCard';

const MONTHS = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

const MonthlyCalendar = ({ 
  challenges, 
  badges, 
  onAcceptChallenge, 
  onCompleteChallenge, 
  onMonthChange,
  initialMonth = "January" 
}) => {
  const [selectedMonth, setSelectedMonth] = useState(initialMonth);
  
  // Filter challenges by selected month
  const filteredChallenges = challenges.filter(
    (challenge) => challenge.month === selectedMonth
  );
  
  // Handle month change
  const handleMonthChange = (month) => {
    setSelectedMonth(month);
    if (onMonthChange) {
      onMonthChange(month);
    }
  };
  
  useEffect(() => {
    if (initialMonth) {
      setSelectedMonth(initialMonth);
    }
  }, [initialMonth]);

  return (
    <Card className="border-0 shadow-sm mb-4">
      <Card.Body className="p-4">
        <h2 className="mb-4">Sustainability Challenge Calendar</h2>
        
        <Nav variant="pills" className="mb-4 flex-nowrap" style={{ overflowX: 'auto' }}>
          {MONTHS.map((month) => (
            <Nav.Item key={month}>
              <Nav.Link 
                active={selectedMonth === month}
                onClick={() => handleMonthChange(month)}
                className="mx-1"
              >
                {month}
              </Nav.Link>
            </Nav.Item>
          ))}
        </Nav>
        
        <div className="mt-4">
          <h3 className="h5 mb-3">{selectedMonth} Challenges</h3>
          
          {filteredChallenges.length === 0 ? (
            <div className="bg-light p-4 text-center rounded">
              <p className="text-muted mb-0">No challenges available for this month</p>
            </div>
          ) : (
            <Row xs={1} md={2} className="g-4">
              {filteredChallenges.map(challenge => (
                <Col key={challenge.id}>
                  <ChallengeCard
                    challenge={challenge}
                    onAccept={onAcceptChallenge}
                    onComplete={onCompleteChallenge}
                    showCalendar={challenge.is_accepted && !challenge.completed}
                  />
                </Col>
              ))}
            </Row>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default MonthlyCalendar;
