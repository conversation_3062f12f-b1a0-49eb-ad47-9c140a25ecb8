import api from '../utils/api';

// Get all resources
export const getResources = async (params) => {
  try {
    const res = await api.get('/resources', { params });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch resources');
  }
};

// Get resource by ID
export const getResourceById = async (id) => {
  try {
    const res = await api.get(`/resources/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch resource');
  }
};

// Create new resource
export const createResource = async (resourceData) => {
  try {
    const res = await api.post('/resources', resourceData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to create resource');
  }
};

// Update resource
export const updateResource = async (id, resourceData) => {
  try {
    const res = await api.put(`/resources/${id}`, resourceData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to update resource');
  }
};

// Delete resource
export const deleteResource = async (id) => {
  try {
    const res = await api.delete(`/resources/${id}`);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to delete resource');
  }
};

// Request to borrow a resource
export const requestResource = async (id, requestData) => {
  try {
    const res = await api.post(`/resources/${id}/request`, requestData);
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to request resource');
  }
};

// Respond to a resource request
export const respondToRequest = async (resourceId, requestId, status) => {
  try {
    const res = await api.put(`/resources/${resourceId}/request/${requestId}`, { status });
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to respond to request');
  }
};

// Get user's shared resources
export const getMyResources = async () => {
  try {
    const res = await api.get('/resources/my-resources');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch your resources');
  }
};

// Get user's borrowed resources
export const getBorrowedResources = async () => {
  try {
    const res = await api.get('/resources/borrowed');
    return res.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Failed to fetch borrowed resources');
  }
};
