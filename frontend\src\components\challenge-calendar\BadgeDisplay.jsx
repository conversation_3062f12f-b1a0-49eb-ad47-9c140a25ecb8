import React from 'react';
import { Card, Badge } from 'react-bootstrap';
import { FaAward, FaLock, FaCheckCircle } from 'react-icons/fa';

const BadgeDisplay = ({ badge, relatedChallenges }) => {
  // Calculate completion percentage for related challenges
  const totalChallenges = relatedChallenges?.length || 0;
  const completedChallenges = relatedChallenges?.filter(c => c.completed)?.length || 0;
  const completionPercentage = totalChallenges > 0 
    ? Math.round((completedChallenges / totalChallenges) * 100) 
    : 0;

  return (
    <Card className={`h-100 ${badge.earned ? 'border-success' : 'border-secondary'}`}>
      <Card.Body className="text-center">
        <div className="mb-3">
          {badge.earned ? (
            <div className="mx-auto d-flex align-items-center justify-content-center bg-success bg-opacity-10 rounded-circle" style={{ width: '80px', height: '80px' }}>
              <FaAward className="text-success" size={40} />
            </div>
          ) : (
            <div className="mx-auto d-flex align-items-center justify-content-center bg-secondary bg-opacity-10 rounded-circle" style={{ width: '80px', height: '80px' }}>
              <FaLock className="text-secondary" size={30} />
            </div>
          )}
        </div>
        
        <Card.Title className={badge.earned ? 'text-success' : 'text-secondary'}>
          {badge.name}
        </Card.Title>
        
        <Card.Text className="text-muted small">
          {badge.description}
        </Card.Text>
        
        <Badge bg={badge.earned ? 'success' : 'secondary'} className="mb-2">
          {badge.earned ? 'Earned' : 'Locked'}
        </Badge>
        
        {relatedChallenges && relatedChallenges.length > 0 && (
          <div className="mt-3 pt-3 border-top">
            <small className="text-muted d-block mb-2">Challenge Progress</small>
            <div className="progress mb-2" style={{ height: '8px' }}>
              <div 
                className="progress-bar bg-success" 
                role="progressbar" 
                style={{ width: `${completionPercentage}%` }}
                aria-valuenow={completionPercentage} 
                aria-valuemin="0" 
                aria-valuemax="100"
              ></div>
            </div>
            <small className="text-muted">
              {completedChallenges}/{totalChallenges} challenges completed
            </small>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default BadgeDisplay;
