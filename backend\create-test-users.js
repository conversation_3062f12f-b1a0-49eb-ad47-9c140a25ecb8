const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Import User model
const User = require('./models/User');

// Connect to MongoDB
console.log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');
  
  // Function to create or update a user
  async function createOrUpdateUser(email, name, password, isAdmin = false) {
    try {
      // Check if user exists
      let user = await User.findOne({ email });
      
      if (user) {
        console.log(`User ${email} already exists, updating...`);
        
        // Hash the password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);
        
        // Update user
        user.name = name;
        user.password = hashedPassword;
        user.isAdmin = isAdmin;
        
        await user.save();
        console.log(`User ${email} updated successfully`);
      } else {
        console.log(`User ${email} does not exist, creating...`);
        
        // Create new user
        user = new User({
          name,
          email,
          password, // Will be hashed by the pre-save hook
          isAdmin
        });
        
        await user.save();
        console.log(`User ${email} created successfully`);
      }
      
      // Verify password
      const updatedUser = await User.findOne({ email }).select('+password');
      const isMatch = await bcrypt.compare(password, updatedUser.password);
      console.log(`Password verification for ${email}: ${isMatch ? 'Success' : 'Failed'}`);
      
      return user;
    } catch (error) {
      console.error(`Error creating/updating user ${email}:`, error.message);
      throw error;
    }
  }
  
  try {
    // Create or update test user
    await createOrUpdateUser('<EMAIL>', 'Test User', 'password123');
    
    // Create or update demo user
    await createOrUpdateUser('<EMAIL>', 'Demo User', 'password123');
    
    // Create or update admin user
    await createOrUpdateUser('<EMAIL>', 'Admin User', 'password123', true);
    
    console.log('\nAll users have been created or updated successfully');
    
    // List all users
    const users = await User.find().select('-password');
    console.log('\nUsers in database:');
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}) ${user.isAdmin ? '[Admin]' : ''}`);
    });
  } catch (error) {
    console.error('Error fixing users:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\nConnection closed');
    process.exit(0);
  }
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});
