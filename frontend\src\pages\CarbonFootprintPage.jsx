import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Badge, Tab, Nav, Alert } from 'react-bootstrap';
import { FaLeaf, FaCar, FaLightbulb, FaUtensils, FaShoppingBag } from 'react-icons/fa';

const CarbonFootprintPage = () => {
  // State for activity tracking
  const [activeTab, setActiveTab] = useState('travel');
  const [activities, setActivities] = useState([]);
  const [totalFootprint, setTotalFootprint] = useState(0);
  
  // Travel activity state
  const [travelMode, setTravelMode] = useState('');
  const [distance, setDistance] = useState('');
  const [travelFootprint, setTravelFootprint] = useState(null);
  
  // Electricity activity state
  const [applianceType, setApplianceType] = useState('');
  const [duration, setDuration] = useState('');
  const [electricityFootprint, setElectricityFootprint] = useState(null);
  
  // Food activity state
  const [mealType, setMealType] = useState('');
  const [meatPortion, setMeatPortion] = useState(30);
  const [locallySourced, setLocallySourced] = useState(false);
  const [foodFootprint, setFoodFootprint] = useState(null);
  
  // Shopping activity state
  const [itemType, setItemType] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [secondHand, setSecondHand] = useState(false);
  const [sustainableBrand, setSustainableBrand] = useState(false);
  const [shoppingFootprint, setShoppingFootprint] = useState(null);

  // Calculate total footprint whenever activities change
  useEffect(() => {
    const total = activities.reduce((sum, activity) => sum + activity.carbonFootprint, 0);
    setTotalFootprint(total);
  }, [activities]);

  // Travel footprint calculation
  const calculateTravelFootprint = () => {
    // Carbon footprint calculation factors (kg CO2 per km)
    const factors = {
      car: 0.12,
      bus: 0.05,
      train: 0.03,
      bike: 0,
      walk: 0,
      plane: 0.25
    };

    const footprint = distance * factors[travelMode];
    return parseFloat(footprint.toFixed(2));
  };

  // Electricity footprint calculation
  const calculateElectricityFootprint = () => {
    // Carbon footprint calculation factors (kg CO2 per hour)
    const factors = {
      ac: 2.0,
      lights: 0.1,
      tv: 0.15,
      refrigerator: 0.5,
      other: 0.3
    };

    const hours = duration / 60; // Convert minutes to hours
    const footprint = hours * factors[applianceType];
    return parseFloat(footprint.toFixed(2));
  };

  // Food footprint calculation
  const calculateFoodFootprint = () => {
    // Carbon footprint calculation factors (kg CO2 per meal)
    const baseFactor = {
      vegetarian: 0.5,
      vegan: 0.3,
      meat: 3.0,
      fish: 1.5
    };

    // Adjust based on meat portion for meat meals
    let footprint = baseFactor[mealType];
    if (mealType === 'meat') {
      // Adjust based on meat portion (30% is the default)
      footprint = footprint * (meatPortion / 30);
    }

    // Reduce by 20% if locally sourced
    if (locallySourced) {
      footprint = footprint * 0.8;
    }

    return parseFloat(footprint.toFixed(2));
  };

  // Shopping footprint calculation
  const calculateShoppingFootprint = () => {
    // Carbon footprint calculation factors (kg CO2 per item)
    const baseFactor = {
      clothing: 10,
      electronics: 30,
      groceries: 2,
      other: 5
    };

    // Calculate base footprint
    let footprint = baseFactor[itemType] * parseInt(quantity);

    // Reduce by 80% if second hand
    if (secondHand) {
      footprint = footprint * 0.2;
    }

    // Reduce by 30% if from a sustainable brand
    if (sustainableBrand && !secondHand) {
      footprint = footprint * 0.7;
    }

    return parseFloat(footprint.toFixed(2));
  };

  // Handle travel form submission
  const handleTravelSubmit = (e) => {
    e.preventDefault();
    const footprint = calculateTravelFootprint();
    setTravelFootprint(footprint);
    
    // Add to activities
    setActivities([...activities, {
      id: Date.now(),
      type: 'travel',
      mode: travelMode,
      distance: parseFloat(distance),
      carbonFootprint: footprint,
      date: new Date()
    }]);
    
    // Reset form
    setTravelMode('');
    setDistance('');
  };

  // Handle electricity form submission
  const handleElectricitySubmit = (e) => {
    e.preventDefault();
    const footprint = calculateElectricityFootprint();
    setElectricityFootprint(footprint);
    
    // Add to activities
    setActivities([...activities, {
      id: Date.now(),
      type: 'electricity',
      appliance: applianceType,
      duration: parseFloat(duration),
      carbonFootprint: footprint,
      date: new Date()
    }]);
    
    // Reset form
    setApplianceType('');
    setDuration('');
  };

  // Handle food form submission
  const handleFoodSubmit = (e) => {
    e.preventDefault();
    const footprint = calculateFoodFootprint();
    setFoodFootprint(footprint);
    
    // Add to activities
    setActivities([...activities, {
      id: Date.now(),
      type: 'food',
      mealType: mealType,
      meatPortion: mealType === 'meat' ? meatPortion : null,
      locallySourced: locallySourced,
      carbonFootprint: footprint,
      date: new Date()
    }]);
    
    // Reset form
    setMealType('');
    setMeatPortion(30);
    setLocallySourced(false);
  };

  // Handle shopping form submission
  const handleShoppingSubmit = (e) => {
    e.preventDefault();
    const footprint = calculateShoppingFootprint();
    setShoppingFootprint(footprint);
    
    // Add to activities
    setActivities([...activities, {
      id: Date.now(),
      type: 'shopping',
      itemType: itemType,
      quantity: parseInt(quantity),
      secondHand: secondHand,
      sustainableBrand: sustainableBrand,
      carbonFootprint: footprint,
      date: new Date()
    }]);
    
    // Reset form
    setItemType('');
    setQuantity('1');
    setSecondHand(false);
    setSustainableBrand(false);
  };

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Carbon Footprint</h1>
          <p className="lead">
            Track your daily activities and calculate your carbon footprint to reduce your environmental impact.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-success bg-opacity-10 p-3 rounded">
            <FaLeaf className="text-success fs-1" />
          </div>
        </Col>
      </Row>

      {/* Carbon Footprint Summary */}
      <Card className="border-0 shadow-sm mb-4">
        <Card.Body className="p-4">
          <Row>
            <Col md={8}>
              <h4 className="mb-3">Your Carbon Footprint</h4>
              <p>
                Your total carbon footprint from tracked activities is:
              </p>
              <h2 className="text-success mb-3">{totalFootprint.toFixed(2)} kg CO₂</h2>
              <p className="text-muted">
                Track more activities to get a more accurate picture of your carbon footprint.
              </p>
            </Col>
            <Col md={4} className="d-flex align-items-center justify-content-center">
              <div className="bg-success bg-opacity-10 p-4 rounded-circle">
                <FaLeaf className="text-success" style={{ fontSize: '4rem' }} />
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Activity Tracking Tabs */}
      <Card className="border-0 shadow-sm mb-4">
        <Card.Body className="p-0">
          <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k)}>
            <Nav variant="tabs" className="border-0">
              <Nav.Item>
                <Nav.Link eventKey="travel" className="px-4 py-3">
                  <FaCar className="me-2" />
                  Travel
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="electricity" className="px-4 py-3">
                  <FaLightbulb className="me-2" />
                  Electricity
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="food" className="px-4 py-3">
                  <FaUtensils className="me-2" />
                  Food
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="shopping" className="px-4 py-3">
                  <FaShoppingBag className="me-2" />
                  Shopping
                </Nav.Link>
              </Nav.Item>
            </Nav>
            <Tab.Content className="p-4">
              {/* Travel Tab */}
              <Tab.Pane eventKey="travel">
                <h5 className="mb-4">Log Your Travel</h5>
                <Form onSubmit={handleTravelSubmit}>
                  <Row className="mb-3">
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Travel Mode</Form.Label>
                        <Form.Select 
                          value={travelMode} 
                          onChange={(e) => setTravelMode(e.target.value)}
                          required
                        >
                          <option value="">Select travel mode</option>
                          <option value="car">Car</option>
                          <option value="bus">Bus</option>
                          <option value="train">Train</option>
                          <option value="bike">Bicycle</option>
                          <option value="walk">Walking</option>
                          <option value="plane">Airplane</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Distance (km)</Form.Label>
                        <Form.Control 
                          type="number" 
                          min="0.1" 
                          step="0.1"
                          value={distance} 
                          onChange={(e) => setDistance(e.target.value)}
                          required
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Button variant="success" type="submit" className="mb-3">
                    Calculate & Log
                  </Button>
                  
                  {travelFootprint !== null && (
                    <Alert variant="success" className="mt-3">
                      <Alert.Heading>Carbon Footprint Result</Alert.Heading>
                      <p>
                        Your {travelMode} journey of {distance} km produces <strong>{travelFootprint} kg</strong> of CO₂.
                      </p>
                    </Alert>
                  )}
                </Form>
              </Tab.Pane>
              
              {/* Electricity Tab */}
              <Tab.Pane eventKey="electricity">
                <h5 className="mb-4">Log Your Electricity Usage</h5>
                <Form onSubmit={handleElectricitySubmit}>
                  <Row className="mb-3">
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Appliance Type</Form.Label>
                        <Form.Select 
                          value={applianceType} 
                          onChange={(e) => setApplianceType(e.target.value)}
                          required
                        >
                          <option value="">Select appliance</option>
                          <option value="ac">Air Conditioner</option>
                          <option value="lights">Lights</option>
                          <option value="tv">Television</option>
                          <option value="refrigerator">Refrigerator</option>
                          <option value="other">Other</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Duration (minutes)</Form.Label>
                        <Form.Control 
                          type="number" 
                          min="1" 
                          step="1"
                          value={duration} 
                          onChange={(e) => setDuration(e.target.value)}
                          required
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Button variant="success" type="submit" className="mb-3">
                    Calculate & Log
                  </Button>
                  
                  {electricityFootprint !== null && (
                    <Alert variant="success" className="mt-3">
                      <Alert.Heading>Carbon Footprint Result</Alert.Heading>
                      <p>
                        Using your {applianceType} for {duration} minutes produces <strong>{electricityFootprint} kg</strong> of CO₂.
                      </p>
                    </Alert>
                  )}
                </Form>
              </Tab.Pane>
              
              {/* Food Tab */}
              <Tab.Pane eventKey="food">
                <h5 className="mb-4">Log Your Food Consumption</h5>
                <Form onSubmit={handleFoodSubmit}>
                  <Row className="mb-3">
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Meal Type</Form.Label>
                        <Form.Select 
                          value={mealType} 
                          onChange={(e) => setMealType(e.target.value)}
                          required
                        >
                          <option value="">Select meal type</option>
                          <option value="vegetarian">Vegetarian</option>
                          <option value="vegan">Vegan</option>
                          <option value="meat">Meat-based</option>
                          <option value="fish">Fish-based</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      {mealType === 'meat' && (
                        <Form.Group className="mb-3">
                          <Form.Label>Meat Portion (% of meal)</Form.Label>
                          <Form.Range 
                            min="10" 
                            max="80" 
                            step="5"
                            value={meatPortion} 
                            onChange={(e) => setMeatPortion(parseInt(e.target.value))}
                          />
                          <div className="d-flex justify-content-between">
                            <small>10%</small>
                            <small className="text-center">{meatPortion}%</small>
                            <small>80%</small>
                          </div>
                        </Form.Group>
                      )}
                      <Form.Group className="mb-3">
                        <Form.Check 
                          type="checkbox" 
                          label="Locally sourced ingredients" 
                          checked={locallySourced}
                          onChange={(e) => setLocallySourced(e.target.checked)}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Button variant="success" type="submit" className="mb-3">
                    Calculate & Log
                  </Button>
                  
                  {foodFootprint !== null && (
                    <Alert variant="success" className="mt-3">
                      <Alert.Heading>Carbon Footprint Result</Alert.Heading>
                      <p>
                        Your {mealType} meal produces <strong>{foodFootprint} kg</strong> of CO₂.
                        {mealType === 'meat' && ` (with ${meatPortion}% meat portion)`}
                        {locallySourced && ' Locally sourced ingredients reduced your footprint!'}
                      </p>
                    </Alert>
                  )}
                </Form>
              </Tab.Pane>
              
              {/* Shopping Tab */}
              <Tab.Pane eventKey="shopping">
                <h5 className="mb-4">Log Your Shopping</h5>
                <Form onSubmit={handleShoppingSubmit}>
                  <Row className="mb-3">
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Item Type</Form.Label>
                        <Form.Select 
                          value={itemType} 
                          onChange={(e) => setItemType(e.target.value)}
                          required
                        >
                          <option value="">Select item type</option>
                          <option value="clothing">Clothing</option>
                          <option value="electronics">Electronics</option>
                          <option value="groceries">Groceries</option>
                          <option value="other">Other</option>
                        </Form.Select>
                      </Form.Group>
                      <Form.Group className="mb-3">
                        <Form.Label>Quantity</Form.Label>
                        <Form.Control 
                          type="number" 
                          min="1" 
                          step="1"
                          value={quantity} 
                          onChange={(e) => setQuantity(e.target.value)}
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Check 
                          type="checkbox" 
                          label="Second-hand item" 
                          checked={secondHand}
                          onChange={(e) => setSecondHand(e.target.checked)}
                        />
                      </Form.Group>
                      <Form.Group className="mb-3">
                        <Form.Check 
                          type="checkbox" 
                          label="Sustainable brand" 
                          checked={sustainableBrand}
                          onChange={(e) => setSustainableBrand(e.target.checked)}
                          disabled={secondHand}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Button variant="success" type="submit" className="mb-3">
                    Calculate & Log
                  </Button>
                  
                  {shoppingFootprint !== null && (
                    <Alert variant="success" className="mt-3">
                      <Alert.Heading>Carbon Footprint Result</Alert.Heading>
                      <p>
                        Purchasing {quantity} {itemType} item(s) produces <strong>{shoppingFootprint} kg</strong> of CO₂.
                        {secondHand && ' Buying second-hand significantly reduced your footprint!'}
                        {sustainableBrand && !secondHand && ' Choosing a sustainable brand reduced your footprint!'}
                      </p>
                    </Alert>
                  )}
                </Form>
              </Tab.Pane>
            </Tab.Content>
          </Tab.Container>
        </Card.Body>
      </Card>

      {/* Recent Activities */}
      <Card className="border-0 shadow-sm mb-4">
        <Card.Body className="p-4">
          <h4 className="mb-3">Recent Activities</h4>
          {activities.length > 0 ? (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Activity Type</th>
                    <th>Details</th>
                    <th>Carbon Footprint</th>
                  </tr>
                </thead>
                <tbody>
                  {activities.slice().reverse().map(activity => (
                    <tr key={activity.id}>
                      <td>{activity.date.toLocaleDateString()}</td>
                      <td>
                        {activity.type === 'travel' && <><FaCar className="me-2" /> Travel</>}
                        {activity.type === 'electricity' && <><FaLightbulb className="me-2" /> Electricity</>}
                        {activity.type === 'food' && <><FaUtensils className="me-2" /> Food</>}
                        {activity.type === 'shopping' && <><FaShoppingBag className="me-2" /> Shopping</>}
                      </td>
                      <td>
                        {activity.type === 'travel' && `${activity.mode}, ${activity.distance} km`}
                        {activity.type === 'electricity' && `${activity.appliance}, ${activity.duration} min`}
                        {activity.type === 'food' && `${activity.mealType} meal${activity.locallySourced ? ', locally sourced' : ''}`}
                        {activity.type === 'shopping' && `${activity.quantity} ${activity.itemType} item(s)${activity.secondHand ? ', second-hand' : ''}`}
                      </td>
                      <td>
                        <Badge bg="success">{activity.carbonFootprint} kg</Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-muted text-center py-4">
              No activities logged yet. Start tracking your carbon footprint by logging activities above.
            </p>
          )}
        </Card.Body>
      </Card>

      {/* Tips Section */}
      <Card className="border-0 shadow-sm mb-4">
        <Card.Body className="p-4">
          <h4 className="mb-3">Tips to Reduce Your Carbon Footprint</h4>
          <Row xs={1} md={2} className="g-4">
            <Col>
              <Card className="h-100 border-0 shadow-sm">
                <Card.Body>
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-success bg-opacity-10 p-2 rounded me-3">
                      <FaCar className="text-success" />
                    </div>
                    <h5 className="mb-0">Transportation</h5>
                  </div>
                  <ul className="mb-0">
                    <li>Use public transportation when possible</li>
                    <li>Carpool with colleagues or friends</li>
                    <li>Consider walking or cycling for short distances</li>
                    <li>Maintain your vehicle for optimal fuel efficiency</li>
                  </ul>
                </Card.Body>
              </Card>
            </Col>
            <Col>
              <Card className="h-100 border-0 shadow-sm">
                <Card.Body>
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-success bg-opacity-10 p-2 rounded me-3">
                      <FaLightbulb className="text-success" />
                    </div>
                    <h5 className="mb-0">Energy Usage</h5>
                  </div>
                  <ul className="mb-0">
                    <li>Switch to energy-efficient LED bulbs</li>
                    <li>Unplug electronics when not in use</li>
                    <li>Use natural lighting when possible</li>
                    <li>Adjust thermostat to reduce heating/cooling</li>
                  </ul>
                </Card.Body>
              </Card>
            </Col>
            <Col>
              <Card className="h-100 border-0 shadow-sm">
                <Card.Body>
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-success bg-opacity-10 p-2 rounded me-3">
                      <FaUtensils className="text-success" />
                    </div>
                    <h5 className="mb-0">Food Choices</h5>
                  </div>
                  <ul className="mb-0">
                    <li>Reduce meat consumption</li>
                    <li>Choose locally sourced and seasonal foods</li>
                    <li>Minimize food waste</li>
                    <li>Grow your own herbs or vegetables if possible</li>
                  </ul>
                </Card.Body>
              </Card>
            </Col>
            <Col>
              <Card className="h-100 border-0 shadow-sm">
                <Card.Body>
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-success bg-opacity-10 p-2 rounded me-3">
                      <FaShoppingBag className="text-success" />
                    </div>
                    <h5 className="mb-0">Shopping Habits</h5>
                  </div>
                  <ul className="mb-0">
                    <li>Buy second-hand items when possible</li>
                    <li>Choose products with minimal packaging</li>
                    <li>Support sustainable and eco-friendly brands</li>
                    <li>Repair items instead of replacing them</li>
                  </ul>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default CarbonFootprintPage;
