const express = require('express');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Issue = require('./models/Issue');
const User = require('./models/User');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
app.use(express.json());

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
  
  // Debug route to test issue creation
  app.post('/debug/issue', async (req, res) => {
    try {
      console.log('Received issue creation request');
      console.log('Request body:', req.body);
      
      // Find a test user
      const testUser = await User.findOne({ email: '<EMAIL>' });
      
      if (!testUser) {
        console.log('Test user not found');
        return res.status(404).json({
          success: false,
          error: 'Test user not found'
        });
      }
      
      console.log('Found test user:', testUser._id);
      
      // Create a test issue
      const issueData = {
        title: 'Debug Test Issue',
        category: 'Roads',
        description: 'This is a test issue created for debugging',
        location: 'Test Location',
        status: 'Open',
        user: testUser._id
      };
      
      console.log('Creating issue with data:', issueData);
      
      const issue = await Issue.create(issueData);
      
      console.log('Issue created successfully:', issue);
      
      res.status(201).json({
        success: true,
        data: issue
      });
    } catch (err) {
      console.error('Error creating issue:', err);
      res.status(400).json({
        success: false,
        error: err.message,
        stack: err.stack
      });
    }
  });
  
  // Start server
  const PORT = 5003;
  app.listen(PORT, () => {
    console.log(`Debug server running on port ${PORT}`);
    console.log(`Test endpoint: http://localhost:${PORT}/debug/issue`);
  });
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});
