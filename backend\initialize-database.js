const mongoose = require('mongoose');
const dotenv = require('dotenv');
const bcrypt = require('bcryptjs');

// Load environment variables
dotenv.config();

// Import models
const User = require('./models/User');
const Issue = require('./models/Issue');
const Event = require('./models/Event');
const Resource = require('./models/Resource');
const GreenSpace = require('./models/GreenSpace');
const Transport = require('./models/Transport');
const Challenge = require('./models/Challenge');
const ServiceReview = require('./models/ServiceReview');
const Service = require('./models/Service');
const Course = require('./models/Course');

// Connect to MongoDB
console.log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');

  try {
    // Create admin user if it doesn't exist
    const adminExists = await User.findOne({ email: '<EMAIL>' });

    if (!adminExists) {
      console.log('Creating admin user...');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);

      await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        isAdmin: true
      });
      console.log('Admin user created');
    } else {
      console.log('Admin user already exists');
    }

    // Create test user if it doesn't exist
    const testUserExists = await User.findOne({ email: '<EMAIL>' });

    if (!testUserExists) {
      console.log('Creating test user...');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('password123', salt);

      await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: hashedPassword,
        isAdmin: false
      });
      console.log('Test user created');
    } else {
      console.log('Test user already exists');
    }

    // Create a service
    const serviceExists = await Service.findOne({ name: 'Residential Waste Collection' });

    if (!serviceExists) {
      console.log('Creating sample service...');
      await Service.create({
        name: 'Residential Waste Collection',
        category: 'waste',
        description: 'Weekly curbside collection of household waste and recyclables for residential properties within city limits'
      });
      console.log('Sample service created');
    } else {
      console.log('Sample service already exists');
    }

    // Create a green space
    const spaceExists = await GreenSpace.findOne({ name: 'Riverside Memorial Park' });
    let spaceId;

    if (!spaceExists) {
      console.log('Creating sample green space...');
      const space = await GreenSpace.create({
        name: 'Riverside Memorial Park',
        location: 'Downtown',
        description: 'A 12-acre urban park featuring mature oak trees, walking paths, two playgrounds, and open green spaces for recreational activities. Recently renovated with improved lighting and accessibility features.',
        amenities: ['Walking Paths', 'Playgrounds', 'Picnic Area', 'Public Restrooms', 'Parking', 'Dog-friendly Area', 'Basketball Court'],
        images: ['riverside_park.jpg'],
        coordinates: {
          latitude: 40.7812,
          longitude: -73.9665
        }
      });
      console.log('Sample green space created');
      spaceId = space._id;
    } else {
      console.log('Sample green space already exists');
      spaceId = spaceExists._id;
    }

    // Create an event
    const eventExists = await Event.findOne({ title: 'Spring Park Clean-Up & Native Planting' });

    if (!eventExists && spaceId) {
      console.log('Creating sample event...');
      await Event.create({
        title: 'Spring Park Clean-Up & Native Planting',
        spaceId: spaceId,
        description: 'Join our seasonal park clean-up and help plant native wildflowers in our new pollinator garden. All necessary tools, gloves, and materials will be provided. Suitable for all ages and abilities, with tasks available for everyone. Refreshments provided by Local Roots Café.',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        startTime: '9:00 AM',
        endTime: '12:30 PM',
        organizer: 'Parks & Recreation Department with Urban Ecology Center',
        image: 'park_cleanup.jpg',
        attendees: []
      });
      console.log('Sample event created');
    } else {
      console.log('Sample event already exists or no space ID available');
    }

    // Create a challenge
    const challengeExists = await Challenge.findOne({ title: 'Urban Waste Reduction Challenge' });

    if (!challengeExists) {
      console.log('Creating sample challenge...');
      await Challenge.create({
        title: 'Urban Waste Reduction Challenge',
        description: 'Reduce your household waste by implementing practical zero-waste strategies in your daily urban lifestyle.',
        category: 'Waste Reduction',
        image: 'waste_reduction.jpg',
        startDate: new Date(),
        endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        points: 120,
        tasks: [
          { title: 'Conduct a waste audit of your household for 3 days', completed: false },
          { title: 'Switch to reusable shopping bags, water bottles, and coffee cups', completed: false },
          { title: 'Start a small compost bin suitable for apartment living', completed: false },
          { title: 'Visit a bulk store and purchase 3 items without packaging', completed: false },
          { title: 'Document your waste reduction journey with before/after photos', completed: false }
        ],
        participants: []
      });
      console.log('Sample challenge created');
    } else {
      console.log('Sample challenge already exists');
    }

    // Create a course
    const courseExists = await Course.findOne({ title: 'Urban Food Production: From Balcony to Community Garden' });

    if (!courseExists) {
      console.log('Creating sample course...');
      await Course.create({
        title: 'Urban Food Production: From Balcony to Community Garden',
        category: 'sustainability',
        description: 'A comprehensive course on growing nutritious food in urban environments, covering container gardening, vertical systems, soil health, and community garden participation. Suitable for beginners and intermediate gardeners.',
        instructor: 'Dr. Elena Rodriguez, Urban Agriculture Specialist',
        duration: '6 weeks',
        image: 'urban_gardening.jpg',
        lessons: [
          {
            title: 'Urban Growing Fundamentals',
            content: 'Understanding urban growing conditions, microclimate assessment, and space optimization techniques for successful food production in city environments.'
          },
          {
            title: 'Container Systems for Small Spaces',
            content: 'Selecting appropriate containers, soil mixes, and plant varieties for balconies, windowsills, and small yards. Includes DIY self-watering container designs.'
          },
          {
            title: 'Vertical and Space-Saving Techniques',
            content: 'Implementing trellises, hanging systems, and vertical gardens to maximize growing space in three dimensions.'
          },
          {
            title: 'Season Extension in Urban Settings',
            content: 'Using row covers, cold frames, and strategic planting schedules to extend your growing season in urban microclimates.'
          },
          {
            title: 'Community Garden Participation',
            content: 'Finding and joining community gardens, understanding shared space etiquette, and contributing to community growing projects.'
          },
          {
            title: 'Urban Garden Planning and Management',
            content: 'Creating annual growing plans, managing pests organically, and implementing water conservation techniques specific to urban environments.'
          }
        ]
      });
      console.log('Sample course created');
    } else {
      console.log('Sample course already exists');
    }

    // Create a sample issue
    const issueExists = await Issue.findOne({ title: 'Non-functioning Streetlight at Oak & Pine' });

    if (!issueExists) {
      console.log('Creating sample issue...');
      const testUser = await User.findOne({ email: '<EMAIL>' });

      if (testUser) {
        await Issue.create({
          title: 'Non-functioning Streetlight at Oak & Pine',
          description: 'Streetlight ID #SL-2467 has been completely out for 5 nights. This is creating a safety hazard for pedestrians in the evening as this is a busy intersection with a crosswalk. I have reported this through the city\'s 311 app (reference #27834) but no action has been taken yet.',
          category: 'infrastructure',
          location: 'Intersection of Oak Avenue and Pine Street',
          status: 'open',
          priority: 'high',
          images: ['streetlight_outage.jpg'],
          user: testUser._id,
          coordinates: {
            latitude: 40.7580,
            longitude: -73.9855
          },
          dateReported: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
        });
        console.log('Sample issue created');
      } else {
        console.log('Could not create sample issue - test user not found');
      }
    } else {
      console.log('Sample issue already exists');
    }

    // Create a sample transport feedback
    const transportExists = await Transport.findOne({ routeName: 'Downtown Express Bus Route 42' });

    if (!transportExists) {
      console.log('Creating sample transport feedback...');
      const testUser = await User.findOne({ email: '<EMAIL>' });

      if (testUser) {
        await Transport.create({
          routeName: 'Downtown Express Bus Route 42',
          routeType: 'bus',
          rating: 3,
          feedback: 'The morning service (7:30-8:30am) is consistently running 12-15 minutes behind schedule, which affects commuters trying to get to work on time. The bus tracking app shows inaccurate arrival times. Weekend service is much more reliable, and the new low-floor buses are a significant accessibility improvement. Driver courtesy is excellent.',
          user: testUser._id,
          tripDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          tripTime: 'Morning Rush Hour (7:30-8:30am)',
          suggestions: 'Increase frequency during peak hours or adjust the published schedule to reflect actual arrival times. Update the real-time tracking system.'
        });
        console.log('Sample transport feedback created');
      } else {
        console.log('Could not create sample transport feedback - test user not found');
      }
    } else {
      console.log('Sample transport feedback already exists');
    }

    // Create a sample service review
    const reviewExists = await ServiceReview.findOne({ title: 'Reliable and well-organized waste collection service' });

    if (!reviewExists) {
      console.log('Creating sample service review...');
      const service = await Service.findOne({ name: 'Residential Waste Collection' });
      const testUser = await User.findOne({ email: '<EMAIL>' });

      if (service && testUser) {
        await ServiceReview.create({
          serviceId: service._id,
          title: 'Reliable and well-organized waste collection service',
          text: 'I\'ve been monitoring our neighborhood\'s waste collection service for the past three months, and I\'m impressed with the consistency and reliability. Collection occurs on the scheduled day (Wednesday) between 7:30-9:00am without fail, even during inclement weather. The collection crews are efficient and careful not to scatter debris. The new split recycling/waste trucks are a great improvement, and the city\'s waste collection app provides helpful reminders and updates about holiday schedule changes. My only suggestion would be to provide more education about proper recycling sorting, as I notice many neighbors still placing non-recyclable items in recycling bins.',
          rating: 4,
          user: testUser._id,
          helpful: [],
          datePosted: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
          neighborhood: 'Riverside'
        });
        console.log('Sample service review created');
      } else {
        console.log('Could not create sample service review - service or test user not found');
      }
    } else {
      console.log('Sample service review already exists');
    }

    // Create a sample resource
    const resourceExists = await Resource.findOne({ title: 'Professional Gardening Tool Set' });

    if (!resourceExists) {
      console.log('Creating sample resource...');
      const testUser = await User.findOne({ email: '<EMAIL>' });

      if (testUser) {
        await Resource.create({
          title: 'Professional Gardening Tool Set',
          category: 'garden',
          description: 'Complete set of high-quality gardening tools including Felco pruners, ergonomic trowel and transplanter, garden fork, weeding tool, and garden gloves. Perfect for container gardening or community garden plots. All tools are in excellent condition and were purchased last season. Available for borrowing on weekends or weekday evenings with advance notice.',
          neighborhood: 'Downtown',
          availability: 'Weekends and weekday evenings after 6pm',
          condition: 'Excellent - like new',
          images: ['garden_tools.jpg'],
          owner: testUser._id,
          requests: [],
          messages: [],
          datePosted: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          preferredExchange: 'In-person pickup at Downtown Community Center',
          additionalNotes: 'Please return tools clean and dry. Happy to provide basic guidance on tool use if needed.'
        });
        console.log('Sample resource created');
      } else {
        console.log('Could not create sample resource - test user not found');
      }
    } else {
      console.log('Sample resource already exists');
    }

    console.log('\nDatabase initialization complete!');

  } catch (error) {
    console.error('Error initializing database:', error);
  }

  // Close the connection
  await mongoose.connection.close();
  console.log('Connection closed');
  process.exit(0);
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});
