import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Badge, Modal, Tab, Nav, ProgressBar } from 'react-bootstrap';
import { FaGraduationCap, FaSearch, FaFilter, FaBook, FaUser, FaPlay, FaCheck, FaDownload, FaLock } from 'react-icons/fa';

const LearningHubPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [showLessonModal, setShowLessonModal] = useState(false);
  const [selectedLesson, setSelectedLesson] = useState(null);

  // Mock data for course categories
  const categories = [
    { id: 'sustainability', name: 'Sustainable Living' },
    { id: 'waste', name: 'Waste Management' },
    { id: 'energy', name: 'Energy Conservation' },
    { id: 'water', name: 'Water Conservation' },
    { id: 'food', name: 'Sustainable Food' },
    { id: 'transport', name: 'Green Transportation' },
    { id: 'construction', name: 'Eco-Friendly Construction' }
  ];

  // Mock data for courses
  const courses = [
    { 
      id: 1, 
      title: 'Introduction to Sustainable Living', 
      category: 'sustainability', 
      description: 'Learn the basics of sustainable living and how to reduce your environmental footprint.', 
      instructor: 'Dr. Emily Green',
      duration: '4 weeks',
      lessons: 12,
      enrolled: 245,
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    { 
      id: 2, 
      title: 'Zero Waste Home', 
      category: 'waste', 
      description: 'Practical strategies to reduce waste in your home and daily life.', 
      instructor: 'Sarah Johnson',
      duration: '3 weeks',
      lessons: 9,
      enrolled: 178,
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    { 
      id: 3, 
      title: 'Home Energy Efficiency', 
      category: 'energy', 
      description: 'Learn how to reduce energy consumption and lower your utility bills.', 
      instructor: 'Michael Chen',
      duration: '2 weeks',
      lessons: 8,
      enrolled: 132,
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    { 
      id: 4, 
      title: 'Water Conservation Techniques', 
      category: 'water', 
      description: 'Strategies for reducing water usage in your home and garden.', 
      instructor: 'Dr. James Waters',
      duration: '2 weeks',
      lessons: 6,
      enrolled: 98,
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1527100673774-cce25eafaf7f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    { 
      id: 5, 
      title: 'Urban Gardening Basics', 
      category: 'food', 
      description: 'Learn how to grow your own food in limited urban spaces.', 
      instructor: 'Maria Garcia',
      duration: '4 weeks',
      lessons: 10,
      enrolled: 215,
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    },
    { 
      id: 6, 
      title: 'Sustainable Transportation', 
      category: 'transport', 
      description: 'Explore eco-friendly transportation options for urban environments.', 
      instructor: 'David Kim',
      duration: '2 weeks',
      lessons: 7,
      enrolled: 124,
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1519583272095-6433daf26b6e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80'
    }
  ];

  // Mock data for user's enrolled courses
  const enrolledCourses = [
    { 
      id: 1, 
      title: 'Introduction to Sustainable Living', 
      progress: 75,
      lastAccessed: '2023-04-15',
      completedLessons: 9,
      totalLessons: 12
    },
    { 
      id: 5, 
      title: 'Urban Gardening Basics', 
      progress: 30,
      lastAccessed: '2023-04-10',
      completedLessons: 3,
      totalLessons: 10
    }
  ];

  // Mock data for course lessons
  const courseLessons = {
    1: [
      { id: 1, title: 'What is Sustainability?', duration: '15 min', completed: true },
      { id: 2, title: 'Measuring Your Environmental Footprint', duration: '20 min', completed: true },
      { id: 3, title: 'Principles of Sustainable Living', duration: '25 min', completed: true },
      { id: 4, title: 'Sustainable Consumption', duration: '18 min', completed: true },
      { id: 5, title: 'Reducing Waste', duration: '22 min', completed: true },
      { id: 6, title: 'Energy Conservation', duration: '20 min', completed: true },
      { id: 7, title: 'Water Conservation', duration: '15 min', completed: true },
      { id: 8, title: 'Sustainable Food Choices', duration: '25 min', completed: true },
      { id: 9, title: 'Sustainable Transportation', duration: '18 min', completed: true },
      { id: 10, title: 'Creating a Sustainable Home', duration: '30 min', completed: false },
      { id: 11, title: 'Community Involvement', duration: '20 min', completed: false },
      { id: 12, title: 'Long-term Sustainability Planning', duration: '25 min', completed: false }
    ]
  };

  const handleViewCourse = (course) => {
    setSelectedCourse(course);
    setShowModal(true);
  };

  const handleEnrollCourse = (courseId) => {
    // In a real app, this would make an API call to enroll in the course
    console.log(`Enrolled in course ${courseId}`);
    setShowModal(false);
  };

  const handleViewLesson = (lesson) => {
    setSelectedLesson(lesson);
    setShowLessonModal(true);
  };

  const handleCompleteLesson = (lessonId) => {
    // In a real app, this would make an API call to mark the lesson as completed
    console.log(`Completed lesson ${lessonId}`);
    setShowLessonModal(false);
  };

  const filteredCourses = courses.filter(course => 
    (searchTerm === '' || course.title.toLowerCase().includes(searchTerm.toLowerCase()) || course.description.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (category === '' || course.category === category)
  );

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Urban Learning Hub</h1>
          <p className="lead">
            Access courses on sustainable living, waste management, eco-friendly construction, and more.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-primary bg-opacity-10 p-3 rounded">
            <FaGraduationCap className="text-primary fs-1" />
          </div>
        </Col>
      </Row>

      <Tab.Container defaultActiveKey="courses">
        <Row>
          <Col lg={3} className="mb-4">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-0">
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="courses" className="rounded-0 px-4 py-3">
                      <FaBook className="me-2" />
                      Browse Courses
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="my-courses" className="rounded-0 px-4 py-3">
                      <FaUser className="me-2" />
                      My Learning
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </Card.Body>
            </Card>
            
            <Card className="border-0 shadow-sm mt-4">
              <Card.Body className="p-4">
                <h5 className="mb-3">Filter Courses</h5>
                
                <Form.Group className="mb-3" controlId="searchTerm">
                  <Form.Label>Search</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Search courses..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </Form.Group>
                
                <Form.Group className="mb-3" controlId="categoryFilter">
                  <Form.Label>Category</Form.Label>
                  <Form.Select
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                  >
                    <option value="">All Categories</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
                
                <div className="d-grid">
                  <Button 
                    variant="outline-primary"
                    onClick={() => {
                      setSearchTerm('');
                      setCategory('');
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
          
          <Col lg={9}>
            <Tab.Content>
              <Tab.Pane eventKey="courses">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <h4 className="mb-0">Available Courses</h4>
                  <div className="d-flex">
                    <div className="input-group me-2">
                      <span className="input-group-text">
                        <FaSearch />
                      </span>
                      <Form.Control
                        type="text"
                        placeholder="Quick search..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Button variant="outline-primary">
                      <FaFilter />
                    </Button>
                  </div>
                </div>
                
                {filteredCourses.length > 0 ? (
                  <Row xs={1} md={2} lg={3} className="g-4">
                    {filteredCourses.map((course) => (
                      <Col key={course.id}>
                        <Card className="h-100 border-0 shadow-sm course-card">
                          <div style={{ height: '160px', overflow: 'hidden' }}>
                            <Card.Img 
                              variant="top" 
                              src={course.image} 
                              alt={course.title}
                              style={{ objectFit: 'cover', height: '100%', width: '100%' }}
                            />
                          </div>
                          <Card.Body>
                            <div className="d-flex justify-content-between align-items-start mb-2">
                              <Badge bg="primary" className="mb-2">
                                {categories.find(c => c.id === course.category)?.name}
                              </Badge>
                              <div className="text-warning">
                                {course.rating} ★
                              </div>
                            </div>
                            <Card.Title>{course.title}</Card.Title>
                            <Card.Text className="text-muted small mb-3">
                              {course.description.substring(0, 80)}...
                            </Card.Text>
                            <div className="d-flex justify-content-between align-items-center mb-3 small text-muted">
                              <div>
                                <FaUser className="me-1" />
                                {course.instructor}
                              </div>
                              <div>
                                {course.lessons} lessons
                              </div>
                            </div>
                            <div className="d-grid">
                              <Button 
                                variant="outline-primary" 
                                size="sm"
                                onClick={() => handleViewCourse(course)}
                              >
                                View Course
                              </Button>
                            </div>
                          </Card.Body>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                ) : (
                  <Card className="border-0 shadow-sm">
                    <Card.Body className="p-5 text-center">
                      <p className="mb-3 text-muted">No courses found matching your criteria.</p>
                      <Button 
                        variant="outline-primary"
                        onClick={() => {
                          setSearchTerm('');
                          setCategory('');
                        }}
                      >
                        Clear Filters
                      </Button>
                    </Card.Body>
                  </Card>
                )}
              </Tab.Pane>
              
              <Tab.Pane eventKey="my-courses">
                <h4 className="mb-4">My Enrolled Courses</h4>
                
                {enrolledCourses.length > 0 ? (
                  <Row xs={1} className="g-4">
                    {enrolledCourses.map((course) => (
                      <Col key={course.id}>
                        <Card className="border-0 shadow-sm">
                          <Card.Body>
                            <Row>
                              <Col md={8}>
                                <h5>{course.title}</h5>
                                <p className="text-muted small mb-3">
                                  Last accessed: {course.lastAccessed}
                                </p>
                                <div className="mb-3">
                                  <div className="d-flex justify-content-between mb-1 small">
                                    <span>Progress</span>
                                    <span>{course.progress}%</span>
                                  </div>
                                  <ProgressBar now={course.progress} variant="primary" />
                                </div>
                                <p className="small text-muted mb-0">
                                  {course.completedLessons} of {course.totalLessons} lessons completed
                                </p>
                              </Col>
                              <Col md={4} className="d-flex align-items-center justify-content-end">
                                <Button 
                                  variant="primary"
                                  onClick={() => {
                                    const fullCourse = courses.find(c => c.id === course.id);
                                    handleViewCourse(fullCourse);
                                  }}
                                >
                                  Continue Learning
                                </Button>
                              </Col>
                            </Row>
                          </Card.Body>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                ) : (
                  <Card className="border-0 shadow-sm">
                    <Card.Body className="p-5 text-center">
                      <p className="mb-3 text-muted">You haven't enrolled in any courses yet.</p>
                      <Button 
                        variant="primary"
                        onClick={() => document.querySelector('a[href="#courses"]').click()}
                      >
                        Browse Courses
                      </Button>
                    </Card.Body>
                  </Card>
                )}
                
                <h4 className="mt-5 mb-4">Recommended for You</h4>
                
                <Row xs={1} md={2} className="g-4">
                  {courses
                    .filter(course => !enrolledCourses.some(ec => ec.id === course.id))
                    .slice(0, 4)
                    .map((course) => (
                      <Col key={course.id}>
                        <Card className="h-100 border-0 shadow-sm">
                          <Row className="g-0">
                            <Col md={4}>
                              <div style={{ height: '100%', overflow: 'hidden' }}>
                                <Card.Img 
                                  src={course.image} 
                                  alt={course.title}
                                  className="h-100"
                                  style={{ objectFit: 'cover' }}
                                />
                              </div>
                            </Col>
                            <Col md={8}>
                              <Card.Body>
                                <Badge bg="primary" className="mb-2">
                                  {categories.find(c => c.id === course.category)?.name}
                                </Badge>
                                <Card.Title>{course.title}</Card.Title>
                                <Card.Text className="text-muted small mb-3">
                                  {course.description.substring(0, 60)}...
                                </Card.Text>
                                <Button 
                                  variant="outline-primary" 
                                  size="sm"
                                  onClick={() => handleViewCourse(course)}
                                >
                                  View Course
                                </Button>
                              </Card.Body>
                            </Col>
                          </Row>
                        </Card>
                      </Col>
                    ))
                  }
                </Row>
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Course Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Course Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedCourse && (
            <Row>
              <Col md={5}>
                <img 
                  src={selectedCourse.image} 
                  alt={selectedCourse.title}
                  className="img-fluid rounded mb-3"
                />
                <Badge bg="primary" className="mb-3">
                  {categories.find(c => c.id === selectedCourse.category)?.name}
                </Badge>
                <h4>{selectedCourse.title}</h4>
                <p>{selectedCourse.description}</p>
                
                <div className="mb-3">
                  <strong>Instructor:</strong> {selectedCourse.instructor}
                </div>
                
                <div className="mb-3">
                  <strong>Duration:</strong> {selectedCourse.duration}
                </div>
                
                <div className="mb-3">
                  <strong>Lessons:</strong> {selectedCourse.lessons}
                </div>
                
                <div className="mb-3">
                  <strong>Enrolled:</strong> {selectedCourse.enrolled} students
                </div>
                
                <div className="mb-3">
                  <strong>Rating:</strong> {selectedCourse.rating} / 5
                </div>
              </Col>
              <Col md={7}>
                <h5 className="mb-3">Course Content</h5>
                
                {courseLessons[selectedCourse.id] ? (
                  <div className="mb-4">
                    {courseLessons[selectedCourse.id].map((lesson, index) => (
                      <div 
                        key={lesson.id} 
                        className="d-flex justify-content-between align-items-center p-3 border-bottom"
                      >
                        <div className="d-flex align-items-center">
                          <div className="me-3">
                            {lesson.completed ? (
                              <span className="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '24px', height: '24px' }}>
                                <FaCheck size={12} />
                              </span>
                            ) : (
                              <span className="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '24px', height: '24px' }}>
                                {index + 1}
                              </span>
                            )}
                          </div>
                          <div>
                            <p className="mb-0">{lesson.title}</p>
                            <small className="text-muted">{lesson.duration}</small>
                          </div>
                        </div>
                        <Button 
                          variant={lesson.completed ? "outline-success" : "outline-primary"} 
                          size="sm"
                          onClick={() => handleViewLesson(lesson)}
                        >
                          {lesson.completed ? <FaCheck className="me-1" /> : <FaPlay className="me-1" />}
                          {lesson.completed ? 'Completed' : 'Start'}
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted">Lesson content will be available after enrollment.</p>
                )}
                
                <h5 className="mb-3">What You'll Learn</h5>
                <ul>
                  <li>Understand the core principles of sustainable living</li>
                  <li>Implement practical strategies to reduce your environmental footprint</li>
                  <li>Make informed decisions about consumption and waste</li>
                  <li>Connect with a community of like-minded individuals</li>
                </ul>
                
                <h5 className="mb-3">Resources</h5>
                <div className="d-flex align-items-center mb-2">
                  <FaDownload className="me-2 text-primary" />
                  <span>Course Syllabus.pdf</span>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <FaDownload className="me-2 text-primary" />
                  <span>Recommended Reading List.pdf</span>
                </div>
                <div className="d-flex align-items-center">
                  <FaLock className="me-2 text-muted" />
                  <span className="text-muted">Additional Resources (Available after enrollment)</span>
                </div>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          {selectedCourse && !enrolledCourses.some(course => course.id === selectedCourse.id) ? (
            <Button 
              variant="primary"
              onClick={() => handleEnrollCourse(selectedCourse.id)}
            >
              Enroll Now
            </Button>
          ) : (
            <Button 
              variant="success"
              onClick={() => {
                setShowModal(false);
                if (courseLessons[selectedCourse.id]) {
                  const nextLesson = courseLessons[selectedCourse.id].find(lesson => !lesson.completed);
                  if (nextLesson) {
                    handleViewLesson(nextLesson);
                  }
                }
              }}
            >
              Continue Learning
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Lesson Modal */}
      <Modal show={showLessonModal} onHide={() => setShowLessonModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Lesson: {selectedLesson?.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedLesson && (
            <>
              <div className="ratio ratio-16x9 mb-4 bg-dark d-flex align-items-center justify-content-center">
                <div className="text-white">
                  <FaPlay size={48} />
                  <p className="mt-3">Video lesson would play here</p>
                </div>
              </div>
              
              <h4>Lesson Content</h4>
              <p>
                This is where the lesson content would be displayed. It could include text, images, and interactive elements.
                For this demo, we're showing placeholder content.
              </p>
              
              <div className="mb-4">
                <h5>Key Points</h5>
                <ul>
                  <li>First important concept from this lesson</li>
                  <li>Second important concept from this lesson</li>
                  <li>Third important concept from this lesson</li>
                  <li>Fourth important concept from this lesson</li>
                </ul>
              </div>
              
              <div className="mb-4">
                <h5>Additional Resources</h5>
                <div className="d-flex align-items-center mb-2">
                  <FaDownload className="me-2 text-primary" />
                  <span>Lesson Notes.pdf</span>
                </div>
                <div className="d-flex align-items-center">
                  <FaDownload className="me-2 text-primary" />
                  <span>Further Reading.pdf</span>
                </div>
              </div>
              
              <div className="alert alert-info">
                <strong>Practice Activity:</strong> Complete the worksheet to apply what you've learned in this lesson.
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowLessonModal(false)}>
            Close
          </Button>
          <Button 
            variant={selectedLesson?.completed ? "success" : "primary"}
            onClick={() => handleCompleteLesson(selectedLesson?.id)}
          >
            {selectedLesson?.completed ? 'Already Completed' : 'Mark as Completed'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default LearningHubPage;
