const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const cors = require('cors');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Enable CORS for all routes
app.use(cors());

// Parse JSON request bodies
app.use(express.json());

// Middleware to log all requests
app.use((req, res, next) => {
  const start = Date.now();
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);

  if (req.body && Object.keys(req.body).length > 0) {
    // Clone the body and mask any passwords
    const safeBody = { ...req.body };
    if (safeBody.password) {
      safeBody.password = '********';
    }
    console.log('Body:', safeBody);
  }

  // Capture the original end method
  const originalEnd = res.end;

  // Override the end method to log the response
  res.end = function(chunk, encoding) {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] Response: ${res.statusCode} (${duration}ms)`);

    // Call the original end method
    return originalEnd.call(this, chunk, encoding);
  };

  next();
});

// Middleware to authenticate token
const authMiddleware = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ message: 'No token provided' });
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return res.status(401).json({ message: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production'
    );
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ message: 'Invalid token' });
  }
};

// Connect to MongoDB
console.log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');

  // Start server
  const PORT = 5003;
  app.listen(PORT, () => {
    console.log(`User stats server running on port ${PORT}`);
    console.log(`http://localhost:${PORT}`);
  });
})
.catch(error => {
  console.error('MongoDB connection error:', error);
  process.exit(1);
});

// Root route
app.get('/', (req, res) => {
  res.json({
    message: 'User stats server is running',
    timestamp: new Date().toISOString()
  });
});

// User stats route
app.get('/api/user/stats', authMiddleware, (req, res) => {
  console.log('\n=== GET USER STATS ===');
  console.log('User ID:', req.user.id);

  // Return mock user stats
  const mockStats = {
    issuesReported: 5,
    transportFeedbacks: 3,
    challengesCompleted: 2,
    reviewsSubmitted: 4,
    resourcesShared: 1,
    eventsAttended: 2,
    coursesCompleted: 1
  };

  console.log('Returning mock stats:', mockStats);

  res.json(mockStats);
});

// User activity route
app.get('/api/user/activity/:type', authMiddleware, (req, res) => {
  console.log('\n=== GET USER ACTIVITY ===');
  console.log('User ID:', req.user.id);
  console.log('Activity type:', req.params.type);

  // Validate activity type
  const validTypes = ['issues', 'transport', 'challenges', 'reviews', 'resources', 'events', 'courses', 'all'];
  if (!validTypes.includes(req.params.type)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid activity type'
    });
  }

  // Return mock activity data
  const mockActivityData = {
    issues: [
      {
        issueId: '60d21b4667d0d8992e610c85',
        title: 'Pothole on Main Street',
        category: 'Roads',
        status: 'Open',
        reportedAt: new Date().toISOString()
      },
      {
        issueId: '60d21b4667d0d8992e610c86',
        title: 'Broken Streetlight',
        category: 'Lighting',
        status: 'In Progress',
        reportedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    transport: [
      {
        feedbackId: '60d21b4667d0d8992e610c87',
        title: 'Bus Route 42 Feedback',
        category: 'Bus Routes',
        submittedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    challenges: [
      {
        challengeId: '60d21b4667d0d8992e610c88',
        title: 'Reduce Plastic Usage',
        category: 'Sustainability',
        points: 50,
        completedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    reviews: [
      {
        reviewId: '60d21b4667d0d8992e610c89',
        serviceId: '60d21b4667d0d8992e610c8a',
        serviceName: 'Garbage Collection',
        rating: 4,
        submittedAt: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    resources: [
      {
        resourceId: '60d21b4667d0d8992e610c8b',
        title: 'Garden Tools',
        category: 'Tools',
        sharedAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    events: [
      {
        eventId: '60d21b4667d0d8992e610c8c',
        title: 'Community Cleanup',
        organizer: 'Green City Initiative',
        attendedAt: new Date(Date.now() - 42 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    courses: [
      {
        courseId: '60d21b4667d0d8992e610c8d',
        title: 'Sustainable Living 101',
        category: 'Education',
        completedAt: new Date(Date.now() - 49 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
  };

  // Return the requested activity type or all
  if (req.params.type === 'all') {
    console.log('Returning all activity data');
    res.json({
      success: true,
      data: mockActivityData
    });
  } else {
    console.log(`Returning ${req.params.type} activity data`);
    res.json({
      success: true,
      data: mockActivityData[req.params.type]
    });
  }
});
