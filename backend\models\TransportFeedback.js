const mongoose = require('mongoose');

const TransportFeedbackSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: ['Routes', 'Schedule', 'Frequency', 'Accessibility', 'Maintenance', 'Other']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  routeNumber: {
    type: String
  },
  status: {
    type: String,
    enum: ['Under Review', 'Approved', 'Implemented', 'Declined'],
    default: 'Under Review'
  },
  votes: {
    up: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      }
    ],
    down: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      }
    ]
  },
  comments: [
    {
      text: {
        type: String,
        required: true
      },
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Virtual for total votes
TransportFeedbackSchema.virtual('totalVotes').get(function() {
  return this.votes.up.length - this.votes.down.length;
});

// Enable virtuals
TransportFeedbackSchema.set('toJSON', { virtuals: true });
TransportFeedbackSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('TransportFeedback', TransportFeedbackSchema);
