const mongoose = require('mongoose');
const UserStats = require('../models/UserStats');
const Issue = require('../models/Issue');
const Event = require('../models/Event');
const Resource = require('../models/Resource');
const Challenge = require('../models/Challenge');
const Course = require('../models/Course');
const ServiceReview = require('../models/ServiceReview');

// @desc    Get user stats
// @route   GET /api/user/stats
// @access  Private
exports.getUserStats = async (req, res) => {
  try {
    const userId = req.user._id;
    console.log('Fetching stats for user:', userId);

    // Find or create user stats
    let userStats = await UserStats.findOne({ user: userId });

    if (!userStats) {
      console.log('No stats found, creating new stats record');
      
      // If no stats exist, create a new record with real data from other collections
      userStats = await createUserStatsFromData(userId);
    } else {
      console.log('Found existing stats, checking if update needed');
      
      // Check if stats need to be updated (e.g., if it's been more than a day)
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      if (userStats.lastUpdated < oneDayAgo) {
        console.log('Stats are outdated, updating from collections');
        userStats = await updateUserStatsFromData(userId, userStats);
      }
    }

    res.status(200).json(userStats);
  } catch (error) {
    console.error('Error fetching user stats:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// @desc    Get detailed user activity
// @route   GET /api/user/activity/:type
// @access  Private
exports.getUserActivity = async (req, res) => {
  try {
    const userId = req.user._id;
    const { type } = req.params;
    
    // Validate activity type
    const validTypes = ['issues', 'transport', 'challenges', 'reviews', 'resources', 'events', 'courses', 'all'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ error: 'Invalid activity type' });
    }
    
    // Find user stats
    const userStats = await UserStats.findOne({ user: userId });
    
    if (!userStats) {
      return res.status(404).json({ error: 'No activity data found' });
    }
    
    let activityData;
    
    // Return the requested activity type
    switch (type) {
      case 'issues':
        activityData = userStats.issueDetails;
        break;
      case 'transport':
        activityData = userStats.transportDetails;
        break;
      case 'challenges':
        activityData = userStats.challengeDetails;
        break;
      case 'reviews':
        activityData = userStats.reviewDetails;
        break;
      case 'resources':
        activityData = userStats.resourceDetails;
        break;
      case 'events':
        activityData = userStats.eventDetails;
        break;
      case 'courses':
        activityData = userStats.courseDetails;
        break;
      case 'all':
        activityData = {
          issues: userStats.issueDetails,
          transport: userStats.transportDetails,
          challenges: userStats.challengeDetails,
          reviews: userStats.reviewDetails,
          resources: userStats.resourceDetails,
          events: userStats.eventDetails,
          courses: userStats.courseDetails
        };
        break;
    }
    
    res.status(200).json({
      success: true,
      data: activityData
    });
  } catch (error) {
    console.error('Error fetching user activity:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// Helper function to create user stats from existing data
const createUserStatsFromData = async (userId) => {
  try {
    // Get counts from various collections
    const issuesCount = await Issue.countDocuments({ user: userId });
    const eventsCount = await Event.countDocuments({ 
      'participants.user': userId 
    });
    const resourcesCount = await Resource.countDocuments({ owner: userId });
    const challengesCount = await Challenge.countDocuments({ 
      'participants.user': userId,
      'participants.progress': 100 // Only count completed challenges
    });
    const coursesCount = await Course.countDocuments({
      'enrollments.user': userId,
      'enrollments.progress': 100 // Only count completed courses
    });
    const reviewsCount = await ServiceReview.countDocuments({ user: userId });
    
    // For transport feedbacks, we'll use a placeholder since we don't have the model
    const transportCount = 0;
    
    // Create detailed activity records
    const issueDetails = await getIssueDetails(userId);
    const eventDetails = await getEventDetails(userId);
    const resourceDetails = await getResourceDetails(userId);
    const challengeDetails = await getChallengeDetails(userId);
    const courseDetails = await getCourseDetails(userId);
    const reviewDetails = await getReviewDetails(userId);
    
    // Create new user stats record
    const userStats = await UserStats.create({
      user: userId,
      issuesReported: issuesCount,
      transportFeedbacks: transportCount,
      challengesCompleted: challengesCount,
      reviewsSubmitted: reviewsCount,
      resourcesShared: resourcesCount,
      eventsAttended: eventsCount,
      coursesCompleted: coursesCount,
      issueDetails,
      eventDetails,
      resourceDetails,
      challengeDetails,
      courseDetails,
      reviewDetails,
      transportDetails: [],
      lastUpdated: new Date()
    });
    
    return userStats;
  } catch (error) {
    console.error('Error creating user stats:', error);
    throw error;
  }
};

// Helper function to update user stats from existing data
const updateUserStatsFromData = async (userId, existingStats) => {
  try {
    // Get updated counts from various collections
    const issuesCount = await Issue.countDocuments({ user: userId });
    const eventsCount = await Event.countDocuments({ 
      'participants.user': userId 
    });
    const resourcesCount = await Resource.countDocuments({ owner: userId });
    const challengesCount = await Challenge.countDocuments({ 
      'participants.user': userId,
      'participants.progress': 100
    });
    const coursesCount = await Course.countDocuments({
      'enrollments.user': userId,
      'enrollments.progress': 100
    });
    const reviewsCount = await ServiceReview.countDocuments({ user: userId });
    
    // For transport feedbacks, we'll keep the existing count for now
    const transportCount = existingStats.transportFeedbacks;
    
    // Update detailed activity records
    const issueDetails = await getIssueDetails(userId);
    const eventDetails = await getEventDetails(userId);
    const resourceDetails = await getResourceDetails(userId);
    const challengeDetails = await getChallengeDetails(userId);
    const courseDetails = await getCourseDetails(userId);
    const reviewDetails = await getReviewDetails(userId);
    
    // Update the existing stats
    existingStats.issuesReported = issuesCount;
    existingStats.transportFeedbacks = transportCount;
    existingStats.challengesCompleted = challengesCount;
    existingStats.reviewsSubmitted = reviewsCount;
    existingStats.resourcesShared = resourcesCount;
    existingStats.eventsAttended = eventsCount;
    existingStats.coursesCompleted = coursesCount;
    existingStats.issueDetails = issueDetails;
    existingStats.eventDetails = eventDetails;
    existingStats.resourceDetails = resourceDetails;
    existingStats.challengeDetails = challengeDetails;
    existingStats.courseDetails = courseDetails;
    existingStats.reviewDetails = reviewDetails;
    existingStats.lastUpdated = new Date();
    
    await existingStats.save();
    
    return existingStats;
  } catch (error) {
    console.error('Error updating user stats:', error);
    throw error;
  }
};

// Helper functions to get detailed activity data
const getIssueDetails = async (userId) => {
  const issues = await Issue.find({ user: userId }).select('_id title category status createdAt');
  return issues.map(issue => ({
    issueId: issue._id,
    title: issue.title,
    category: issue.category,
    status: issue.status,
    reportedAt: issue.createdAt
  }));
};

const getEventDetails = async (userId) => {
  const events = await Event.find({ 'participants.user': userId }).select('_id title organizer');
  return events.map(event => {
    const participant = event.participants.find(p => p.user.toString() === userId.toString());
    return {
      eventId: event._id,
      title: event.title,
      organizer: event.organizer,
      attendedAt: participant ? participant.joinedAt : new Date()
    };
  });
};

const getResourceDetails = async (userId) => {
  const resources = await Resource.find({ owner: userId }).select('_id title category createdAt');
  return resources.map(resource => ({
    resourceId: resource._id,
    title: resource.title,
    category: resource.category,
    sharedAt: resource.createdAt
  }));
};

const getChallengeDetails = async (userId) => {
  const challenges = await Challenge.find({ 
    'participants.user': userId,
    'participants.progress': 100
  }).select('_id title category points');
  
  return challenges.map(challenge => {
    const participant = challenge.participants.find(p => p.user.toString() === userId.toString());
    return {
      challengeId: challenge._id,
      title: challenge.title,
      category: challenge.category,
      points: challenge.points,
      completedAt: participant ? participant.completedTasks[participant.completedTasks.length - 1]?.completedAt : new Date()
    };
  });
};

const getCourseDetails = async (userId) => {
  const courses = await Course.find({
    'enrollments.user': userId,
    'enrollments.progress': 100
  }).select('_id title category');
  
  return courses.map(course => {
    const enrollment = course.enrollments.find(e => e.user.toString() === userId.toString());
    return {
      courseId: course._id,
      title: course.title,
      category: course.category,
      completedAt: enrollment ? enrollment.completedLessons[enrollment.completedLessons.length - 1]?.completedAt : new Date()
    };
  });
};

const getReviewDetails = async (userId) => {
  const reviews = await ServiceReview.find({ user: userId }).select('_id serviceId rating createdAt');
  
  // Get service names for each review
  const reviewDetails = [];
  for (const review of reviews) {
    let serviceName = 'Unknown Service';
    try {
      const service = await mongoose.model('Service').findById(review.serviceId);
      if (service) {
        serviceName = service.name;
      }
    } catch (error) {
      console.error('Error fetching service name:', error);
    }
    
    reviewDetails.push({
      reviewId: review._id,
      serviceId: review.serviceId,
      serviceName,
      rating: review.rating,
      submittedAt: review.createdAt
    });
  }
  
  return reviewDetails;
};
