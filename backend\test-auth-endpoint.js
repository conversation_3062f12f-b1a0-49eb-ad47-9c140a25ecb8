const axios = require('axios');

// Test user data
const testUser = {
  name: 'Test User',
  email: `test${Date.now()}@example.com`, // Use timestamp to ensure unique email
  password: 'password123'
};

console.log('Testing registration endpoint with user:', {
  ...testUser,
  password: '********'
});

// Make the API request to our test server
axios.post('http://localhost:5005/api/auth/register', testUser)
  .then(response => {
    console.log('Registration successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
  })
  .catch(error => {
    console.error('Error occurred:');
    
    if (error.response) {
      // The request was made and the server responded with a status code
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received - server might not be running');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
  });
