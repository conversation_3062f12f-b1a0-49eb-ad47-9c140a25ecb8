import { useEffect } from 'react';

/**
 * Custom hook to sync React state with localStorage changes
 * @param {Function} onStorageChange - Callback function to run when storage changes
 * @param {Array} keys - Array of localStorage keys to watch for changes
 */
const useLocalStorageSync = (onStorageChange, keys = ['token', 'user']) => {
  useEffect(() => {
    // Function to handle storage changes
    const handleStorageChange = (e) => {
      console.log('Storage changed:', e.key);
      if (keys.includes(e.key)) {
        console.log('Detected change in watched key:', e.key);
        onStorageChange(e);
      }
    };

    // Function to handle custom auth-change events
    const handleAuthChange = (e) => {
      console.log('Auth change event detected:', e.detail.key);
      if (keys.includes(e.detail.key)) {
        console.log('Detected auth change in watched key:', e.detail.key);
        onStorageChange({
          key: e.detail.key,
          newValue: e.detail.newValue
        });
      }
    };

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('auth-change', handleAuthChange);

    // Listen for userLoggedIn events
    const handleUserLoggedIn = () => {
      console.log('userLoggedIn event detected, triggering storage sync');
      // Force a check of both token and user data
      onStorageChange({ key: 'user' });
      onStorageChange({ key: 'token' });

      // Force a refresh of the page if needed
      const loginLinks = document.querySelectorAll('a[href="/public/direct-login.html"], a[href="/login"], a[href*="login"]');
      const profileDropdown = document.getElementById('profile-dropdown');

      if (loginLinks.length > 0 && !profileDropdown) {
        console.log('Navbar needs updating after login, forcing refresh');
        window.location.reload();
      }
    };

    window.addEventListener('userLoggedIn', handleUserLoggedIn);

    // Check for auth_update parameter in URL which indicates a login from static HTML
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('auth_update')) {
      console.log('Auth update parameter detected, triggering storage sync');
      onStorageChange({ key: 'auth_update' });

      // Remove the parameter from URL to avoid repeated checks
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }

    // Clean up event listeners
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('auth-change', handleAuthChange);
      window.removeEventListener('userLoggedIn', handleUserLoggedIn);
    };
  }, [onStorageChange, keys]);
};

export default useLocalStorageSync;
