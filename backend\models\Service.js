const mongoose = require('mongoose');

const ServiceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: ['waste', 'water', 'parks', 'roads', 'transport', 'health', 'education', 'safety']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Reverse populate with reviews
ServiceSchema.virtual('reviews', {
  ref: 'ServiceReview',
  localField: '_id',
  foreignField: 'serviceId',
  justOne: false
});

// Virtual for average rating
ServiceSchema.virtual('averageRating').get(function() {
  if (this.reviews && this.reviews.length > 0) {
    const sum = this.reviews.reduce((total, review) => total + review.rating, 0);
    return (sum / this.reviews.length).toFixed(1);
  }
  return 0;
});

// Virtual for review count
ServiceSchema.virtual('reviewCount').get(function() {
  return this.reviews ? this.reviews.length : 0;
});

module.exports = mongoose.model('Service', ServiceSchema);
