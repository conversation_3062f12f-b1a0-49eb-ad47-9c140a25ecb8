const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Ensure we're connecting to the urban-pulse database
const dbURI = process.env.MONGO_URI ? 
  (process.env.MONGO_URI.endsWith('/') ? `${process.env.MONGO_URI}urban-pulse` : `${process.env.MONGO_URI}/urban-pulse`) : 
  'mongodb://localhost:27017/urban-pulse';

console.log('Attempting to connect to MongoDB...');
console.log(`Connection URI: ${dbURI}`);

// Connect to MongoDB
mongoose.connect(dbURI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(conn => {
  console.log(`MongoDB Connected: ${conn.connection.host}`);
  console.log(`Database Name: ${conn.connection.name}`);
  console.log('Connection successful!');
  process.exit(0);
})
.catch(error => {
  console.error(`Error: ${error.message}`);
  console.error('Could not connect to MongoDB. Please make sure MongoDB is running.');
  process.exit(1);
});
