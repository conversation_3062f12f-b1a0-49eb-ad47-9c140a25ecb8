const mongoose = require('mongoose');

// Connect to MongoDB
const connectDB = async () => {
  try {
    // Ensure we're connecting to the urban-pulse database
    const dbURI = process.env.MONGO_URI ?
      (process.env.MONGO_URI.endsWith('/') ? `${process.env.MONGO_URI}urban-pulse` : `${process.env.MONGO_URI}/urban-pulse`) :
      'mongodb://localhost:27017/urban-pulse';

    const conn = await mongoose.connect(dbURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`MongoDB Connected: ${conn.connection.host}`);
    console.log(`Database Name: ${conn.connection.name}`);
  } catch (error) {
    console.error(`Error: ${error.message}`);
    console.error('Could not connect to MongoDB. Please make sure MongoDB is running.');
    process.exit(1);
  }
};

module.exports = connectDB;
