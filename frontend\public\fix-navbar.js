// This script forcibly updates the navbar based on authentication state
(function() {
    console.log('Fix Navbar script loaded');

    // Function to check if user is logged in
    function isUserLoggedIn() {
        const token = localStorage.getItem('token');
        const userDataString = localStorage.getItem('user');

        if (token && userDataString) {
            try {
                // Parse user data
                const userData = JSON.parse(userDataString);
                console.log('User is logged in as:', userData.name || 'User');
                return {
                    isLoggedIn: true,
                    userData: userData
                };
            } catch (error) {
                console.error('Error parsing user data:', error);
                return {
                    isLoggedIn: false,
                    userData: null
                };
            }
        }

        return {
            isLoggedIn: false,
            userData: null
        };
    }

    // Function to check if we're in a React application
    function isReactApp() {
        return !!document.getElementById('root') && 
               (document.getElementById('root').childElementCount > 0);
    }

    // Function to create authenticated navbar elements
    function createAuthenticatedNavElements(userData) {
        // Create dashboard link
        const dashboardLink = document.createElement('li');
        dashboardLink.className = 'nav-item';
        const dashboardAnchor = document.createElement('a');
        dashboardAnchor.className = 'nav-link';
        dashboardAnchor.href = '/dashboard';
        dashboardAnchor.textContent = 'My Dashboard';
        dashboardLink.appendChild(dashboardAnchor);

        // Create profile dropdown
        const profileDropdown = document.createElement('li');
        profileDropdown.className = 'nav-item dropdown';
        
        const dropdownToggle = document.createElement('a');
        dropdownToggle.className = 'nav-link dropdown-toggle';
        dropdownToggle.href = '#';
        dropdownToggle.id = 'profile-dropdown';
        dropdownToggle.setAttribute('role', 'button');
        dropdownToggle.setAttribute('data-bs-toggle', 'dropdown');
        dropdownToggle.setAttribute('aria-expanded', 'false');
        dropdownToggle.textContent = userData.name || 'Profile';
        
        const dropdownMenu = document.createElement('ul');
        dropdownMenu.className = 'dropdown-menu';
        dropdownMenu.setAttribute('aria-labelledby', 'profile-dropdown');
        
        const profileItem = document.createElement('li');
        const profileLink = document.createElement('a');
        profileLink.className = 'dropdown-item';
        profileLink.href = '/profile';
        profileLink.textContent = 'My Profile';
        profileItem.appendChild(profileLink);
        
        const divider = document.createElement('li');
        divider.innerHTML = '<hr class="dropdown-divider">';
        
        const logoutItem = document.createElement('li');
        const logoutLink = document.createElement('a');
        logoutLink.className = 'dropdown-item';
        logoutLink.href = '#';
        logoutLink.textContent = 'Logout';
        logoutLink.addEventListener('click', function(e) {
            e.preventDefault();
            // Clear auth data
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            // Redirect to home
            window.location.href = '/';
        });
        logoutItem.appendChild(logoutLink);
        
        // Assemble dropdown
        dropdownMenu.appendChild(profileItem);
        dropdownMenu.appendChild(divider);
        dropdownMenu.appendChild(logoutItem);
        
        profileDropdown.appendChild(dropdownToggle);
        profileDropdown.appendChild(dropdownMenu);
        
        return { dashboardLink, profileDropdown };
    }

    // Function to update the navbar
    function updateNavbar() {
        const { isLoggedIn, userData } = isUserLoggedIn();

        // Check if we're in a React application
        const isReact = isReactApp();
        if (isReact) {
            console.log('React application detected, dispatching auth event');
            // For React apps, we'll dispatch an event to notify about auth state
            const event = new CustomEvent('auth-state-change', {
                detail: { isLoggedIn, userData }
            });
            window.dispatchEvent(event);
            
            // Also try to force a React component update
            if (isLoggedIn) {
                console.log('Attempting to force React component update');
                // This is a hack to force React to re-render
                const navbarToggler = document.querySelector('.navbar-toggler');
                if (navbarToggler) {
                    // Click the navbar toggler to force a re-render
                    navbarToggler.click();
                    setTimeout(() => {
                        navbarToggler.click();
                    }, 100);
                }
            }
            return;
        }

        if (isLoggedIn) {
            console.log('Updating navbar for authenticated user');

            // Find the navbar elements - try multiple selectors to ensure we find the navbar
            const navbarNav = document.querySelector('.navbar-nav') ||
                             document.querySelector('.navbar .nav') ||
                             document.querySelector('nav .nav');

            if (!navbarNav) {
                console.error('Could not find navbar-nav element');
                return;
            }

            // Check if we already have the authenticated elements
            const existingDashboard = document.querySelector('a.nav-link[href="/dashboard"]');
            const existingProfileDropdown = document.querySelector('#profile-dropdown');

            if (existingDashboard && existingProfileDropdown) {
                console.log('Authenticated navbar elements already exist');
                return;
            }

            // Find and hide login/signup buttons
            const loginLinks = document.querySelectorAll('a[href="/public/direct-login.html"], a[href="/login"], a[href*="login"]');
            loginLinks.forEach(link => {
                if (link) {
                    console.log('Hiding login link:', link);
                    const parentLi = link.closest('li') || link.parentElement;
                    if (parentLi) {
                        parentLi.style.display = 'none';
                    } else {
                        link.style.display = 'none';
                    }
                }
            });

            // Find and hide signup buttons
            const signupButtons = document.querySelectorAll('a[href="/public/register-redirect.html"], a[href="/register"], button:contains("Sign Up")');
            signupButtons.forEach(button => {
                if (button) {
                    console.log('Hiding signup button:', button);
                    const parentLi = button.closest('li') || button.parentElement;
                    if (parentLi) {
                        parentLi.style.display = 'none';
                    } else {
                        button.style.display = 'none';
                    }
                }
            });

            // Find and hide admin login links
            const adminLoginLinks = document.querySelectorAll('a[href="/admin-login"]');
            adminLoginLinks.forEach(link => {
                if (link) {
                    console.log('Hiding admin login link:', link);
                    const parentLi = link.closest('li') || link.parentElement;
                    if (parentLi) {
                        parentLi.style.display = 'none';
                    } else {
                        link.style.display = 'none';
                    }
                }
            });

            // Create authenticated elements
            const { dashboardLink, profileDropdown } = createAuthenticatedNavElements(userData);

            // Add the new elements to the navbar
            navbarNav.appendChild(dashboardLink);
            navbarNav.appendChild(profileDropdown);

            console.log('Added authenticated elements to navbar');

            // Initialize Bootstrap dropdown
            if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
                dropdownElementList.map(function (dropdownToggleEl) {
                    return new bootstrap.Dropdown(dropdownToggleEl);
                });
                console.log('Initialized Bootstrap dropdowns');
            } else {
                console.warn('Bootstrap not available, dropdowns may not work');
            }
        } else {
            console.log('User is not logged in, no navbar update needed');
        }
    }

    // Function to initialize the script
    function init() {
        console.log('Initializing fix-navbar.js');
        
        // Update the navbar immediately
        updateNavbar();
        
        // Also listen for storage events to update the navbar when auth state changes
        window.addEventListener('storage', function(e) {
            if (e.key === 'token' || e.key === 'user') {
                console.log('Auth storage changed, updating navbar');
                updateNavbar();
            }
        });
        
        // Also listen for custom auth events
        window.addEventListener('auth-state-change', function(e) {
            console.log('Auth state change event received, updating navbar');
            updateNavbar();
        });
        
        // Also listen for userLoggedIn events
        window.addEventListener('userLoggedIn', function(e) {
            console.log('userLoggedIn event received, updating navbar');
            updateNavbar();
        });
    }
    
    // Run initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // If DOM is already loaded, run init immediately
        init();
    }
})();
