<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Link - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .icon {
            font-size: 4rem;
            color: #0d6efd;
            margin-bottom: 1rem;
        }
        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.25rem;
        }
        .card {
            margin-top: 2rem;
            border: none;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: none;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" class="bi bi-speedometer2" viewBox="0 0 16 16">
                <path d="M8 4a.5.5 0 0 1 .5.5V6a.5.5 0 0 1-1 0V4.5A.5.5 0 0 1 8 4zM3.732 5.732a.5.5 0 0 1 .707 0l.915.914a.5.5 0 1 1-.708.708l-.914-.915a.5.5 0 0 1 0-.707zM2 10a.5.5 0 0 1 .5-.5h1.586a.5.5 0 0 1 0 1H2.5A.5.5 0 0 1 2 10zm9.5 0a.5.5 0 0 1 .5-.5h1.5a.5.5 0 0 1 0 1H12a.5.5 0 0 1-.5-.5zm.754-4.246a.389.389 0 0 0-.527-.02L7.547 9.31a.91.91 0 1 0 1.302 1.258l3.434-4.297a.389.389 0 0 0-.029-.518z"/>
                <path fill-rule="evenodd" d="M0 10a8 8 0 1 1 15.547 2.661c-.442 1.253-1.845 1.602-2.932 1.25C11.309 13.488 9.475 13 8 13c-1.474 0-3.31.488-4.615.911-1.087.352-2.49.003-2.932-1.25A7.988 7.988 0 0 1 0 10zm8-7a7 7 0 0 0-6.603 9.329c.203.575.923.876 1.68.63C4.397 12.533 6.358 12 8 12s3.604.532 4.923.96c.757.245 1.477-.056 1.68-.631A7 7 0 0 0 8 3z"/>
            </svg>
        </div>
        <h1 class="mb-3">Dashboard Access</h1>
        <p class="lead mb-4">
            Access your personalized dashboard to view your activity, notifications, and more.
        </p>
        
        <div id="auth-status"></div>
        
        <div class="d-grid gap-3">
            <a href="/public/go-to-dashboard.html" class="btn btn-primary btn-lg">
                Go to Dashboard
            </a>
            <a href="/" class="btn btn-outline-secondary">
                Return to Home Page
            </a>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                Having trouble with the navbar?
            </div>
            <div class="card-body">
                <p>If you're logged in but don't see your profile in the navbar, try our fix:</p>
                <a href="/public/inject-auth.html" class="btn btn-outline-primary">
                    Fix Navbar Display
                </a>
            </div>
        </div>
    </div>

    <script>
        // Check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('token');
            const userDataString = localStorage.getItem('user');
            
            if (token && userDataString) {
                try {
                    const userData = JSON.parse(userDataString);
                    document.getElementById('auth-status').innerHTML = `
                        <div class="alert alert-success mb-4">
                            Logged in as: <strong>${userData.name || 'User'}</strong>
                        </div>
                    `;
                } catch (error) {
                    document.getElementById('auth-status').innerHTML = `
                        <div class="alert alert-warning mb-4">
                            Authentication issue detected. Please <a href="/public/direct-login.html">login again</a>.
                        </div>
                    `;
                }
            } else {
                document.getElementById('auth-status').innerHTML = `
                    <div class="alert alert-info mb-4">
                        You need to <a href="/public/direct-login.html">login</a> to access your dashboard.
                    </div>
                `;
            }
        }
        
        // Run the check
        checkAuthStatus();
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
