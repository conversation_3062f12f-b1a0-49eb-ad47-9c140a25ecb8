.notification-dropdown {
  width: 350px;
  max-width: 100vw;
}

@media (max-width: 576px) {
  .notification-dropdown {
    width: 300px;
  }
}

.notification-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-item {
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.notification-item.unread {
  background-color: rgba(13, 110, 253, 0.05);
}

.notification-timestamp {
  font-size: 0.75rem;
  color: #6c757d;
}

.notification-message {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

/* Interest notification styles */
.notification-interest {
  border-left: 3px solid #dc3545 !important;
}

.notification-interest.unread {
  background-color: rgba(220, 53, 69, 0.05) !important;
}

.interest-notification-details {
  background-color: rgba(220, 53, 69, 0.05) !important;
  border-color: rgba(220, 53, 69, 0.2) !important;
}

/* Animation for new notifications */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.notification-interest.unread {
  animation: pulse 2s infinite;
}
