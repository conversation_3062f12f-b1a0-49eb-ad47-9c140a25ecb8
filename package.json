{"name": "urban-pulse", "version": "0.1.0", "description": "A comprehensive platform for smart urban living", "main": "index.js", "scripts": {"start": "node backend/server.js", "server": "cd backend && npm run dev", "client": "cd frontend && npm run dev", "dev": "concurrently \"npm run server\" \"npm run client\"", "install-all": "npm install && cd frontend && npm install && cd ../backend && npm install", "build": "cd frontend && npm run build"}, "keywords": ["urban", "sustainability", "community", "smart-city"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.0.1"}, "dependencies": {"bcrypt": "^5.1.1", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1"}}