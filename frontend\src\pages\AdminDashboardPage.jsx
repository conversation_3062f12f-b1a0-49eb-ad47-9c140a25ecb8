import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, But<PERSON>, Nav, Tab, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import { FaUsers, FaExclamationTriangle, FaBus, FaLeaf, FaStar, FaExchangeAlt, FaTree, FaGraduationCap } from 'react-icons/fa';
import { getAllIssues, getAllTransportFeedback, getAllUsers, getDashboardStats, updateIssueStatus } from '../services/adminService';
import socketService from '../services/socketService';
import notificationService from '../services/notificationService';

const AdminDashboardPage = () => {
  const [stats, setStats] = useState({
    users: 0,
    issues: 0,
    feedbacks: 0,
    challenges: 0,
    reviews: 0,
    resources: 0,
    events: 0,
    courses: 0
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // State for status update modal
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [newStatus, setNewStatus] = useState('');
  const [statusComment, setStatusComment] = useState('');
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [statusUpdateError, setStatusUpdateError] = useState('');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await getDashboardStats();
        if (response.success) {
          setStats(response.data);
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        setError('Failed to load dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // State for issues, transport feedback, and users
  const [recentIssues, setRecentIssues] = useState([]);
  const [transportFeedback, setTransportFeedback] = useState([]);
  const [users, setUsers] = useState([]);

  const [issuesLoading, setIssuesLoading] = useState(true);
  const [transportLoading, setTransportLoading] = useState(true);
  const [usersLoading, setUsersLoading] = useState(true);

  const [issuesError, setIssuesError] = useState('');
  const [transportError, setTransportError] = useState('');
  const [usersError, setUsersError] = useState('');

  // Fetch issues
  useEffect(() => {
    const fetchIssues = async () => {
      try {
        setIssuesLoading(true);
        const response = await getAllIssues();
        if (response.success) {
          // If the API returns real data, use it
          if (response.data && response.data.length > 0) {
            setRecentIssues(response.data.slice(0, 5));
          } else {
            // Otherwise use mock data
            setRecentIssues([
              { id: 1, title: 'Pothole on Main Street', category: 'Roads', status: 'Open', date: '2023-04-15', reporter: 'John Doe' },
              { id: 2, title: 'Broken Streetlight', category: 'Lighting', status: 'In Progress', date: '2023-04-14', reporter: 'Jane Smith' },
              { id: 3, title: 'Overflowing Trash Bin', category: 'Waste', status: 'Resolved', date: '2023-04-13', reporter: 'Mike Johnson' },
              { id: 4, title: 'Graffiti on Public Building', category: 'Vandalism', status: 'Open', date: '2023-04-12', reporter: 'Sarah Williams' },
              { id: 5, title: 'Fallen Tree Branch', category: 'Parks', status: 'In Progress', date: '2023-04-11', reporter: 'David Brown' }
            ]);
          }
        }
      } catch (error) {
        console.error('Error fetching issues:', error);
        setIssuesError('Failed to load issues');
        // Use mock data as fallback
        setRecentIssues([
          { id: 1, title: 'Pothole on Main Street', category: 'Roads', status: 'Open', date: '2023-04-15', reporter: 'John Doe' },
          { id: 2, title: 'Broken Streetlight', category: 'Lighting', status: 'In Progress', date: '2023-04-14', reporter: 'Jane Smith' },
          { id: 3, title: 'Overflowing Trash Bin', category: 'Waste', status: 'Resolved', date: '2023-04-13', reporter: 'Mike Johnson' },
          { id: 4, title: 'Graffiti on Public Building', category: 'Vandalism', status: 'Open', date: '2023-04-12', reporter: 'Sarah Williams' },
          { id: 5, title: 'Fallen Tree Branch', category: 'Parks', status: 'In Progress', date: '2023-04-11', reporter: 'David Brown' }
        ]);
      } finally {
        setIssuesLoading(false);
      }
    };

    fetchIssues();
  }, []);

  // Fetch transport feedback
  useEffect(() => {
    const fetchTransportFeedback = async () => {
      try {
        setTransportLoading(true);
        const response = await getAllTransportFeedback();
        if (response.success) {
          // If the API returns real data, use it
          if (response.data && response.data.length > 0) {
            setTransportFeedback(response.data.slice(0, 5));
          } else {
            // Otherwise use mock data
            setTransportFeedback([
              { id: 1, title: 'Bus Route 42 Delay', category: 'Schedule', votes: 28, date: '2023-04-15', status: 'Under Review' },
              { id: 2, title: 'Add Stop at Central Park', category: 'Routes', votes: 45, date: '2023-04-13', status: 'Approved' },
              { id: 3, title: 'Overcrowding on Line 7', category: 'Capacity', votes: 32, date: '2023-04-12', status: 'Under Review' },
              { id: 4, title: 'Extend Weekend Service Hours', category: 'Schedule', votes: 67, date: '2023-04-10', status: 'Implemented' },
              { id: 5, title: 'Improve Bus Cleanliness', category: 'Maintenance', votes: 23, date: '2023-04-09', status: 'Under Review' }
            ]);
          }
        }
      } catch (error) {
        console.error('Error fetching transport feedback:', error);
        setTransportError('Failed to load transport feedback');
        // Use mock data as fallback
        setTransportFeedback([
          { id: 1, title: 'Bus Route 42 Delay', category: 'Schedule', votes: 28, date: '2023-04-15', status: 'Under Review' },
          { id: 2, title: 'Add Stop at Central Park', category: 'Routes', votes: 45, date: '2023-04-13', status: 'Approved' },
          { id: 3, title: 'Overcrowding on Line 7', category: 'Capacity', votes: 32, date: '2023-04-12', status: 'Under Review' },
          { id: 4, title: 'Extend Weekend Service Hours', category: 'Schedule', votes: 67, date: '2023-04-10', status: 'Implemented' },
          { id: 5, title: 'Improve Bus Cleanliness', category: 'Maintenance', votes: 23, date: '2023-04-09', status: 'Under Review' }
        ]);
      } finally {
        setTransportLoading(false);
      }
    };

    fetchTransportFeedback();
  }, []);

  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setUsersLoading(true);
        const response = await getAllUsers();
        if (response.success) {
          // If the API returns real data, use it
          if (response.data && response.data.length > 0) {
            setUsers(response.data.slice(0, 5));
          } else {
            // Otherwise use mock data
            setUsers([
              { id: 1, name: 'John Doe', email: '<EMAIL>', joined: '2023-01-15', role: 'User', status: 'Active' },
              { id: 2, name: 'Jane Smith', email: '<EMAIL>', joined: '2023-02-20', role: 'User', status: 'Active' },
              { id: 3, name: 'Admin User', email: '<EMAIL>', joined: '2022-12-01', role: 'Admin', status: 'Active' },
              { id: 4, name: 'Mike Johnson', email: '<EMAIL>', joined: '2023-03-10', role: 'User', status: 'Inactive' },
              { id: 5, name: 'Sarah Williams', email: '<EMAIL>', joined: '2023-03-25', role: 'User', status: 'Active' }
            ]);
          }
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        setUsersError('Failed to load users');
        // Use mock data as fallback
        setUsers([
          { id: 1, name: 'John Doe', email: '<EMAIL>', joined: '2023-01-15', role: 'User', status: 'Active' },
          { id: 2, name: 'Jane Smith', email: '<EMAIL>', joined: '2023-02-20', role: 'User', status: 'Active' },
          { id: 3, name: 'Admin User', email: '<EMAIL>', joined: '2022-12-01', role: 'Admin', status: 'Active' },
          { id: 4, name: 'Mike Johnson', email: '<EMAIL>', joined: '2023-03-10', role: 'User', status: 'Inactive' },
          { id: 5, name: 'Sarah Williams', email: '<EMAIL>', joined: '2023-03-25', role: 'User', status: 'Active' }
        ]);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Handle opening the status update modal
  const handleUpdateStatus = (issue) => {
    setSelectedIssue(issue);
    setNewStatus(issue.status);
    setStatusComment('');
    setStatusUpdateError('');
    setShowStatusModal(true);
  };

  // Handle closing the status update modal
  const handleCloseStatusModal = () => {
    setShowStatusModal(false);
    setSelectedIssue(null);
    setNewStatus('');
    setStatusComment('');
    setStatusUpdateError('');
  };

  // Handle submitting the status update
  const handleSubmitStatusUpdate = async (e) => {
    e.preventDefault();

    if (!selectedIssue || !newStatus) {
      setStatusUpdateError('Please select a status');
      return;
    }

    try {
      setUpdatingStatus(true);
      setStatusUpdateError('');

      // Call the API to update the issue status
      const response = await updateIssueStatus(selectedIssue.id, {
        status: newStatus,
        comment: statusComment
      });

      if (response.success) {
        // Update the issue in the local state
        setRecentIssues(prevIssues =>
          prevIssues.map(issue =>
            issue.id === selectedIssue.id
              ? { ...issue, status: newStatus }
              : issue
          )
        );

        // Emit the status update via socket
        socketService.emitIssueStatusUpdate(selectedIssue.id, newStatus, statusComment);

        // Show a notification
        notificationService.showNotification(
          `Issue Status Updated`,
          {
            body: `Issue "${selectedIssue.title}" status changed to ${newStatus}`,
            type: 'success'
          }
        );

        // Close the modal
        handleCloseStatusModal();
      } else {
        setStatusUpdateError(response.error || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating issue status:', error);
      setStatusUpdateError(error.message || 'Failed to update status');
    } finally {
      setUpdatingStatus(false);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Open':
        return <Badge bg="danger">Open</Badge>;
      case 'In Progress':
        return <Badge bg="warning">In Progress</Badge>;
      case 'Resolved':
        return <Badge bg="success">Resolved</Badge>;
      case 'Under Review':
        return <Badge bg="info">Under Review</Badge>;
      case 'Approved':
        return <Badge bg="primary">Approved</Badge>;
      case 'Implemented':
        return <Badge bg="success">Implemented</Badge>;
      case 'Active':
        return <Badge bg="success">Active</Badge>;
      case 'Inactive':
        return <Badge bg="secondary">Inactive</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  return (
    <Container fluid>
      <h1 className="mb-4">Admin Dashboard</h1>

      {error && <Alert variant="danger">{error}</Alert>}

      {/* Stats Cards */}
      <Row className="g-4 mb-4">
        {loading ? (
          <Col className="text-center py-5">
            <Spinner animation="border" role="status" variant="primary">
              <span className="visually-hidden">Loading...</span>
            </Spinner>
            <p className="mt-2">Loading dashboard statistics...</p>
          </Col>
        ) : (
          <>
            <Col md={3}>
              <Card className="border-0 shadow-sm">
                <Card.Body className="d-flex align-items-center">
                  <div className="bg-primary bg-opacity-10 p-3 rounded me-3">
                    <FaUsers className="text-primary fs-3" />
                  </div>
                  <div>
                    <h6 className="mb-0">Total Users</h6>
                    <h3 className="mb-0">{stats.users}</h3>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="border-0 shadow-sm">
                <Card.Body className="d-flex align-items-center">
                  <div className="bg-danger bg-opacity-10 p-3 rounded me-3">
                    <FaExclamationTriangle className="text-danger fs-3" />
                  </div>
                  <div>
                    <h6 className="mb-0">Issues Reported</h6>
                    <h3 className="mb-0">{stats.issues}</h3>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="border-0 shadow-sm">
                <Card.Body className="d-flex align-items-center">
                  <div className="bg-info bg-opacity-10 p-3 rounded me-3">
                    <FaBus className="text-info fs-3" />
                  </div>
                  <div>
                    <h6 className="mb-0">Transport Feedback</h6>
                    <h3 className="mb-0">{stats.feedbacks}</h3>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="border-0 shadow-sm">
                <Card.Body className="d-flex align-items-center">
                  <div className="bg-success bg-opacity-10 p-3 rounded me-3">
                    <FaLeaf className="text-success fs-3" />
                  </div>
                  <div>
                    <h6 className="mb-0">Active Challenges</h6>
                    <h3 className="mb-0">{stats.challenges}</h3>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </>
        )}
      </Row>

      <Tab.Container defaultActiveKey="issues">
        <Row>
          <Col md={2} className="mb-4">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-0">
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="issues" className="rounded-0 px-4 py-3">Issues</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="transport" className="rounded-0 px-4 py-3">Transport Feedback</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="users" className="rounded-0 px-4 py-3">Users</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="challenges" className="rounded-0 px-4 py-3">Challenges</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="reviews" className="rounded-0 px-4 py-3">Reviews</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="resources" className="rounded-0 px-4 py-3">Resources</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="events" className="rounded-0 px-4 py-3">Events</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="courses" className="rounded-0 px-4 py-3">Courses</Nav.Link>
                  </Nav.Item>
                </Nav>
              </Card.Body>
            </Card>
          </Col>

          <Col md={10}>
            <Card className="border-0 shadow-sm">
              <Card.Body>
                <Tab.Content>
                  <Tab.Pane eventKey="issues">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Recent Issues</h4>
                      <Button variant="outline-primary">View All Issues</Button>
                    </div>

                    {issuesError && <Alert variant="danger">{issuesError}</Alert>}

                    {issuesLoading ? (
                      <div className="text-center py-5">
                        <Spinner animation="border" role="status" variant="primary">
                          <span className="visually-hidden">Loading...</span>
                        </Spinner>
                        <p className="mt-2">Loading issues...</p>
                      </div>
                    ) : (
                      <Table responsive hover>
                        <thead>
                          <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Reporter</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {recentIssues.map((issue) => (
                            <tr key={issue.id}>
                              <td>#{issue.id}</td>
                              <td>{issue.title}</td>
                              <td>{issue.category}</td>
                              <td>{getStatusBadge(issue.status)}</td>
                              <td>{issue.date}</td>
                              <td>{issue.reporter}</td>
                              <td>
                                <Button variant="outline-primary" size="sm" className="me-2">View</Button>
                                <Button
                                  variant="outline-success"
                                  size="sm"
                                  onClick={() => handleUpdateStatus(issue)}
                                >
                                  Update Status
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    )}
                  </Tab.Pane>

                  <Tab.Pane eventKey="transport">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Transport Feedback</h4>
                      <Button variant="outline-primary">View All Feedback</Button>
                    </div>

                    {transportError && <Alert variant="danger">{transportError}</Alert>}

                    {transportLoading ? (
                      <div className="text-center py-5">
                        <Spinner animation="border" role="status" variant="primary">
                          <span className="visually-hidden">Loading...</span>
                        </Spinner>
                        <p className="mt-2">Loading transport feedback...</p>
                      </div>
                    ) : (
                      <Table responsive hover>
                        <thead>
                          <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Votes</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {transportFeedback.map((feedback) => (
                            <tr key={feedback.id}>
                              <td>#{feedback.id}</td>
                              <td>{feedback.title}</td>
                              <td>{feedback.category}</td>
                              <td>{feedback.votes}</td>
                              <td>{getStatusBadge(feedback.status)}</td>
                              <td>{feedback.date}</td>
                              <td>
                                <Button variant="outline-primary" size="sm" className="me-2">View</Button>
                                <Button variant="outline-success" size="sm">Update</Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    )}
                  </Tab.Pane>

                  <Tab.Pane eventKey="users">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">User Management</h4>
                      <Button variant="outline-primary">Add New User</Button>
                    </div>

                    {usersError && <Alert variant="danger">{usersError}</Alert>}

                    {usersLoading ? (
                      <div className="text-center py-5">
                        <Spinner animation="border" role="status" variant="primary">
                          <span className="visually-hidden">Loading...</span>
                        </Spinner>
                        <p className="mt-2">Loading users...</p>
                      </div>
                    ) : (
                      <Table responsive hover>
                        <thead>
                          <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Joined</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {users.map((user) => (
                            <tr key={user._id || user.id}>
                              <td>#{user._id || user.id}</td>
                              <td>{user.name}</td>
                              <td>{user.email}</td>
                              <td>{new Date(user.createdAt || user.joined).toLocaleDateString()}</td>
                              <td>{user.isAdmin ? 'Admin' : 'User'}</td>
                              <td>{getStatusBadge('Active')}</td>
                              <td>
                                <Button variant="outline-primary" size="sm" className="me-2">Edit</Button>
                                <Button variant="outline-danger" size="sm">Deactivate</Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    )}
                  </Tab.Pane>

                  <Tab.Pane eventKey="challenges">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Sustainable Living Challenges</h4>
                      <Button variant="outline-primary">Create New Challenge</Button>
                    </div>

                    <p className="text-muted">Manage sustainable living challenges here.</p>
                  </Tab.Pane>

                  <Tab.Pane eventKey="reviews">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Public Service Reviews</h4>
                      <Button variant="outline-primary">View All Reviews</Button>
                    </div>

                    <p className="text-muted">Manage public service reviews here.</p>
                  </Tab.Pane>

                  <Tab.Pane eventKey="resources">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Resource Sharing</h4>
                      <Button variant="outline-primary">View All Resources</Button>
                    </div>

                    <p className="text-muted">Manage shared resources here.</p>
                  </Tab.Pane>

                  <Tab.Pane eventKey="events">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Community Events</h4>
                      <Button variant="outline-primary">Create New Event</Button>
                    </div>

                    <p className="text-muted">Manage community events here.</p>
                  </Tab.Pane>

                  <Tab.Pane eventKey="courses">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Learning Hub Courses</h4>
                      <Button variant="outline-primary">Create New Course</Button>
                    </div>

                    <p className="text-muted">Manage learning hub courses here.</p>
                  </Tab.Pane>
                </Tab.Content>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Tab.Container>

      {/* Status Update Modal */}
      <Modal show={showStatusModal} onHide={handleCloseStatusModal}>
        <Modal.Header closeButton>
          <Modal.Title>Update Issue Status</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedIssue && (
            <Form onSubmit={handleSubmitStatusUpdate}>
              <Form.Group className="mb-3">
                <Form.Label>Issue</Form.Label>
                <Form.Control
                  type="text"
                  value={selectedIssue.title}
                  disabled
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Current Status</Form.Label>
                <Form.Control
                  type="text"
                  value={selectedIssue.status}
                  disabled
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>New Status</Form.Label>
                <Form.Select
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                  required
                >
                  <option value="">Select Status</option>
                  <option value="Open">Open</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Resolved">Resolved</option>
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Comment (Optional)</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={statusComment}
                  onChange={(e) => setStatusComment(e.target.value)}
                  placeholder="Add a comment about this status update"
                />
              </Form.Group>

              {statusUpdateError && (
                <Alert variant="danger">{statusUpdateError}</Alert>
              )}

              <div className="d-flex justify-content-end">
                <Button variant="secondary" className="me-2" onClick={handleCloseStatusModal}>
                  Cancel
                </Button>
                <Button
                  variant="success"
                  type="submit"
                  disabled={updatingStatus || !newStatus}
                >
                  {updatingStatus ? 'Updating...' : 'Update Status'}
                </Button>
              </div>
            </Form>
          )}
        </Modal.Body>
      </Modal>
    </Container>
  );
};

export default AdminDashboardPage;
