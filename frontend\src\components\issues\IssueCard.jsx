import React from 'react';
import { <PERSON>, Badge, Button } from 'react-bootstrap';
import { FaMapMarkerAlt, FaCalendarAlt, FaTag, FaExclamationTriangle } from 'react-icons/fa';
import { format } from 'date-fns';

const IssueCard = ({ issue, onClick, className = '' }) => {
  // Get status badge
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Open':
        return <Badge bg="danger">Open</Badge>;
      case 'In Progress':
        return <Badge bg="warning">In Progress</Badge>;
      case 'Resolved':
        return <Badge bg="success">Resolved</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Card className={`border-0 shadow-sm h-100 ${className}`}>
      {issue.photo && (
        <div className="position-relative">
          <Card.Img 
            variant="top" 
            src={issue.photo} 
            alt={issue.title}
            style={{ height: '160px', objectFit: 'cover' }}
          />
          <div 
            className="position-absolute top-0 end-0 m-2"
            style={{ zIndex: 1 }}
          >
            {getStatusBadge(issue.status)}
          </div>
        </div>
      )}
      <Card.Body>
        <Card.Title className="mb-1">{issue.title}</Card.Title>
        <div className="d-flex align-items-center mb-2 text-muted small">
          <FaTag className="me-1" />
          <span>{issue.category}</span>
        </div>
        <Card.Text className="mb-3">
          {issue.description?.substring(0, 100) || 'No description'}
          {issue.description?.length > 100 ? '...' : ''}
        </Card.Text>
        <div className="d-flex align-items-center mb-2 small text-muted">
          <FaMapMarkerAlt className="me-1" />
          <span>{issue.location}</span>
        </div>
        <div className="d-flex align-items-center mb-3 small text-muted">
          <FaCalendarAlt className="me-1" />
          <span>Reported on {formatDate(issue.createdAt || issue.date)}</span>
        </div>
        <Button 
          variant="outline-primary" 
          size="sm" 
          onClick={() => onClick && onClick(issue)}
          className="w-100"
        >
          View Details
        </Button>
      </Card.Body>
    </Card>
  );
};

export default IssueCard;
