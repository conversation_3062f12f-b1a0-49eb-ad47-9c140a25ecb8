const axios = require('axios');

// Test user data
const testUser = {
  name: 'Test User',
  email: `test${Date.now()}@example.com`, // Use timestamp to ensure unique email
  password: 'password123'
};

console.log('Testing registration endpoint with user:', {
  ...testUser,
  password: '********'
});

// Make the API request
axios.post('http://localhost:5002/api/auth/register', testUser)
  .then(response => {
    console.log('Registration successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);

    // Test login with the same credentials
    console.log('\nTesting login with the same credentials...');
    return axios.post('http://localhost:5002/api/auth/login', {
      email: testUser.email,
      password: testUser.password
    });
  })
  .then(response => {
    console.log('Login successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', {
      ...response.data,
      token: response.data.token ? 'TOKEN_EXISTS' : 'NO_TOKEN'
    });

    // Test getting user profile
    console.log('\nTesting get user profile...');
    return axios.get('http://localhost:5002/api/auth/me', {
      headers: {
        Authorization: `Bearer ${response.data.token}`
      }
    });
  })
  .then(response => {
    console.log('Get profile successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);

    console.log('\nAll tests passed successfully!');
  })
  .catch(error => {
    console.error('Error occurred:');

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
  });
