import React from 'react';
import { Container, <PERSON>, Col, Card } from 'react-bootstrap';

const TestPage = () => {
  return (
    <Container>
      <Row className="justify-content-center mt-5">
        <Col md={8}>
          <Card className="shadow-sm">
            <Card.Body className="p-5 text-center">
              <h1 className="mb-4">Test Page</h1>
              <p className="lead">
                This is a simple test page to verify that routing is working correctly.
              </p>
              <p>
                If you can see this page, then the routing system is functioning properly.
              </p>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default TestPage;
