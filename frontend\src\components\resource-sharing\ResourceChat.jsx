import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, But<PERSON>, Spinner } from 'react-bootstrap';
import { FaPaperPlane, FaUser } from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import socketService from '../../services/socketService';
import api from '../../utils/api';
import './ResourceChat.css';

const ResourceChat = ({ resourceId, resourceTitle, ownerId, ownerName }) => {
  const { currentUser } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef(null);
  const [error, setError] = useState(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Load messages when component mounts
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);
        setError(null);

        // In a real app, this would be an API call
        // const response = await api.get(`/resources/${resourceId}/messages`);
        // setMessages(response.data.data);

        // For demo purposes, use realistic data
        setTimeout(() => {
          const mockMessages = [
            {
              id: '1',
              content: `Hello! I noticed your listing for the ${resourceTitle}. Is it still available for borrowing? I've been looking for something like this for an upcoming project.`,
              userName: 'Sophia Chen',
              createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000), // 2 days ago
              isOwner: false
            },
            {
              id: '2',
              content: `Hi Sophia! Yes, the ${resourceTitle} is still available. I'd be happy to lend it out. When were you thinking of using it, and for how long would you need it?`,
              userName: ownerName,
              createdAt: new Date(Date.now() - 47 * 60 * 60 * 1000), // 1 hour after first message
              isOwner: true
            },
            {
              id: '3',
              content: `That's great! I'm planning to start my project this weekend and would need it for about a week. Would that work with your schedule? I live in the Riverside neighborhood, so I could pick it up whenever is convenient for you.`,
              userName: 'Sophia Chen',
              createdAt: new Date(Date.now() - 36 * 60 * 60 * 1000), // 11 hours after previous message
              isOwner: false
            },
            {
              id: '4',
              content: `A week works perfectly. I'm actually in Northside, but I commute to Riverside for work. I could bring it with me this Friday if that works for you? We could meet around 5:30pm at the community center.`,
              userName: ownerName,
              createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 12 hours after previous message
              isOwner: true
            },
            {
              id: '5',
              content: `Friday at 5:30pm at the community center sounds perfect! I really appreciate your flexibility. I'll make sure to take good care of it and return it in the same condition. Looking forward to meeting you!`,
              userName: 'Sophia Chen',
              createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours after previous message
              isOwner: false
            }
          ];

          setMessages(mockMessages);
          setLoading(false);
        }, 1000);
      } catch (err) {
        console.error('Error fetching messages:', err);
        setError('Failed to load messages. Please try again later.');
        setLoading(false);
      }
    };

    if (resourceId) {
      fetchMessages();

      // Join the resource room to receive updates
      socketService.joinResourceRoom(resourceId);

      // Listen for new messages
      const unsubscribe = socketService.on('resource-message', handleNewMessage);

      // Clean up when component unmounts
      return () => {
        unsubscribe();
      };
    }
  }, [resourceId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle new messages from socket
  const handleNewMessage = (data) => {
    if (data.resourceId === resourceId) {
      setMessages(prev => [...prev, data.message]);
    }
  };

  // Send a new message
  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (!newMessage.trim() || !currentUser) return;

    try {
      setSending(true);

      // In a real app, this would be an API call
      // await api.post(`/resources/${resourceId}/messages`, {
      //   content: newMessage
      // });

      // For demo purposes, use socket service to simulate sending a message
      const isOwner = currentUser.id === ownerId;
      socketService.emitResourceMessage(resourceId, newMessage, isOwner);

      setNewMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (error) {
    return (
      <div className="text-center text-danger p-3">
        <p>{error}</p>
        <Button variant="outline-primary" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="resource-chat">
      <div className="chat-header">
        <h5 className="mb-0">Chat with {ownerName}</h5>
        <small className="text-muted">Discuss details, arrange pickup, etc.</small>
      </div>

      <div className="chat-messages">
        {loading ? (
          <div className="text-center p-4">
            <Spinner animation="border" variant="primary" size="sm" />
            <p className="mt-2">Loading messages...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center p-4 text-muted">
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`message ${message.isOwner ? 'owner' : 'user'}`}
            >
              <div className="message-content">
                <div className="message-header">
                  <span className="message-sender">
                    {message.isOwner ? `${ownerName} (Owner)` : message.userName}
                  </span>
                  <span className="message-time">{formatDate(message.createdAt)}</span>
                </div>
                <div className="message-body">{message.content}</div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      <Form onSubmit={handleSendMessage} className="chat-input">
        <Form.Group className="mb-0 d-flex">
          <Form.Control
            type="text"
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            disabled={!currentUser || sending}
          />
          <Button
            variant="primary"
            type="submit"
            disabled={!newMessage.trim() || !currentUser || sending}
            className="ms-2"
          >
            {sending ? (
              <Spinner animation="border" size="sm" />
            ) : (
              <FaPaperPlane />
            )}
          </Button>
        </Form.Group>
      </Form>

      {!currentUser && (
        <div className="text-center mt-2 text-danger">
          <small>Please log in to send messages</small>
        </div>
      )}
    </div>
  );
};

export default ResourceChat;
