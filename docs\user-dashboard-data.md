# User Dashboard with Realistic Data

This document explains how the user dashboard in Urban Pulse displays realistic data based on user activity.

## Overview

The user dashboard now displays real data from the user's activities across the platform, including:

- Issues reported
- Transport feedback submitted
- Challenges completed
- Reviews submitted
- Resources shared
- Events attended
- Courses completed

## Implementation Details

### Backend Components

1. **UserStats Model** (`backend/models/UserStats.js`)
   - Stores aggregated statistics for each user
   - Maintains detailed records of each activity
   - Includes timestamps for all activities

2. **User Stats Controller** (`backend/controllers/userStatsController.js`)
   - Provides API endpoints to fetch user statistics
   - Handles detailed activity data retrieval
   - Automatically creates stats for new users

3. **Stats Update Utility** (`backend/utils/updateUserStats.js`)
   - Updates user statistics when new activities are performed
   - Maintains consistency across different activity types
   - Called from various controllers (issues, resources, etc.)

4. **Reporting Service** (`backend/services/reportingService.js`)
   - Generates activity reports for specified date ranges
   - Calculates summary statistics
   - Provides trend analysis

5. **Report Controller** (`backend/controllers/reportController.js`)
   - Exposes API endpoints for generating reports
   - Handles date range filtering
   - Supports different report types (daily, weekly, monthly)

### Frontend Components

1. **Activity Details Component** (`frontend/src/components/dashboard/ActivityDetails.jsx`)
   - Displays detailed activity history
   - Organizes activities by type
   - Shows status and timestamps

2. **Activity Report Component** (`frontend/src/components/dashboard/ActivityReport.jsx`)
   - Allows users to generate custom reports
   - Displays activity trends with charts
   - Shows summary statistics

3. **Dashboard Integration** (`frontend/src/pages/UserDashboardPage.jsx`)
   - Integrates activity details and reports
   - Shows activity counts in a user-friendly format
   - Calculates user level based on activity

## API Endpoints

### User Stats

- `GET /api/user/stats` - Get aggregated user statistics
- `GET /api/user/activity/:type` - Get detailed activity data by type
  - Valid types: `issues`, `transport`, `challenges`, `reviews`, `resources`, `events`, `courses`, `all`

### Reports

- `GET /api/reports/activity` - Generate user activity report
  - Query parameters:
    - `reportType`: `daily`, `weekly`, `monthly`
    - `startDate`: Start date in ISO format (YYYY-MM-DD)
    - `endDate`: End date in ISO format (YYYY-MM-DD)

- `GET /api/reports/admin/user/:userId/activity` - Admin endpoint to generate report for any user

## Data Flow

1. User performs an activity (reports an issue, shares a resource, etc.)
2. The controller handling that activity calls `updateUserStats` utility
3. The utility updates the user's stats in the database
4. When the user visits their dashboard, the frontend fetches the latest stats
5. The dashboard components display the data in a user-friendly format

## Initialization

For existing users and data, run the initialization script:

```bash
node backend/scripts/initialize-user-stats.js
```

This script will:
1. Find all users without stats records
2. Count their activities across all collections
3. Create detailed activity records
4. Save the stats to the database

## Testing

To test the user dashboard with realistic data:

1. Create a test user account
2. Perform various activities (report issues, share resources, etc.)
3. Visit the dashboard to see the updated stats
4. Generate reports for different time periods

## Troubleshooting

If user stats are not updating:

1. Check the browser console for API errors
2. Verify that the `updateUserStats` utility is being called
3. Check the MongoDB database for the UserStats collection
4. Run the initialization script if needed

## Future Enhancements

- Add more detailed analytics
- Implement activity goals and achievements
- Add export functionality for reports
- Create admin dashboard for community-wide statistics
