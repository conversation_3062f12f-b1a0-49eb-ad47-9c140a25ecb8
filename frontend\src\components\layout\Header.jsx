import React, { useState, useEffect } from 'react';
import { Navbar, Container, Nav, NavDropdown, Button } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { FaRecycle } from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import NotificationDropdown from '../notifications/NotificationDropdown';
import useLocalStorageSync from '../../hooks/useLocalStorageSync';

const Header = () => {
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const [localUser, setLocalUser] = useState(null);

  // Check localStorage directly for user data
  useEffect(() => {
    const checkLocalStorage = () => {
      try {
        const userString = localStorage.getItem('user');
        const token = localStorage.getItem('token');

        console.log('Header checking localStorage - User data exists:', !!userString, 'Token exists:', !!token);

        if (userString && token) {
          const userData = JSON.parse(userString);
          console.log('Setting localUser from localStorage:', userData.name);
          setLocalUser(userData);
        } else {
          console.log('Clearing localUser - no valid data in localStorage');
          setLocalUser(null);
        }
      } catch (error) {
        console.error('Error checking localStorage:', error);
        setLocalUser(null);
      }
    };

    // Check on mount
    checkLocalStorage();

    // Also set up an interval to periodically check localStorage
    // This helps with cases where the event listeners might miss changes
    const intervalId = setInterval(checkLocalStorage, 2000);

    // Listen for custom auth-state-change events
    const handleAuthStateChange = (event) => {
      console.log('Header received auth-state-change event:', event.detail);
      if (event.detail && event.detail.isLoggedIn && event.detail.userData) {
        setLocalUser(event.detail.userData);
      }
    };

    window.addEventListener('auth-state-change', handleAuthStateChange);
    window.addEventListener('userLoggedIn', checkLocalStorage);

    // Clean up interval and event listeners on unmount
    return () => {
      clearInterval(intervalId);
      window.removeEventListener('auth-state-change', handleAuthStateChange);
      window.removeEventListener('userLoggedIn', checkLocalStorage);
    };
  }, []);

  // Use our custom hook to listen for localStorage changes
  useLocalStorageSync((e) => {
    console.log('Header detected storage change:', e.key);
    try {
      const userString = localStorage.getItem('user');
      if (userString) {
        const userData = JSON.parse(userString);
        setLocalUser(userData);
      } else {
        setLocalUser(null);
      }
    } catch (error) {
      console.error('Error handling storage change:', error);
      setLocalUser(null);
    }
  });

  // Use either the context user or localStorage user
  const user = currentUser || localUser;

  // Debug output to help diagnose authentication issues
  useEffect(() => {
    console.log('Header component auth state:', {
      currentUser: !!currentUser,
      localUser: !!localUser,
      user: !!user,
      userData: user ? { name: user.name, email: user.email } : null
    });
  }, [currentUser, localUser, user]);

  const handleLogout = async () => {
    try {
      await logout();
      setLocalUser(null);
      navigate('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  return (
    <Navbar bg="white" expand="lg" className="shadow-sm">
      <Container>
        <Navbar.Brand onClick={() => window.location.href = '/'} style={{ cursor: 'pointer' }}>
          <span className="fw-bold">Urban<span className="text-primary">Pulse</span></span>
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link onClick={() => window.location.href = '/'}>Home</Nav.Link>
            <NavDropdown title="Services" id="services-dropdown">
              <NavDropdown.Item onClick={() => window.location.href = '/issue-reporting'}>Issue Reporting</NavDropdown.Item>
              <NavDropdown.Item onClick={() => window.location.href = '/issues-map'}>Issues Map</NavDropdown.Item>
              <NavDropdown.Item onClick={() => window.location.href = '/transport-feedback'}>Transport Feedback</NavDropdown.Item>
              <NavDropdown.Item onClick={() => window.location.href = '/sustainable-living'}>Challenge Calendars</NavDropdown.Item>
              <NavDropdown.Item onClick={() => window.location.href = '/public-services'}>Public Services</NavDropdown.Item>
              <NavDropdown.Item onClick={() => window.location.href = '/resource-sharing'}>Resource Sharing</NavDropdown.Item>
              <NavDropdown.Item onClick={() => window.location.href = '/green-spaces'}>Carbon Footprint</NavDropdown.Item>
              <NavDropdown.Item onClick={() => window.location.href = '/learning-hub'}>Learning Hub</NavDropdown.Item>
            </NavDropdown>
            <Nav.Link onClick={() => window.location.href = '/ai-assistant'} className="d-flex align-items-center">
              <FaRecycle className="me-1" /> Reuse
            </Nav.Link>
          </Nav>
          <Nav>
            {user ? (
              <>
                {user.isAdmin && (
                  <Nav.Link as={Link} to="/admin">Admin Dashboard</Nav.Link>
                )}
                <Nav.Link as={Link} to="/dashboard">My Dashboard</Nav.Link>
                {/* Only render NotificationDropdown if it's available */}
                {typeof NotificationDropdown === 'function' && (
                  <Nav.Item className="d-flex align-items-center me-2">
                    <NotificationDropdown />
                  </Nav.Item>
                )}
                <NavDropdown title={user.name || 'Profile'} id="profile-dropdown">
                  <NavDropdown.Item as={Link} to="/profile">My Profile</NavDropdown.Item>
                  <NavDropdown.Divider />
                  <NavDropdown.Item onClick={handleLogout}>Logout</NavDropdown.Item>
                </NavDropdown>
              </>
            ) : (
              <>
                <Nav.Link onClick={() => window.location.href = '/public/direct-login.html'}>Login</Nav.Link>
                <Nav.Link onClick={() => window.location.href = '/admin-login'}>Admin Login</Nav.Link>
                <Button
                  onClick={() => window.location.href = '/public/register-redirect.html'}
                  variant="primary"
                  className="ms-2"
                >
                  Sign Up
                </Button>
              </>
            )}
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Header;
