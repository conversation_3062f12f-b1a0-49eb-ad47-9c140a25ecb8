const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');
  
  // Get the users collection
  const db = mongoose.connection.db;
  const usersCollection = db.collection('users');
  
  // Function to update a user's password
  async function updateUserPassword(email, password) {
    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Update the user
    const result = await usersCollection.updateOne(
      { email },
      { $set: { password: hashedPassword } }
    );
    
    console.log(`User ${email} updated: ${result.modifiedCount > 0 ? 'Yes' : 'No'}`);
    
    // Verify the password
    const user = await usersCollection.findOne({ email });
    if (user) {
      const isMatch = await bcrypt.compare(password, user.password);
      console.log(`Password verification for ${email}: ${isMatch ? 'Success ✅' : 'Failed ❌'}`);
    } else {
      console.log(`User ${email} not found`);
    }
  }
  
  // Update test users
  console.log('\nUpdating test users:');
  await updateUserPassword('<EMAIL>', 'password123');
  await updateUserPassword('<EMAIL>', 'password123');
  await updateUserPassword('<EMAIL>', 'password123');
  
  // Create missing users if needed
  console.log('\nChecking for missing users:');
  
  const testUsers = [
    { email: '<EMAIL>', name: 'Test User', password: 'password123', isAdmin: false },
    { email: '<EMAIL>', name: 'Demo User', password: 'password123', isAdmin: false },
    { email: '<EMAIL>', name: 'Admin User', password: 'password123', isAdmin: true }
  ];
  
  for (const userData of testUsers) {
    const user = await usersCollection.findOne({ email: userData.email });
    
    if (!user) {
      console.log(`User ${userData.email} not found, creating...`);
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);
      
      // Create the user
      await usersCollection.insertOne({
        name: userData.name,
        email: userData.email,
        password: hashedPassword,
        isAdmin: userData.isAdmin,
        createdAt: new Date()
      });
      
      console.log(`User ${userData.email} created successfully`);
    } else {
      console.log(`User ${userData.email} already exists`);
    }
  }
  
  // List all users
  console.log('\nUsers in database:');
  const users = await usersCollection.find({}).toArray();
  users.forEach(user => {
    console.log(`- ${user.name} (${user.email}) ${user.isAdmin ? '[Admin]' : ''}`);
  });
  
  // Close the connection
  await mongoose.connection.close();
  console.log('\nConnection closed');
  process.exit(0);
})
.catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
