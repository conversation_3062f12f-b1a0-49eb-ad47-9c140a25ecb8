const { generateUserActivityReport } = require('../services/reportingService');

// @desc    Generate user activity report
// @route   GET /api/reports/activity
// @access  Private
exports.getUserActivityReport = async (req, res) => {
  try {
    const userId = req.user._id;
    const { reportType, startDate, endDate } = req.query;
    
    // Parse dates if provided
    let parsedStartDate = startDate ? new Date(startDate) : null;
    let parsedEndDate = endDate ? new Date(endDate) : null;
    
    // Validate dates
    if (startDate && isNaN(parsedStartDate.getTime())) {
      return res.status(400).json({
        success: false,
        error: 'Invalid start date format'
      });
    }
    
    if (endDate && isNaN(parsedEndDate.getTime())) {
      return res.status(400).json({
        success: false,
        error: 'Invalid end date format'
      });
    }
    
    // Generate report
    const report = await generateUserActivityReport(
      userId,
      reportType,
      parsedStartDate,
      parsedEndDate
    );
    
    if (!report.success) {
      return res.status(400).json(report);
    }
    
    res.status(200).json(report);
  } catch (error) {
    console.error('Error generating user activity report:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

// @desc    Generate admin activity report for a specific user
// @route   GET /api/reports/admin/user/:userId/activity
// @access  Private/Admin
exports.getAdminUserActivityReport = async (req, res) => {
  try {
    const { userId } = req.params;
    const { reportType, startDate, endDate } = req.query;
    
    // Parse dates if provided
    let parsedStartDate = startDate ? new Date(startDate) : null;
    let parsedEndDate = endDate ? new Date(endDate) : null;
    
    // Validate dates
    if (startDate && isNaN(parsedStartDate.getTime())) {
      return res.status(400).json({
        success: false,
        error: 'Invalid start date format'
      });
    }
    
    if (endDate && isNaN(parsedEndDate.getTime())) {
      return res.status(400).json({
        success: false,
        error: 'Invalid end date format'
      });
    }
    
    // Generate report
    const report = await generateUserActivityReport(
      userId,
      reportType,
      parsedStartDate,
      parsedEndDate
    );
    
    if (!report.success) {
      return res.status(400).json(report);
    }
    
    res.status(200).json(report);
  } catch (error) {
    console.error('Error generating admin user activity report:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};
