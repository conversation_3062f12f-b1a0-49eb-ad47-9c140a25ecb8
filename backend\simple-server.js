const express = require('express');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
app.use(express.json());

// Connect to MongoDB
console.log('Connecting to MongoDB...');
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/urban-pulse', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
  
  // Start server
  const PORT = 5001;
  app.listen(PORT, () => {
    console.log(`Simple server running on port ${PORT}`);
  });
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});

// Root route
app.get('/', (req, res) => {
  res.json({ message: 'Simple server is running' });
});

// Login route
app.post('/api/auth/login', async (req, res) => {
  console.log('Login request received:', {
    body: { ...req.body, password: req.body.password ? '********' : undefined }
  });
  
  try {
    const { email, password } = req.body;
    
    // Validate input
    if (!email || !password) {
      console.log('Missing email or password');
      return res.status(400).json({ message: 'Please provide email and password' });
    }
    
    // Get users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Find user by email
    const user = await usersCollection.findOne({ email });
    
    if (!user) {
      console.log(`User not found: ${email}`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    console.log(`User found: ${user.name} (${user.email})`);
    
    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    
    if (!isMatch) {
      console.log('Password does not match');
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    console.log('Password matches, generating token');
    
    // Generate token
    const token = jwt.sign(
      { id: user._id, email: user.email, isAdmin: user.isAdmin || false },
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production',
      { expiresIn: '24h' }
    );
    
    console.log('Login successful');
    
    // Return success response
    res.json({
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin || false
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Server error during login' });
  }
});
