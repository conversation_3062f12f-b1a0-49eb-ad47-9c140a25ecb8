<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Injecting Authentication - Urban Pulse</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .inject-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .inject-icon {
            width: 80px;
            height: 80px;
            background-color: #0d6efd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }
        .inject-icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        .spinner {
            width: 3rem;
            height: 3rem;
            margin: 1.5rem auto;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            text-align: left;
            margin: 1rem 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="inject-container">
        <div class="inject-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
        </div>
        <h2 class="mb-3">Authentication Helper</h2>
        <p class="mb-4">This tool will help fix the navbar after login.</p>
        
        <div id="auth-status"></div>
        
        <div class="spinner-border text-primary spinner" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        
        <div id="manual-fix" style="display: none;">
            <h4 class="mt-4">Manual Fix Instructions</h4>
            <p>If the automatic fix doesn't work, you can try this manual approach:</p>
            <div class="code-block">
// Open your browser's developer console (F12)
// and paste this code:

const script = document.createElement('script');
script.src = '/public/force-navbar-update.js';
document.head.appendChild(script);</div>
            <button id="copy-code" class="btn btn-outline-primary mt-2">Copy Code</button>
        </div>
    </div>

    <script>
        // Function to check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('token');
            const userDataString = localStorage.getItem('user');
            
            if (token && userDataString) {
                try {
                    const userData = JSON.parse(userDataString);
                    document.getElementById('auth-status').innerHTML = `
                        <div class="alert alert-success">
                            Logged in as: <strong>${userData.name || 'User'}</strong>
                        </div>
                    `;
                    return true;
                } catch (error) {
                    document.getElementById('auth-status').innerHTML = `
                        <div class="alert alert-warning">
                            Authentication issue detected: ${error.message}
                        </div>
                    `;
                    return false;
                }
            } else {
                document.getElementById('auth-status').innerHTML = `
                    <div class="alert alert-danger">
                        Not logged in. Please <a href="/public/direct-login.html">login</a> first.
                    </div>
                `;
                return false;
            }
        }
        
        // Function to create and inject the script
        function injectScript() {
            // Create a script element
            const script = document.createElement('script');
            script.src = '/public/force-navbar-update.js';
            
            // Add it to the document
            document.head.appendChild(script);
            
            console.log('Injected force-navbar-update.js');
        }
        
        // Function to redirect to home with the script
        function redirectToHome() {
            const isLoggedIn = checkAuthStatus();
            
            if (isLoggedIn) {
                // Create a special URL with a parameter to trigger script injection
                const homeUrl = '/?inject_auth=' + new Date().getTime();
                
                // Show message
                document.getElementById('auth-status').innerHTML += `
                    <div class="alert alert-info mt-2">
                        Redirecting to home page with navbar fix...
                    </div>
                `;
                
                // Inject the script before redirecting
                injectScript();
                
                // Redirect after a delay
                setTimeout(() => {
                    // Store a flag in sessionStorage to indicate we should inject the script
                    sessionStorage.setItem('inject_auth_script', 'true');
                    
                    // Redirect to home
                    window.location.href = homeUrl;
                }, 2000);
            } else {
                // Hide spinner
                document.querySelector('.spinner').style.display = 'none';
                
                // Show manual fix instructions
                document.getElementById('manual-fix').style.display = 'block';
            }
        }
        
        // Copy code to clipboard
        document.getElementById('copy-code').addEventListener('click', function() {
            const code = document.querySelector('.code-block').textContent;
            navigator.clipboard.writeText(code).then(() => {
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = 'Copy Code';
                }, 2000);
            });
        });
        
        // Run the redirect function
        redirectToHome();
    </script>
</body>
</html>
