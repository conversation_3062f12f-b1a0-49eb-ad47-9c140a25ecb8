import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Button, ListGroup, Spinner, Badge } from 'react-bootstrap';
import { FaPaper<PERSON>lane, FaUser, FaRobot, FaTrash } from 'react-icons/fa';
import { formatDistanceToNow } from 'date-fns';
import geminiService from '../../services/geminiService';

const GeminiChatbot = ({ title = "Urban Pulse Reuse Assistant" }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef(null);

  // Initialize chat with a welcome message
  useEffect(() => {
    const initialMessage = {
      role: 'model',
      parts: [{ text: "Hello! I'm your Urban Pulse Reuse assistant. How can I help you with reusing waste materials today?" }],
      timestamp: new Date()
    };
    setMessages([initialMessage]);
    geminiService.initChat();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a new message
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!newMessage.trim() || loading) return;

    // Add user message to UI
    const userMessage = {
      role: 'user',
      parts: [{ text: newMessage }],
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setLoading(true);

    try {
      // Get response from Gemini
      const response = await geminiService.getResponse(newMessage);

      // Add AI response to UI
      const aiMessage = {
        role: 'model',
        parts: [{ text: response }],
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error getting response:', error);

      // Add error message
      const errorMessage = {
        role: 'model',
        parts: [{ text: "I'm sorry, I encountered an error. Please try again." }],
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  // Clear chat history
  const handleClearChat = () => {
    const initialMessage = {
      role: 'model',
      parts: [{ text: "Chat history cleared. How can I help you today?" }],
      timestamp: new Date()
    };
    setMessages([initialMessage]);
    geminiService.clearChatHistory();
  };

  return (
    <Card className="gemini-chatbot border-0 shadow-sm">
      <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
        <div className="d-flex align-items-center">
          <FaRobot className="me-2" />
          <h5 className="mb-0">{title}</h5>
        </div>
        <Button
          variant="outline-light"
          size="sm"
          onClick={handleClearChat}
          title="Clear chat history"
        >
          <FaTrash />
        </Button>
      </Card.Header>

      <ListGroup variant="flush" className="chat-messages" style={{ height: '400px', overflowY: 'auto' }}>
        {messages.map((msg, index) => (
          <ListGroup.Item
            key={index}
            className={`border-0 py-2 ${msg.role === 'user' ? 'bg-light' : ''} ${msg.isError ? 'text-danger' : ''}`}
          >
            <div className="d-flex">
              <div className="me-2">
                {msg.role === 'user' ? (
                  <div className="bg-primary bg-opacity-10 rounded-circle p-1">
                    <FaUser className="text-primary" size={18} />
                  </div>
                ) : (
                  <div className="bg-success bg-opacity-10 rounded-circle p-1">
                    <FaRobot className="text-success" size={18} />
                  </div>
                )}
              </div>
              <div className="flex-grow-1">
                <div className="d-flex justify-content-between align-items-center mb-1">
                  <strong>
                    {msg.role === 'user' ? 'You' : 'Reuse Assistant'}
                  </strong>
                  <small className="text-muted">
                    {formatDistanceToNow(new Date(msg.timestamp), { addSuffix: true })}
                  </small>
                </div>
                <div className="message-content">
                  {msg.parts[0].text.split('\n').map((line, i) => (
                    <p key={i} className="mb-1">{line}</p>
                  ))}
                </div>
              </div>
            </div>
          </ListGroup.Item>
        ))}
        {loading && (
          <ListGroup.Item className="border-0 py-2">
            <div className="d-flex">
              <div className="me-2">
                <div className="bg-success bg-opacity-10 rounded-circle p-1">
                  <FaRobot className="text-success" size={18} />
                </div>
              </div>
              <div className="flex-grow-1">
                <div className="d-flex align-items-center">
                  <Spinner animation="border" size="sm" variant="success" className="me-2" />
                  <span className="text-muted">AI is thinking...</span>
                </div>
              </div>
            </div>
          </ListGroup.Item>
        )}
        <div ref={messagesEndRef} />
      </ListGroup>

      <Card.Footer className="bg-white border-top">
        <Form onSubmit={handleSubmit}>
          <div className="d-flex">
            <Form.Control
              type="text"
              placeholder="Ask me about reusing waste materials..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={loading}
              className="border-end-0 rounded-end-0"
              autoFocus
            />
            <Button
              variant="primary"
              type="submit"
              className="rounded-start-0"
              disabled={loading || !newMessage.trim()}
            >
              {loading ? (
                <Spinner animation="border" size="sm" />
              ) : (
                <FaPaperPlane />
              )}
            </Button>
          </div>
        </Form>
      </Card.Footer>
    </Card>
  );
};

export default GeminiChatbot;
