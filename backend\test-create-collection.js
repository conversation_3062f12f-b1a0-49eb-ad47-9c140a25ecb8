const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('./models/User');

// Load environment variables
dotenv.config();

console.log('Testing collection creation...');
console.log(`Connecting to: ${process.env.MONGO_URI}`);

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');
  
  // Check if users collection exists
  const collections = await mongoose.connection.db.listCollections().toArray();
  const collectionNames = collections.map(c => c.name);
  console.log('Existing collections:', collectionNames);
  
  // Create a test user
  console.log('Creating a test user...');
  try {
    const testUser = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });
    
    await testUser.save();
    console.log('Test user created successfully!');
    
    // Check collections again after creating user
    const updatedCollections = await mongoose.connection.db.listCollections().toArray();
    const updatedCollectionNames = updatedCollections.map(c => c.name);
    console.log('Collections after creating user:', updatedCollectionNames);
    
    // Find the user to verify
    const foundUser = await User.findOne({ email: '<EMAIL>' });
    console.log('Found user:', foundUser ? `${foundUser.name} (${foundUser.email})` : 'No user found');
    
  } catch (error) {
    console.error('Error creating test user:', error.message);
  }
  
  // Close the connection
  await mongoose.connection.close();
  console.log('Connection closed');
  process.exit(0);
})
.catch(error => {
  console.error('MongoDB connection error:', error.message);
  process.exit(1);
});
