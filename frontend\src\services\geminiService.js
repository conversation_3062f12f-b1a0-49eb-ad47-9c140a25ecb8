import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the Gemini API with your API key
// In a production environment, this should be stored in an environment variable
const API_KEY = "AIzaSyBj8kQdSmIj6vrhCvbwBR-Fbrmtt8InsgA"; // Replace with your actual API key
const genAI = new GoogleGenerativeAI(API_KEY);

// Get the Gemini Pro model
const model = genAI.getGenerativeModel({ model: "gemini-pro" });

// History to maintain conversation context
let chatHistory = [];

const geminiService = {
  // Initialize a new chat session
  initChat: () => {
    chatHistory = [];
    return chatHistory;
  },

  // Get a response from Gemini
  async getResponse(userMessage) {
    try {
      // Add user message to history
      chatHistory.push({
        role: 'user',
        parts: [{ text: userMessage }],
        timestamp: new Date()
      });

      // Create a chat session
      const chat = model.startChat({
        history: chatHistory.map(msg => ({
          role: msg.role,
          parts: msg.parts.map(part => part.text)
        })),
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1000,
        },
      });

      // Generate a response
      const result = await chat.sendMessage(userMessage);
      const response = result.response.text();

      // Add AI response to history
      chatHistory.push({
        role: 'model',
        parts: [{ text: response }],
        timestamp: new Date()
      });

      return response;
    } catch (error) {
      console.error('Error getting response from Gemini:', error);

      // Instead of returning an error message, use the fallback mechanism
      // by throwing the error so it can be caught by the calling function
      throw new Error('Failed to generate content from Gemini API');
    }
  },

  // Get the chat history
  getChatHistory: () => {
    return chatHistory;
  },

  // Clear the chat history
  clearChatHistory: () => {
    chatHistory = [];
    return chatHistory;
  }
};

export default geminiService;
