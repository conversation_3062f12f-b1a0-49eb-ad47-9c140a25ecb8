import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Tab, Nav, Badge, Modal } from 'react-bootstrap';
import { FaStar, FaRegStar, FaSearch, FaFilter, FaThumbsUp, FaComment, FaChartBar } from 'react-icons/fa';

const PublicServiceReviewPage = () => {
  const [selectedService, setSelectedService] = useState('');
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [reviewTitle, setReviewTitle] = useState('');
  const [reviewText, setReviewText] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedReview, setSelectedReview] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');

  // Mock data for service categories
  const serviceCategories = [
    { id: 'waste', name: 'Waste Management' },
    { id: 'water', name: 'Water Supply' },
    { id: 'parks', name: 'Parks & Recreation' },
    { id: 'roads', name: 'Roads & Infrastructure' },
    { id: 'transport', name: 'Public Transportation' },
    { id: 'health', name: 'Public Health Services' },
    { id: 'education', name: 'Education' },
    { id: 'safety', name: 'Public Safety' }
  ];

  // Realistic data for services
  const services = [
    { id: 1, name: 'Residential Waste Collection', category: 'waste', avgRating: 3.8, reviewCount: 127 },
    { id: 2, name: 'Municipal Recycling Program', category: 'waste', avgRating: 4.2, reviewCount: 98 },
    { id: 3, name: 'Drinking Water Quality', category: 'water', avgRating: 3.5, reviewCount: 143 },
    { id: 4, name: 'Urban Parks Maintenance', category: 'parks', avgRating: 4.0, reviewCount: 156 },
    { id: 5, name: 'Street & Pothole Repair', category: 'roads', avgRating: 2.8, reviewCount: 211 },
    { id: 6, name: 'Public Transit System', category: 'transport', avgRating: 3.2, reviewCount: 245 },
    { id: 7, name: 'Community Health Centers', category: 'health', avgRating: 3.9, reviewCount: 118 },
    { id: 8, name: 'Public School System', category: 'education', avgRating: 3.6, reviewCount: 172 },
    { id: 9, name: 'Emergency Services Response', category: 'safety', avgRating: 3.7, reviewCount: 189 },
    { id: 10, name: 'Stormwater Management', category: 'water', avgRating: 3.3, reviewCount: 87 },
    { id: 11, name: 'Bike Lane Infrastructure', category: 'transport', avgRating: 3.4, reviewCount: 112 },
    { id: 12, name: 'Public Library Services', category: 'education', avgRating: 4.5, reviewCount: 134 }
  ];

  // Realistic data for recent reviews with shorter text
  const recentReviews = [
    {
      id: 1,
      serviceId: 4,
      serviceName: 'Urban Parks Maintenance',
      title: 'Significant improvements at Riverside Memorial Park',
      text: "Great improvements to the park. New playground equipment is excellent and safe for all children. Walking paths have been resurfaced nicely.",
      rating: 5,
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      author: 'Marcus Johnson',
      helpful: 27,
      comments: 5
    },
    {
      id: 2,
      serviceId: 6,
      serviceName: 'Public Transit System',
      title: 'Reliability issues on Route 42 during peak hours',
      text: "Route 42 buses are consistently 12-18 minutes late during morning rush hour. The app shows incorrect arrival times. Weekend service is better.",
      rating: 2,
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      author: 'Sophia Chen',
      helpful: 43,
      comments: 8
    },
    {
      id: 3,
      serviceId: 2,
      serviceName: 'Municipal Recycling Program',
      title: 'Well-implemented recycling system',
      text: "The new recycling system is excellent. Color-coded bins make sorting easy. The recycling guide and website are very helpful resources.",
      rating: 4,
      date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      author: 'Priya Patel',
      helpful: 19,
      comments: 3
    },
    {
      id: 4,
      serviceId: 5,
      serviceName: 'Street & Pothole Repair',
      title: 'Unacceptable delay in addressing hazardous road conditions',
      text: "Reported dangerous potholes two months ago with no repair. These pose safety risks to drivers and pedestrians. Poor communication from department.",
      rating: 1,
      date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      author: 'Jamal Washington',
      helpful: 56,
      comments: 12
    },
    {
      id: 5,
      serviceId: 12,
      serviceName: 'Public Library Services',
      title: 'Exceptional programming and responsive staff',
      text: "The Central Library offers excellent programs for all ages. Staff are helpful and knowledgeable. Digital resources and reservation system work perfectly.",
      rating: 5,
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      author: 'Elena Rodriguez',
      helpful: 31,
      comments: 7
    }
  ];

  // Realistic data for service statistics
  const serviceStats = {
    totalReviews: 1792,
    avgRating: 3.6,
    topRated: 'Public Library Services',
    needsImprovement: 'Street & Pothole Repair',
    mostReviewed: 'Public Transit System',
    improvementTrend: '+0.3 points over 6 months',
    participationRate: '23% of residents have submitted at least one review',
    responseRate: '78% of reviews receive official response within 14 days'
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!selectedService || rating === 0 || !reviewTitle || !reviewText) {
      return setError('Please fill in all required fields and provide a rating');
    }

    setError('');
    setSuccess('');
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setSuccess('Your review has been submitted successfully! Thank you for your feedback.');
      setLoading(false);
      setSelectedService('');
      setRating(0);
      setReviewTitle('');
      setReviewText('');
    }, 1500);
  };

  const handleViewReview = (review) => {
    setSelectedReview(review);
    setShowModal(true);
  };

  const handleMarkHelpful = (reviewId) => {
    // In a real app, this would make an API call to mark the review as helpful
    console.log(`Marked review ${reviewId} as helpful`);
  };

  const renderStars = (count, interactive = false) => {
    const stars = [];

    for (let i = 1; i <= 5; i++) {
      if (interactive) {
        stars.push(
          <span
            key={i}
            className="star-rating"
            onMouseEnter={() => setHoverRating(i)}
            onMouseLeave={() => setHoverRating(0)}
            onClick={() => setRating(i)}
            style={{ cursor: 'pointer' }}
          >
            {i <= (hoverRating || rating) ? <FaStar /> : <FaRegStar />}
          </span>
        );
      } else {
        stars.push(
          <span key={i} className="star-rating">
            {i <= count ? <FaStar /> : <FaRegStar />}
          </span>
        );
      }
    }

    return stars;
  };

  const filteredServices = services.filter(service =>
    (filterCategory === '' || service.category === filterCategory) &&
    (searchTerm === '' || service.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <Container>
      <Row className="align-items-center mb-4">
        <Col>
          <h1>Public Service Reviews</h1>
          <p className="lead">
            Rate and review public services to help improve transparency and accountability in our city.
          </p>
        </Col>
        <Col xs="auto">
          <div className="bg-warning bg-opacity-10 p-3 rounded">
            <FaStar className="text-warning fs-1" />
          </div>
        </Col>
      </Row>

      <Tab.Container defaultActiveKey="write">
        <Row>
          <Col lg={3} className="mb-4">
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-0">
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="write" className="rounded-0 px-4 py-3">
                      <FaStar className="me-2" />
                      Write a Review
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="browse" className="rounded-0 px-4 py-3">
                      <FaSearch className="me-2" />
                      Browse Services
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="recent" className="rounded-0 px-4 py-3">
                      <FaComment className="me-2" />
                      Recent Reviews
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="stats" className="rounded-0 px-4 py-3">
                      <FaChartBar className="me-2" />
                      Service Statistics
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </Card.Body>
            </Card>

            <Card className="border-0 shadow-sm mt-4">
              <Card.Body className="p-4">
                <h5 className="mb-3">Service Categories</h5>
                <Nav className="flex-column">
                  {serviceCategories.map((category) => (
                    <Nav.Link
                      key={category.id}
                      className="px-0 py-2 text-dark"
                      onClick={() => setFilterCategory(category.id)}
                    >
                      {category.name}
                    </Nav.Link>
                  ))}
                </Nav>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={9}>
            <Tab.Content>
              <Tab.Pane eventKey="write">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">Write a Review</h4>

                    {error && <Alert variant="danger">{error}</Alert>}
                    {success && <Alert variant="success">{success}</Alert>}

                    <Form onSubmit={handleSubmit}>
                      <Form.Group className="mb-3" controlId="serviceSelect">
                        <Form.Label>Select a Service*</Form.Label>
                        <Form.Select
                          value={selectedService}
                          onChange={(e) => setSelectedService(e.target.value)}
                          required
                        >
                          <option value="">Choose a service to review</option>
                          {services.map((service) => (
                            <option key={service.id} value={service.id}>
                              {service.name}
                            </option>
                          ))}
                        </Form.Select>
                      </Form.Group>

                      <Form.Group className="mb-3" controlId="rating">
                        <Form.Label>Your Rating*</Form.Label>
                        <div className="mb-2 fs-4">
                          {renderStars(rating, true)}
                        </div>
                      </Form.Group>

                      <Form.Group className="mb-3" controlId="reviewTitle">
                        <Form.Label>Review Title*</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Summarize your experience"
                          value={reviewTitle}
                          onChange={(e) => setReviewTitle(e.target.value)}
                          required
                        />
                      </Form.Group>

                      <Form.Group className="mb-4" controlId="reviewText">
                        <Form.Label>Your Review*</Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={5}
                          placeholder="Share your experience with this service..."
                          value={reviewText}
                          onChange={(e) => setReviewText(e.target.value)}
                          required
                        />
                      </Form.Group>

                      <div className="d-grid">
                        <Button
                          variant="primary"
                          type="submit"
                          disabled={loading}
                          size="lg"
                        >
                          {loading ? 'Submitting...' : 'Submit Review'}
                        </Button>
                      </div>
                    </Form>
                  </Card.Body>
                </Card>
              </Tab.Pane>

              <Tab.Pane eventKey="browse">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h4 className="mb-0">Browse Services</h4>
                      <div className="d-flex">
                        <div className="input-group me-2">
                          <span className="input-group-text">
                            <FaSearch />
                          </span>
                          <Form.Control
                            type="text"
                            placeholder="Search services..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                        </div>
                        <Button variant="outline-secondary">
                          <FaFilter />
                        </Button>
                      </div>
                    </div>

                    {filterCategory && (
                      <div className="mb-3">
                        <Badge
                          bg="primary"
                          className="py-2 px-3"
                          style={{ cursor: 'pointer' }}
                          onClick={() => setFilterCategory('')}
                        >
                          {serviceCategories.find(c => c.id === filterCategory)?.name} ×
                        </Badge>
                      </div>
                    )}

                    <Row xs={1} md={2} className="g-4">
                      {filteredServices.map((service) => (
                        <Col key={service.id}>
                          <Card className="h-100 border-0 shadow-sm">
                            <Card.Body>
                              <div className="d-flex justify-content-between align-items-start mb-2">
                                <Badge bg="secondary" className="mb-2">
                                  {serviceCategories.find(c => c.id === service.category)?.name}
                                </Badge>
                                <div className="text-warning">
                                  {renderStars(Math.round(service.avgRating))}
                                </div>
                              </div>
                              <Card.Title>{service.name}</Card.Title>
                              <Card.Text className="text-muted small">
                                Average Rating: {service.avgRating.toFixed(1)}/5 ({service.reviewCount} reviews)
                              </Card.Text>
                              <div className="d-flex justify-content-between align-items-center mt-3">
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedService(service.id.toString());
                                    document.querySelector('a[href="#write"]').click();
                                  }}
                                >
                                  Write a Review
                                </Button>
                                <Button
                                  variant="link"
                                  className="text-decoration-none"
                                >
                                  Read Reviews
                                </Button>
                              </div>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>

                    {filteredServices.length === 0 && (
                      <div className="text-center py-5">
                        <p className="text-muted">No services found matching your criteria.</p>
                        <Button
                          variant="outline-primary"
                          onClick={() => {
                            setSearchTerm('');
                            setFilterCategory('');
                          }}
                        >
                          Clear Filters
                        </Button>
                      </div>
                    )}
                  </Card.Body>
                </Card>
              </Tab.Pane>

              <Tab.Pane eventKey="recent">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">Recent Reviews</h4>

                    {recentReviews.map((review) => (
                      <Card key={review.id} className="mb-4 border-0 shadow-sm">
                        <Card.Body>
                          <div className="d-flex justify-content-between align-items-start mb-2">
                            <div>
                              <Badge bg="secondary" className="mb-2">
                                {review.serviceName}
                              </Badge>
                            </div>
                            <div className="text-warning">
                              {renderStars(review.rating)}
                            </div>
                          </div>
                          <h5>{review.title}</h5>
                          <p className="text-muted small mb-3">
                            By {review.author} on {review.date}
                          </p>
                          <p className="mb-3">
                            {review.text.length > 200
                              ? `${review.text.substring(0, 200)}...`
                              : review.text
                            }
                          </p>
                          <div className="d-flex justify-content-between align-items-center">
                            <div>
                              <Button
                                variant="outline-secondary"
                                size="sm"
                                className="me-2"
                                onClick={() => handleMarkHelpful(review.id)}
                              >
                                <FaThumbsUp className="me-1" />
                                Helpful ({review.helpful})
                              </Button>
                              <Button
                                variant="outline-secondary"
                                size="sm"
                              >
                                <FaComment className="me-1" />
                                Comment ({review.comments})
                              </Button>
                            </div>
                            <Button
                              variant="link"
                              className="text-decoration-none"
                              onClick={() => handleViewReview(review)}
                            >
                              Read More
                            </Button>
                          </div>
                        </Card.Body>
                      </Card>
                    ))}

                    <div className="text-center mt-4">
                      <Button variant="outline-primary">
                        Load More Reviews
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Tab.Pane>

              <Tab.Pane eventKey="stats">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-4">
                    <h4 className="mb-4">Service Statistics</h4>

                    <Row className="g-4 mb-4">
                      <Col md={4}>
                        <Card className="border-0 bg-light h-100">
                          <Card.Body className="text-center p-4">
                            <h6 className="text-muted mb-2">Total Reviews</h6>
                            <h2 className="mb-0">{serviceStats.totalReviews}</h2>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col md={4}>
                        <Card className="border-0 bg-light h-100">
                          <Card.Body className="text-center p-4">
                            <h6 className="text-muted mb-2">Average Rating</h6>
                            <h2 className="mb-0">{serviceStats.avgRating.toFixed(1)}/5</h2>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col md={4}>
                        <Card className="border-0 bg-light h-100">
                          <Card.Body className="text-center p-4">
                            <h6 className="text-muted mb-2">Most Reviewed</h6>
                            <h5 className="mb-0">{serviceStats.mostReviewed}</h5>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>

                    <h5 className="mb-3">Rating Distribution</h5>
                    <div className="mb-4">
                      <div className="d-flex align-items-center mb-2">
                        <div className="me-2" style={{ width: '60px' }}>5 stars</div>
                        <div className="flex-grow-1 me-2">
                          <div className="progress" style={{ height: '20px' }}>
                            <div
                              className="progress-bar bg-success"
                              role="progressbar"
                              style={{ width: '45%' }}
                              aria-valuenow="45"
                              aria-valuemin="0"
                              aria-valuemax="100"
                            ></div>
                          </div>
                        </div>
                        <div style={{ width: '40px' }}>45%</div>
                      </div>
                      <div className="d-flex align-items-center mb-2">
                        <div className="me-2" style={{ width: '60px' }}>4 stars</div>
                        <div className="flex-grow-1 me-2">
                          <div className="progress" style={{ height: '20px' }}>
                            <div
                              className="progress-bar bg-primary"
                              role="progressbar"
                              style={{ width: '30%' }}
                              aria-valuenow="30"
                              aria-valuemin="0"
                              aria-valuemax="100"
                            ></div>
                          </div>
                        </div>
                        <div style={{ width: '40px' }}>30%</div>
                      </div>
                      <div className="d-flex align-items-center mb-2">
                        <div className="me-2" style={{ width: '60px' }}>3 stars</div>
                        <div className="flex-grow-1 me-2">
                          <div className="progress" style={{ height: '20px' }}>
                            <div
                              className="progress-bar bg-info"
                              role="progressbar"
                              style={{ width: '15%' }}
                              aria-valuenow="15"
                              aria-valuemin="0"
                              aria-valuemax="100"
                            ></div>
                          </div>
                        </div>
                        <div style={{ width: '40px' }}>15%</div>
                      </div>
                      <div className="d-flex align-items-center mb-2">
                        <div className="me-2" style={{ width: '60px' }}>2 stars</div>
                        <div className="flex-grow-1 me-2">
                          <div className="progress" style={{ height: '20px' }}>
                            <div
                              className="progress-bar bg-warning"
                              role="progressbar"
                              style={{ width: '7%' }}
                              aria-valuenow="7"
                              aria-valuemin="0"
                              aria-valuemax="100"
                            ></div>
                          </div>
                        </div>
                        <div style={{ width: '40px' }}>7%</div>
                      </div>
                      <div className="d-flex align-items-center">
                        <div className="me-2" style={{ width: '60px' }}>1 star</div>
                        <div className="flex-grow-1 me-2">
                          <div className="progress" style={{ height: '20px' }}>
                            <div
                              className="progress-bar bg-danger"
                              role="progressbar"
                              style={{ width: '3%' }}
                              aria-valuenow="3"
                              aria-valuemin="0"
                              aria-valuemax="100"
                            ></div>
                          </div>
                        </div>
                        <div style={{ width: '40px' }}>3%</div>
                      </div>
                    </div>

                    <Row className="g-4">
                      <Col md={6}>
                        <Card className="border-0 shadow-sm h-100">
                          <Card.Body className="p-4">
                            <h5 className="mb-3">Top Rated Services</h5>
                            <ol className="ps-3">
                              <li className="mb-2">Recycling Program (4.2/5)</li>
                              <li className="mb-2">City Park Maintenance (4.0/5)</li>
                              <li className="mb-2">Public Health Clinics (3.9/5)</li>
                              <li className="mb-2">Weekly Garbage Collection (3.8/5)</li>
                              <li>Police Response (3.7/5)</li>
                            </ol>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col md={6}>
                        <Card className="border-0 shadow-sm h-100">
                          <Card.Body className="p-4">
                            <h5 className="mb-3">Needs Improvement</h5>
                            <ol className="ps-3">
                              <li className="mb-2">Road Repair Response (2.8/5)</li>
                              <li className="mb-2">Bus Service (3.2/5)</li>
                              <li className="mb-2">Water Quality (3.5/5)</li>
                              <li className="mb-2">Public Schools (3.6/5)</li>
                              <li>Police Response (3.7/5)</li>
                            </ol>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Review Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Review Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedReview && (
            <>
              <div className="d-flex justify-content-between align-items-start mb-2">
                <Badge bg="secondary" className="mb-2">
                  {selectedReview.serviceName}
                </Badge>
                <div className="text-warning">
                  {renderStars(selectedReview.rating)}
                </div>
              </div>
              <h5>{selectedReview.title}</h5>
              <p className="text-muted small mb-3">
                By {selectedReview.author} on {selectedReview.date}
              </p>
              <p className="mb-4">{selectedReview.text}</p>

              <div className="d-flex justify-content-between align-items-center mb-3">
                <h6 className="mb-0">Comments ({selectedReview.comments})</h6>
                <Button variant="outline-primary" size="sm">
                  <FaComment className="me-1" />
                  Add Comment
                </Button>
              </div>

              <div className="border-top pt-3">
                <div className="mb-3">
                  <div className="d-flex justify-content-between">
                    <strong>City Official</strong>
                    <small className="text-muted">2023-04-16</small>
                  </div>
                  <p className="mb-0">Thank you for your feedback. We have noted your concerns and will address them promptly.</p>
                </div>

                <div className="mb-3">
                  <div className="d-flex justify-content-between">
                    <strong>Jane Smith</strong>
                    <small className="text-muted">2023-04-15</small>
                  </div>
                  <p className="mb-0">I've had a similar experience. This definitely needs attention.</p>
                </div>
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          <Button variant="primary">
            <FaThumbsUp className="me-1" />
            Mark as Helpful
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default PublicServiceReviewPage;
